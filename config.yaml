# Configuration file for Topic Modeling Pipeline

# News API Configuration
news_api:
  # Get your API key from https://newsapi.org/
  api_key: "YOUR_NEWS_API_KEY_HERE"
  base_url: "https://newsapi.org/v2/everything"
  
  # Search parameters
  query_keywords: ["India", "election", "economy", "technology", "politics"]
  language: "en"
  sort_by: "publishedAt"  # publishedAt, relevancy, popularity
  page_size: 100
  max_pages: 5

# Alternative RSS feeds (if NewsAPI limit reached)
rss_feeds:
  - "https://feeds.bbci.co.uk/news/rss.xml"
  - "https://rss.cnn.com/rss/edition.rss"
  - "https://feeds.reuters.com/reuters/topNews"

# Text Preprocessing
preprocessing:
  min_word_length: 3
  max_word_length: 20
  remove_stopwords: true
  lemmatize: true
  remove_numbers: true
  remove_punctuation: true
  
  # Custom stopwords to add
  custom_stopwords: ["said", "says", "according", "reuters", "news", "report"]

# Topic Modeling (LDA)
topic_modeling:
  num_topics: 8
  max_features: 1000  # Maximum number of features for CountVectorizer
  min_df: 2  # Minimum document frequency
  max_df: 0.8  # Maximum document frequency
  ngram_range: [1, 2]  # Unigrams and bigrams
  
  # LDA parameters
  random_state: 42
  max_iter: 100
  learning_method: "batch"

# Output Configuration
output:
  data_dir: "data"
  raw_articles_file: "raw_articles.csv"
  processed_articles_file: "processed_articles.csv"
  topic_trends_file: "topic_trends.csv"
  model_dir: "models"
  
  # Power BI export
  powerbi_export_file: "powerbi_data.csv"

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  log_file: "logs/pipeline.log"
  max_log_size: "10MB"
  backup_count: 5

# Scheduling
scheduling:
  enabled: false
  frequency: "daily"  # hourly, daily, weekly
  time: "09:00"  # For daily scheduling
