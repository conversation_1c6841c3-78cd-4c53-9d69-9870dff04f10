"""
Utility functions for the topic modeling pipeline.
"""

import os
import logging
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger as loguru_logger

from .config import config

def setup_logging():
    """Set up logging configuration."""
    log_config = config.logging
    log_level = log_config.get('level', 'INFO')
    log_file = log_config.get('log_file', 'logs/pipeline.log')
    
    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(exist_ok=True)
    
    # Configure loguru
    loguru_logger.remove()  # Remove default handler
    
    # Add file handler
    loguru_logger.add(
        log_file,
        level=log_level,
        rotation=log_config.get('max_log_size', '10MB'),
        retention=log_config.get('backup_count', 5),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    )
    
    # Add console handler
    loguru_logger.add(
        lambda msg: print(msg, end=''),
        level=log_level,
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>"
    )

def ensure_directories():
    """Ensure all required directories exist."""
    directories = [
        config.get('output.data_dir', 'data'),
        config.get('output.model_dir', 'models'),
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

def load_articles(file_path: str) -> pd.DataFrame:
    """
    Load articles from CSV file.
    
    Args:
        file_path: Path to CSV file
        
    Returns:
        DataFrame with articles
    """
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        loguru_logger.info(f"Loaded {len(df)} articles from {file_path}")
        return df
    except FileNotFoundError:
        loguru_logger.error(f"File not found: {file_path}")
        return pd.DataFrame()
    except Exception as e:
        loguru_logger.error(f"Error loading articles from {file_path}: {e}")
        return pd.DataFrame()

def save_dataframe(df: pd.DataFrame, file_path: str, add_timestamp: bool = True) -> str:
    """
    Save DataFrame to CSV file.
    
    Args:
        df: DataFrame to save
        file_path: Output file path
        add_timestamp: Whether to create a timestamped backup
        
    Returns:
        Path to saved file
    """
    try:
        # Ensure directory exists
        Path(file_path).parent.mkdir(exist_ok=True)
        
        # Save main file
        df.to_csv(file_path, index=False, encoding='utf-8')
        loguru_logger.info(f"Saved {len(df)} rows to {file_path}")
        
        # Create timestamped backup if requested
        if add_timestamp:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            path_obj = Path(file_path)
            backup_path = path_obj.parent / f"{path_obj.stem}_{timestamp}{path_obj.suffix}"
            df.to_csv(backup_path, index=False, encoding='utf-8')
            loguru_logger.info(f"Backup saved to {backup_path}")
        
        return file_path
        
    except Exception as e:
        loguru_logger.error(f"Error saving DataFrame to {file_path}: {e}")
        raise

def get_latest_file(directory: str, pattern: str = "*.csv") -> Optional[str]:
    """
    Get the most recently modified file matching pattern in directory.
    
    Args:
        directory: Directory to search
        pattern: File pattern to match
        
    Returns:
        Path to latest file or None if no files found
    """
    try:
        directory_path = Path(directory)
        if not directory_path.exists():
            return None
        
        files = list(directory_path.glob(pattern))
        if not files:
            return None
        
        latest_file = max(files, key=lambda f: f.stat().st_mtime)
        return str(latest_file)
        
    except Exception as e:
        loguru_logger.error(f"Error finding latest file in {directory}: {e}")
        return None

def clean_text_basic(text: str) -> str:
    """
    Basic text cleaning for display purposes.
    
    Args:
        text: Input text
        
    Returns:
        Cleaned text
    """
    if not isinstance(text, str):
        return ""
    
    # Remove extra whitespace
    text = " ".join(text.split())
    
    # Remove common HTML entities
    html_entities = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'",
        '&nbsp;': ' '
    }
    
    for entity, replacement in html_entities.items():
        text = text.replace(entity, replacement)
    
    return text.strip()

def validate_dataframe(df: pd.DataFrame, required_columns: List[str]) -> bool:
    """
    Validate that DataFrame has required columns and data.
    
    Args:
        df: DataFrame to validate
        required_columns: List of required column names
        
    Returns:
        True if valid, False otherwise
    """
    if df.empty:
        loguru_logger.error("DataFrame is empty")
        return False
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        loguru_logger.error(f"Missing required columns: {missing_columns}")
        return False
    
    # Check for null values in required columns
    for col in required_columns:
        null_count = df[col].isnull().sum()
        if null_count > 0:
            loguru_logger.warning(f"Column '{col}' has {null_count} null values")
    
    return True

def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a file.
    
    Args:
        file_path: Path to file
        
    Returns:
        Dictionary with file information
    """
    try:
        path_obj = Path(file_path)
        if not path_obj.exists():
            return {"exists": False}
        
        stat = path_obj.stat()
        return {
            "exists": True,
            "size_bytes": stat.st_size,
            "size_mb": round(stat.st_size / (1024 * 1024), 2),
            "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "created": datetime.fromtimestamp(stat.st_ctime).isoformat()
        }
        
    except Exception as e:
        loguru_logger.error(f"Error getting file info for {file_path}: {e}")
        return {"exists": False, "error": str(e)}

def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

def create_summary_stats(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Create summary statistics for articles DataFrame.
    
    Args:
        df: Articles DataFrame
        
    Returns:
        Dictionary with summary statistics
    """
    if df.empty:
        return {"total_articles": 0}
    
    stats = {
        "total_articles": len(df),
        "date_range": {},
        "sources": {},
        "text_stats": {}
    }
    
    # Date range
    if 'publishedAt' in df.columns:
        df['publishedAt'] = pd.to_datetime(df['publishedAt'], errors='coerce')
        valid_dates = df['publishedAt'].dropna()
        if not valid_dates.empty:
            stats["date_range"] = {
                "earliest": valid_dates.min().isoformat(),
                "latest": valid_dates.max().isoformat(),
                "span_days": (valid_dates.max() - valid_dates.min()).days
            }
    
    # Source distribution
    if 'source_name' in df.columns:
        source_counts = df['source_name'].value_counts().head(10)
        stats["sources"] = source_counts.to_dict()
    
    # Text statistics
    text_columns = ['title', 'description', 'content']
    for col in text_columns:
        if col in df.columns:
            text_series = df[col].astype(str)
            stats["text_stats"][col] = {
                "avg_length": text_series.str.len().mean(),
                "max_length": text_series.str.len().max(),
                "min_length": text_series.str.len().min()
            }
    
    return stats
