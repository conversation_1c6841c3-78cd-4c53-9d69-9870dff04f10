#!/usr/bin/env python3
"""
Unified Database Connection Manager
Supports both SQL Server and MySQL for topic modeling pipeline
"""

import json
import logging
from datetime import datetime
from typing import Dict, Optional, Any
from contextlib import contextmanager
from pathlib import Path

# Database drivers
try:
    import pyodbc  # SQL Server
except ImportError:
    pyodbc = None

try:
    import pymysql  # MySQL
    pymysql.install_as_MySQLdb()
except ImportError:
    pymysql = None

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UnifiedSQLConnectionManager:
    """
    Unified connection manager supporting both SQL Server and MySQL.
    Automatically detects database type from configuration.
    """
    
    def __init__(self, 
                 server: str,
                 database: str,
                 username: str = None,
                 password: str = None,
                 driver: str = None,
                 port: int = None,
                 trusted_connection: bool = False,
                 pool_size: int = 10):
        """
        Initialize unified database connection manager.
        
        Args:
            server: Database server name or IP
            database: Database name
            username: Database username
            password: Database password
            driver: Database driver (auto-detected if None)
            port: Database port (auto-detected if None)
            trusted_connection: Use Windows auth (SQL Server only)
            pool_size: Connection pool size
        """
        
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.trusted_connection = trusted_connection
        self.pool_size = pool_size
        
        # Auto-detect database type and settings
        self.db_type = self._detect_database_type(driver)
        self.driver = driver or self._get_default_driver()
        self.port = port or self._get_default_port()
        
        # Initialize connection
        self.engine = None
        self.SessionLocal = None
        self._initialize_connection()
        
        logger.info(f"✅ {self.db_type.upper()} connection manager initialized for {server}/{database}")
    
    def _detect_database_type(self, driver: str = None) -> str:
        """Auto-detect database type from driver or server name."""
        
        if driver:
            if 'mysql' in driver.lower() or 'pymysql' in driver.lower():
                return 'mysql'
            elif 'odbc' in driver.lower() or 'sqlserver' in driver.lower():
                return 'sqlserver'
        
        # Default to MySQL for simplicity
        return 'mysql'
    
    def _get_default_driver(self) -> str:
        """Get default driver for detected database type."""
        
        if self.db_type == 'mysql':
            return 'mysql+pymysql'
        else:
            return 'ODBC Driver 17 for SQL Server'
    
    def _get_default_port(self) -> int:
        """Get default port for detected database type."""
        
        if self.db_type == 'mysql':
            return 3306
        else:
            return 1433
    
    def _build_connection_string(self) -> str:
        """Build connection string for the detected database type."""
        
        if self.db_type == 'mysql':
            # MySQL connection string
            if self.username and self.password:
                return f"mysql+pymysql://{self.username}:{self.password}@{self.server}:{self.port}/{self.database}"
            else:
                return f"mysql+pymysql://{self.server}:{self.port}/{self.database}"
        
        else:
            # SQL Server connection string
            if self.trusted_connection:
                connection_parts = [
                    f"DRIVER={{{self.driver}}}",
                    f"SERVER={self.server}",
                    f"DATABASE={self.database}",
                    "Trusted_Connection=yes"
                ]
            else:
                connection_parts = [
                    f"DRIVER={{{self.driver}}}",
                    f"SERVER={self.server}",
                    f"DATABASE={self.database}",
                    f"UID={self.username}",
                    f"PWD={self.password}"
                ]
            
            connection_string = ";".join(connection_parts)
            return f"mssql+pyodbc:///?odbc_connect={connection_string}"
    
    def _initialize_connection(self):
        """Initialize database connection and engine."""
        
        try:
            connection_string = self._build_connection_string()
            
            # Create SQLAlchemy engine
            self.engine = create_engine(
                connection_string,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                pool_pre_ping=True,
                echo=False
            )
            
            # Create session factory
            self.SessionLocal = sessionmaker(bind=self.engine)
            
            # Test connection
            self._test_connection()
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize {self.db_type} connection: {e}")
            raise
    
    def _test_connection(self):
        """Test database connection."""
        
        try:
            with self.get_connection() as conn:
                if self.db_type == 'mysql':
                    result = conn.execute(text("SELECT 1 as test")).fetchone()
                else:
                    result = conn.execute(text("SELECT 1 as test")).fetchone()
                
                if result:
                    logger.info(f"✅ {self.db_type.upper()} connection test successful")
                else:
                    raise Exception("Connection test returned no result")
                    
        except Exception as e:
            logger.error(f"❌ {self.db_type.upper()} connection test failed: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup."""
        
        connection = None
        try:
            connection = self.engine.connect()
            yield connection
        except Exception as e:
            logger.error(f"❌ Database operation failed: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    @contextmanager
    def get_session(self):
        """Get database session with automatic cleanup."""
        
        session = None
        try:
            session = self.SessionLocal()
            yield session
            session.commit()
        except Exception as e:
            logger.error(f"❌ Database session failed: {e}")
            if session:
                session.rollback()
            raise
        finally:
            if session:
                session.close()
    
    def execute_query(self, query: str, params: Dict = None) -> Any:
        """Execute a query and return results."""
        
        try:
            with self.get_connection() as conn:
                if params:
                    result = conn.execute(text(query), params)
                else:
                    result = conn.execute(text(query))
                
                # Return fetchall for SELECT queries, rowcount for others
                if query.strip().upper().startswith('SELECT'):
                    return result.fetchall()
                else:
                    conn.commit()
                    return result.rowcount
                    
        except Exception as e:
            logger.error(f"❌ Query execution failed: {e}")
            raise
    
    def health_check(self) -> Dict:
        """Perform comprehensive health check."""
        
        health_status = {
            'database_type': self.db_type,
            'server': self.server,
            'database': self.database,
            'connection_status': 'unknown',
            'timestamp': datetime.now().isoformat(),
            'error': None
        }
        
        try:
            # Test basic connection
            with self.get_connection() as conn:
                if self.db_type == 'mysql':
                    result = conn.execute(text("SELECT VERSION() as version")).fetchone()
                    health_status['database_version'] = result.version if result else 'unknown'
                else:
                    result = conn.execute(text("SELECT @@VERSION as version")).fetchone()
                    health_status['database_version'] = result.version if result else 'unknown'
                
                health_status['connection_status'] = 'healthy'
                
        except Exception as e:
            health_status['connection_status'] = 'failed'
            health_status['error'] = str(e)
            logger.error(f"❌ Health check failed: {e}")
        
        return health_status
    
    @classmethod
    def from_config_file(cls, config_path: str) -> 'UnifiedSQLConnectionManager':
        """Create connection manager from JSON configuration file."""
        
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            return cls(
                server=config.get('server'),
                database=config.get('database'),
                username=config.get('username'),
                password=config.get('password'),
                driver=config.get('driver'),
                port=config.get('port'),
                trusted_connection=config.get('trusted_connection', False),
                pool_size=config.get('pool_size', 10)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to load configuration from {config_path}: {e}")
            raise

# Alias for backward compatibility
SQLConnectionManager = UnifiedSQLConnectionManager
