# =====================================================
# Topic Modeling Pipeline with MySQL & Power BI
# Essential packages for Python 3.8+
# =====================================================

# Core data processing
pandas>=1.5.0
numpy>=1.21.0

# Machine learning & model loading
scikit-learn>=1.1.0
joblib>=1.2.0

# News data collection
requests>=2.28.0
feedparser>=6.0.0
beautifulsoup4>=4.11.0
lxml>=4.9.0

# Text preprocessing & NLP
nltk>=3.8
spacy>=3.4.0

# MySQL database integration
pymysql>=1.0.0
mysql-connector-python>=8.0.0
sqlalchemy>=1.4.0

# Configuration & environment
pyyaml>=6.0
python-dotenv>=0.19.0

# Logging & monitoring
loguru>=0.6.0

# Scheduling & automation
schedule>=1.2.0

# Performance & utilities
tqdm>=4.64.0
python-dateutil>=2.8.0

# Optional: Streamlit dashboard
streamlit>=1.28.0
plotly>=5.15.0

# Optional: Development tools
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0
