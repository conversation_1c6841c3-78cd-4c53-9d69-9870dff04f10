# =====================================================
# Real-Time Topic Modeling Pipeline with MySQL & Power BI
# Optimized for Virtual Environment Installation
# =====================================================

# =====================================================
# CORE DATA PROCESSING
# =====================================================
pandas>=1.5.0,<3.0.0
numpy>=1.21.0,<2.0.0

# =====================================================
# MACHINE LEARNING & MODEL LOADING
# =====================================================
scikit-learn>=1.1.0,<2.0.0
joblib>=1.2.0                    # For .pkl model loading
pickle-mixin>=1.0.2              # Enhanced pickle support

# =====================================================
# NEWS DATA COLLECTION (NewsAPI/GNews)
# =====================================================
requests>=2.28.0,<3.0.0
feedparser>=6.0.0,<7.0.0
beautifulsoup4>=4.11.0,<5.0.0
lxml>=4.9.0                      # XML/HTML parsing
urllib3>=1.26.0,<3.0.0          # HTTP client

# =====================================================
# TEXT PREPROCESSING & NLP
# =====================================================
nltk>=3.8,<4.0.0
spacy>=3.4.0,<4.0.0
textblob>=0.17.0                 # Additional text processing
regex>=2022.10.31                # Advanced regex support

# =====================================================
# MYSQL DATABASE INTEGRATION
# =====================================================
pymysql>=1.0.0,<2.0.0
mysql-connector-python>=8.0.0,<9.0.0
sqlalchemy>=1.4.0,<3.0.0
cryptography>=3.4.8             # MySQL SSL connections

# =====================================================
# CONFIGURATION & ENVIRONMENT
# =====================================================
pyyaml>=6.0,<7.0.0
python-dotenv>=0.19.0,<2.0.0
configparser>=5.3.0             # Additional config support

# =====================================================
# LOGGING & MONITORING
# =====================================================
loguru>=0.6.0,<1.0.0

# =====================================================
# SCHEDULING & AUTOMATION
# =====================================================
schedule>=1.2.0,<2.0.0
apscheduler>=3.10.0              # Advanced scheduling

# =====================================================
# STREAMLIT DASHBOARD (Optional)
# =====================================================
streamlit>=1.28.0,<2.0.0
plotly>=5.15.0,<6.0.0
altair>=4.2.0,<6.0.0            # Additional visualization

# =====================================================
# DEVELOPMENT & TESTING (Optional)
# =====================================================
pytest>=7.0.0,<8.0.0
pytest-cov>=4.0.0               # Coverage reporting
black>=22.0.0,<24.0.0           # Code formatting
flake8>=5.0.0,<7.0.0            # Linting
isort>=5.12.0                   # Import sorting

# =====================================================
# PERFORMANCE & UTILITIES
# =====================================================
tqdm>=4.64.0                    # Progress bars
psutil>=5.9.0                   # System monitoring
python-dateutil>=2.8.0         # Date parsing
pytz>=2022.7                    # Timezone handling

# =====================================================
# SECURITY & VALIDATION
# =====================================================
validators>=0.20.0              # URL/data validation
hashlib-compat>=1.0.1          # Hash compatibility
