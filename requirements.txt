# =====================================================
# Real-Time Topic Modeling Pipeline with MySQL & Power BI
# Compatible with Python 3.8+ and Virtual Environments
# =====================================================

# =====================================================
# CORE DATA PROCESSING
# =====================================================
pandas>=1.5.0
numpy>=1.21.0

# =====================================================
# MACHINE LEARNING & MODEL LOADING
# =====================================================
scikit-learn>=1.1.0
joblib>=1.2.0                    # For .pkl model loading

# =====================================================
# NEWS DATA COLLECTION (NewsAPI/GNews)
# =====================================================
requests>=2.28.0
feedparser>=6.0.0
beautifulsoup4>=4.11.0
lxml>=4.9.0                      # XML/HTML parsing

# =====================================================
# TEXT PREPROCESSING & NLP
# =====================================================
nltk>=3.8
spacy>=3.4.0
textblob>=0.17.0                 # Additional text processing

# =====================================================
# MYSQL DATABASE INTEGRATION
# =====================================================
pymysql>=1.0.0
mysql-connector-python>=8.0.0
sqlalchemy>=1.4.0
cryptography>=3.4.8             # MySQL SSL connections

# =====================================================
# CONFIGURATION & ENVIRONMENT
# =====================================================
pyyaml>=6.0
python-dotenv>=0.19.0

# =====================================================
# LOGGING & MONITORING
# =====================================================
loguru>=0.6.0

# =====================================================
# SCHEDULING & AUTOMATION
# =====================================================
schedule>=1.2.0
apscheduler>=3.10.0              # Advanced scheduling

# =====================================================
# STREAMLIT DASHBOARD (Optional)
# =====================================================
streamlit>=1.28.0
plotly>=5.15.0

# =====================================================
# DEVELOPMENT & TESTING (Optional)
# =====================================================
pytest>=7.0.0
black>=22.0.0                   # Code formatting
flake8>=5.0.0                   # Linting

# =====================================================
# PERFORMANCE & UTILITIES
# =====================================================
tqdm>=4.64.0                    # Progress bars
python-dateutil>=2.8.0         # Date parsing
