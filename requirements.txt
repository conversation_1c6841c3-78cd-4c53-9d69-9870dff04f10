# Real-Time Topic Modeling with Pre-trained LDA
# Core dependencies for .pkl model classification

# Data processing
pandas>=1.5.0
numpy>=1.21.0

# Machine Learning (for .pkl model loading)
scikit-learn>=1.1.0

# News data collection
requests>=2.28.0
feedparser>=6.0.0

# Text preprocessing
nltk>=3.8
spacy>=3.4.0

# Configuration and utilities
pyyaml>=6.0
python-dotenv>=0.19.0

# Logging
loguru>=0.6.0

# Scheduling for hourly collection
schedule>=1.2.0

# Optional: Testing
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0
