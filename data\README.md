# 📊 Data Directory

## 📁 Purpose
This directory stores temporary data files and exports from the topic modeling pipeline.

## 🗂️ Directory Structure

```
data/
├── hourly/              # Hourly news collection data
├── processed/           # Processed and classified articles
├── dashboard/           # Power BI export files
└── temp/               # Temporary processing files
```

## 📋 File Types

### **Hourly Collection (`hourly/`):**
- `news_YYYY-MM-DD_HH.csv` - Raw articles fetched each hour
- Contains: title, description, content, source, published_at, url

### **Processed Data (`processed/`):**
- `classified_YYYY-MM-DD_HH.csv` - Articles with topic classifications
- Contains: article_id, cleaned_text, topic_id, confidence, classification_timestamp

### **Dashboard Exports (`dashboard/`):**
- `hourly_trends.csv` - Real-time trend data for Power BI
- `realtime_articles.csv` - Latest classified articles
- `topic_summary.csv` - Topic distribution summary

### **Temporary Files (`temp/`):**
- Processing intermediates
- Error recovery files
- Backup data

## 🔄 Data Flow

```
NewsAPI → hourly/ → MySQL → processed/ → dashboard/ → Power BI
```

## 🧹 Data Cleanup

### **Automatic Cleanup:**
- Files older than 7 days are automatically removed
- Temporary files cleaned after each pipeline run
- Dashboard files refreshed hourly

### **Manual Cleanup:**
```bash
# Remove old hourly files
find data/hourly -name "*.csv" -mtime +7 -delete

# Clear all temporary files
rm -rf data/temp/*

# Reset dashboard exports
rm -rf data/dashboard/*
```

## 📈 Data Monitoring

### **File Size Monitoring:**
- Hourly files: ~1-5MB each
- Processed files: ~2-10MB each
- Dashboard files: <1MB each

### **Data Quality Checks:**
- Duplicate article detection
- Missing field validation
- Classification confidence thresholds

## 🚨 Troubleshooting

### **Common Issues:**

1. **"Permission denied"**
   - Check directory write permissions
   - Ensure sufficient disk space

2. **"File not found"**
   - Directory created automatically on first run
   - Check pipeline configuration paths

3. **"Large file sizes"**
   - Monitor news volume during major events
   - Adjust collection frequency if needed

**This directory is automatically managed by the pipeline!** 📁
