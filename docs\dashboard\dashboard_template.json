{"name": "Topic Modeling Dashboard Template", "description": "Power BI template for news topic modeling analysis", "version": "1.0", "pages": [{"name": "Overview", "visualizations": [{"type": "<PERSON><PERSON><PERSON>", "title": "Topic Distribution", "data": {"table": "powerbi_topic_summary", "values": "Article_Count", "legend": "Topic_Label"}, "position": {"x": 0, "y": 0, "width": 6, "height": 4}}, {"type": "lineChart", "title": "Topic Trends Over Time", "data": {"table": "powerbi_time_series", "axis": "Date", "values": "Article_Count", "legend": "Topic_Label"}, "position": {"x": 6, "y": 0, "width": 6, "height": 4}}, {"type": "<PERSON><PERSON><PERSON>", "title": "Articles by Source", "data": {"table": "powerbi_data", "axis": "Source_Name", "values": "COUNT(Article_Title)"}, "position": {"x": 0, "y": 4, "width": 6, "height": 4}}, {"type": "histogram", "title": "Topic Confidence Distribution", "data": {"table": "powerbi_data", "axis": "Topic_Confidence", "values": "COUNT(Article_Title)"}, "position": {"x": 6, "y": 4, "width": 6, "height": 4}}, {"type": "slicer", "title": "Date Range", "data": {"table": "powerbi_data", "field": "Published_Date"}, "position": {"x": 0, "y": 8, "width": 12, "height": 1}}]}, {"name": "Topic Analysis", "visualizations": [{"type": "table", "title": "Topic Summary", "data": {"table": "powerbi_topic_summary", "columns": ["Topic_Label", "Article_Count", "Topic_Percentage", "Avg_Confidence"]}, "position": {"x": 0, "y": 0, "width": 6, "height": 6}}, {"type": "table", "title": "Top Keywords by Topic", "data": {"table": "powerbi_topic_summary", "columns": ["Topic_Label", "Topic_Keywords"]}, "position": {"x": 6, "y": 0, "width": 6, "height": 6}}, {"type": "scatterPlot", "title": "Content Length vs Confidence", "data": {"table": "powerbi_data", "x": "Character_Count", "y": "Topic_Confidence", "legend": "Topic_Label"}, "position": {"x": 0, "y": 6, "width": 6, "height": 4}}, {"type": "clusteredBarChart", "title": "Weekend vs Weekday Publishing", "data": {"table": "powerbi_data", "axis": "Is_Weekend", "values": "COUNT(Article_Title)", "legend": "Topic_Label"}, "position": {"x": 6, "y": 6, "width": 6, "height": 4}}]}, {"name": "Time Analysis", "visualizations": [{"type": "areaChart", "title": "Daily Article Volume", "data": {"table": "powerbi_time_series", "axis": "Date", "values": "Article_Count", "legend": "Topic_Label"}, "position": {"x": 0, "y": 0, "width": 12, "height": 5}}, {"type": "clusteredColumnChart", "title": "Articles by Day of Week", "data": {"table": "powerbi_data", "axis": "Weekday", "values": "COUNT(Article_Title)", "legend": "Topic_Label"}, "position": {"x": 0, "y": 5, "width": 6, "height": 4}}, {"type": "lineChart", "title": "Publishing Patterns by Hour", "data": {"table": "powerbi_data", "axis": "Published_Hour", "values": "COUNT(Article_Title)"}, "position": {"x": 6, "y": 5, "width": 6, "height": 4}}]}], "measures": [{"name": "Total Articles", "formula": "COUNT(powerbi_data[Article_Title])"}, {"name": "Average Confidence", "formula": "AVERAGE(powerbi_data[Topic_Confidence])"}, {"name": "Articles This Week", "formula": "CALCULATE(COUNT(powerbi_data[Article_Title]), DATESINPERIOD(powerbi_data[Published_Date], TODAY(), -7, DAY))"}, {"name": "Top Topic", "formula": "CALCULATE(FIRST(powerbi_data[Topic_Label]), TOPN(1, VALUES(powerbi_data[Topic_Label]), COUNT(powerbi_data[Article_Title])))"}, {"name": "Unique Sources", "formula": "DISTINCTCOUNT(powerbi_data[Source_Name])"}, {"name": "Average Article Length", "formula": "AVERAGE(powerbi_data[Character_Count])"}], "relationships": [{"from": {"table": "powerbi_data", "column": "Topic_ID"}, "to": {"table": "powerbi_topic_summary", "column": "Topic_ID"}, "type": "many-to-one"}, {"from": {"table": "powerbi_data", "column": "Topic_ID"}, "to": {"table": "powerbi_time_series", "column": "Topic_ID"}, "type": "many-to-one"}], "slicers": [{"name": "Date Range", "table": "powerbi_data", "column": "Published_Date", "type": "date<PERSON><PERSON><PERSON>"}, {"name": "Topic Filter", "table": "powerbi_data", "column": "Topic_Label", "type": "list"}, {"name": "Source Filter", "table": "powerbi_data", "column": "Source_Name", "type": "list"}, {"name": "Content Length", "table": "powerbi_data", "column": "Content_Length_Category", "type": "list"}], "formatting": {"theme": "corporate", "colors": {"primary": "#1f77b4", "secondary": "#ff7f0e", "accent": "#2ca02c", "background": "#ffffff", "text": "#333333"}, "fonts": {"title": "Segoe UI Semibold", "body": "Segoe UI", "size": {"title": 14, "body": 10}}}, "instructions": {"setup": ["1. Open Power BI Desktop", "2. Import powerbi_data.csv as main table", "3. Import powerbi_topic_summary.csv", "4. Import powerbi_time_series.csv", "5. Create relationships between tables using Topic_ID", "6. Add calculated measures from the measures section", "7. Create visualizations according to the template", "8. Apply formatting and color scheme", "9. Add slicers for interactivity", "10. Test all visualizations and interactions"], "customization": ["- Adjust color scheme to match your brand", "- Modify topic labels for better readability", "- Add company logo and branding", "- Customize date ranges based on your data", "- Add additional calculated measures as needed", "- Create drill-through pages for detailed analysis"], "maintenance": ["- Refresh data regularly (daily/weekly)", "- Monitor dashboard performance", "- Update visualizations based on user feedback", "- Archive old data to maintain performance", "- Document any customizations made"]}}