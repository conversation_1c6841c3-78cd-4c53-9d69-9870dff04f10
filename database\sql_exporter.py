#!/usr/bin/env python3
"""
SQL Server Data Exporter
High-performance data export system for topic modeling pipeline
Replaces CSV exports with real-time SQL Server integration
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import json
from pathlib import Path
import time

# Import connection manager
from sql_connection import SQLConnectionManager, create_connection_from_config

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SQLExporter:
    """
    High-performance SQL Server exporter for topic modeling data.
    Replaces CSV-based exports with real-time database operations.
    """
    
    def __init__(self, connection_manager: SQLConnectionManager):
        """
        Initialize SQL exporter.
        
        Args:
            connection_manager: SQLConnectionManager instance
        """
        self.conn_manager = connection_manager
        self.batch_size = 1000
        
        # Initialize topics if needed
        self._ensure_topics_exist()
        
        logger.info("📊 SQL Exporter initialized")
    
    def _ensure_topics_exist(self):
        """Ensure topics table has required topics."""
        
        try:
            # Check if topics exist
            existing_topics = self.conn_manager.execute_query(
                "SELECT topic_id, topic_label FROM topics"
            )
            
            if existing_topics.empty:
                logger.info("📝 Initializing topics table with default topics")
                
                # Insert default topics (these will be updated by the pipeline)
                default_topics = [
                    (0, 'Technology', '["technology", "ai", "artificial", "intelligence", "software"]'),
                    (1, 'Politics', '["politics", "election", "government", "policy", "democracy"]'),
                    (2, 'Business', '["business", "economy", "market", "finance", "company"]'),
                    (3, 'Health', '["health", "medical", "healthcare", "medicine", "treatment"]'),
                    (4, 'Sports', '["sports", "football", "basketball", "soccer", "game"]'),
                    (5, 'Entertainment', '["entertainment", "movie", "music", "celebrity", "film"]'),
                    (6, 'Science', '["science", "research", "study", "discovery", "scientific"]'),
                    (7, 'Environment', '["environment", "climate", "energy", "green", "sustainability"]')
                ]
                
                for topic_id, label, keywords in default_topics:
                    self.conn_manager.execute_non_query(
                        """
                        INSERT INTO topics (topic_id, topic_label, topic_keywords)
                        VALUES (?, ?, ?)
                        """,
                        {'topic_id': topic_id, 'topic_label': label, 'topic_keywords': keywords}
                    )
                
                logger.info("✅ Default topics initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to ensure topics exist: {e}")
            raise
    
    def export_articles(self, articles_df: pd.DataFrame) -> int:
        """
        Export articles to SQL Server.
        
        Args:
            articles_df: DataFrame with article data
            
        Returns:
            Number of articles exported
        """
        
        if articles_df.empty:
            logger.warning("⚠️ No articles to export")
            return 0
        
        try:
            logger.info(f"📰 Exporting {len(articles_df)} articles to SQL Server...")
            
            # Prepare articles DataFrame
            articles_clean = self._prepare_articles_dataframe(articles_df)
            
            # Use bulk insert with upsert logic
            return self._upsert_articles(articles_clean)
            
        except Exception as e:
            logger.error(f"❌ Article export failed: {e}")
            raise
    
    def _prepare_articles_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare articles DataFrame for SQL Server."""
        
        # Required columns for articles table
        required_columns = [
            'article_id', 'title', 'description', 'content', 'full_text',
            'source_name', 'source_id', 'author', 'url', 'url_to_image',
            'published_at', 'collected_at', 'content_hash', 'language', 'country', 'category'
        ]
        
        # Create clean DataFrame with required columns
        articles_clean = pd.DataFrame()
        
        for col in required_columns:
            if col in df.columns:
                articles_clean[col] = df[col]
            else:
                # Set default values for missing columns
                if col == 'collected_at':
                    articles_clean[col] = datetime.now()
                elif col == 'language':
                    articles_clean[col] = 'en'
                elif col in ['source_id', 'author', 'url_to_image', 'country', 'category']:
                    articles_clean[col] = None
                else:
                    articles_clean[col] = ''
        
        # Ensure proper data types
        articles_clean['published_at'] = pd.to_datetime(articles_clean['published_at'])
        articles_clean['collected_at'] = pd.to_datetime(articles_clean['collected_at'])
        
        return articles_clean
    
    def _upsert_articles(self, articles_df: pd.DataFrame) -> int:
        """Upsert articles with duplicate handling."""
        
        try:
            # Check for existing articles
            existing_urls = self.conn_manager.execute_query(
                """
                SELECT url FROM articles 
                WHERE url IN ({})
                """.format(','.join(['?' for _ in articles_df['url'].dropna()]))
            )
            
            # Filter out existing articles
            if not existing_urls.empty:
                existing_url_set = set(existing_urls['url'].tolist())
                new_articles = articles_df[~articles_df['url'].isin(existing_url_set)]
                logger.info(f"🔍 Filtered out {len(articles_df) - len(new_articles)} duplicate articles")
            else:
                new_articles = articles_df
            
            if new_articles.empty:
                logger.info("ℹ️ No new articles to insert")
                return 0
            
            # Bulk insert new articles
            rows_inserted = self.conn_manager.bulk_insert_dataframe(
                new_articles, 
                'articles',
                if_exists='append',
                chunk_size=self.batch_size
            )
            
            return rows_inserted
            
        except Exception as e:
            logger.error(f"❌ Article upsert failed: {e}")
            raise
    
    def export_article_topics(self, article_topics_df: pd.DataFrame) -> int:
        """
        Export article topic classifications to SQL Server.
        
        Args:
            article_topics_df: DataFrame with article topic data
            
        Returns:
            Number of classifications exported
        """
        
        if article_topics_df.empty:
            logger.warning("⚠️ No article topics to export")
            return 0
        
        try:
            logger.info(f"🏷️ Exporting {len(article_topics_df)} article classifications...")
            
            # Prepare classifications DataFrame
            topics_clean = self._prepare_article_topics_dataframe(article_topics_df)
            
            # Use bulk insert with upsert logic
            return self._upsert_article_topics(topics_clean)
            
        except Exception as e:
            logger.error(f"❌ Article topics export failed: {e}")
            raise
    
    def _prepare_article_topics_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare article topics DataFrame for SQL Server."""
        
        # Required columns for article_topics table
        required_columns = [
            'article_id', 'topic_id', 'confidence', 'all_probabilities', 
            'classified_at', 'model_version'
        ]
        
        # Create clean DataFrame
        topics_clean = pd.DataFrame()
        
        for col in required_columns:
            if col in df.columns:
                topics_clean[col] = df[col]
            else:
                # Set default values
                if col == 'classified_at':
                    topics_clean[col] = datetime.now()
                elif col == 'model_version':
                    topics_clean[col] = '1.0'
                elif col == 'all_probabilities':
                    topics_clean[col] = None
                else:
                    topics_clean[col] = 0
        
        # Ensure proper data types
        topics_clean['classified_at'] = pd.to_datetime(topics_clean['classified_at'])
        topics_clean['confidence'] = pd.to_numeric(topics_clean['confidence'])
        topics_clean['topic_id'] = pd.to_numeric(topics_clean['topic_id'], downcast='integer')
        
        # Convert all_probabilities to JSON string if it's a list
        if 'all_probabilities' in topics_clean.columns:
            topics_clean['all_probabilities'] = topics_clean['all_probabilities'].apply(
                lambda x: json.dumps(x) if isinstance(x, list) else x
            )
        
        return topics_clean
    
    def _upsert_article_topics(self, topics_df: pd.DataFrame) -> int:
        """Upsert article topics with duplicate handling."""
        
        try:
            # Delete existing classifications for these articles (re-classification)
            article_ids = topics_df['article_id'].unique().tolist()
            
            if article_ids:
                placeholders = ','.join(['?' for _ in article_ids])
                deleted_count = self.conn_manager.execute_non_query(
                    f"DELETE FROM article_topics WHERE article_id IN ({placeholders})",
                    dict(zip([f'param{i}' for i in range(len(article_ids))], article_ids))
                )
                
                if deleted_count > 0:
                    logger.info(f"🗑️ Removed {deleted_count} existing classifications for re-classification")
            
            # Bulk insert new classifications
            rows_inserted = self.conn_manager.bulk_insert_dataframe(
                topics_df,
                'article_topics',
                if_exists='append',
                chunk_size=self.batch_size
            )
            
            return rows_inserted
            
        except Exception as e:
            logger.error(f"❌ Article topics upsert failed: {e}")
            raise
    
    def update_topics(self, topics_data: Dict[int, Dict]) -> int:
        """
        Update topics master table.
        
        Args:
            topics_data: Dictionary mapping topic_id to topic info
            
        Returns:
            Number of topics updated
        """
        
        try:
            logger.info(f"📋 Updating {len(topics_data)} topics...")
            
            updated_count = 0
            
            for topic_id, topic_info in topics_data.items():
                # Upsert topic
                affected_rows = self.conn_manager.execute_non_query(
                    """
                    MERGE topics AS target
                    USING (SELECT ? as topic_id, ? as topic_label, ? as topic_keywords) AS source
                    ON target.topic_id = source.topic_id
                    WHEN MATCHED THEN
                        UPDATE SET 
                            topic_label = source.topic_label,
                            topic_keywords = source.topic_keywords,
                            updated_at = GETDATE()
                    WHEN NOT MATCHED THEN
                        INSERT (topic_id, topic_label, topic_keywords)
                        VALUES (source.topic_id, source.topic_label, source.topic_keywords);
                    """,
                    {
                        'topic_id': topic_id,
                        'topic_label': topic_info['label'],
                        'topic_keywords': json.dumps(topic_info.get('keywords', []))
                    }
                )
                
                updated_count += 1
            
            logger.info(f"✅ Updated {updated_count} topics")
            return updated_count
            
        except Exception as e:
            logger.error(f"❌ Topics update failed: {e}")
            raise
    
    def generate_aggregated_data(self) -> Dict[str, int]:
        """
        Generate all aggregated tables for Power BI.
        
        Returns:
            Dictionary with counts of generated records
        """
        
        logger.info("📊 Generating aggregated data for Power BI...")
        
        results = {}
        
        try:
            # Generate daily trends
            results['daily_trends'] = self._generate_daily_trends()
            
            # Generate hourly trends
            results['hourly_trends'] = self._generate_hourly_trends()
            
            # Generate source analysis
            results['source_analysis'] = self._generate_source_analysis()
            
            # Generate confidence analysis
            results['confidence_analysis'] = self._generate_confidence_analysis()
            
            logger.info(f"✅ Aggregated data generation complete: {results}")
            return results
            
        except Exception as e:
            logger.error(f"❌ Aggregated data generation failed: {e}")
            raise
    
    def _generate_daily_trends(self) -> int:
        """Generate daily trends aggregation."""
        
        try:
            # Generate daily trends for last 90 days
            daily_trends_query = """
            WITH daily_stats AS (
                SELECT 
                    CAST(a.published_at AS DATE) as date_key,
                    at.topic_id,
                    t.topic_label,
                    COUNT(*) as article_count,
                    AVG(at.confidence) as avg_confidence,
                    STDEV(at.confidence) as confidence_std,
                    MIN(at.confidence) as min_confidence,
                    MAX(at.confidence) as max_confidence
                FROM articles a
                INNER JOIN article_topics at ON a.article_id = at.article_id
                INNER JOIN topics t ON at.topic_id = t.topic_id
                WHERE a.published_at >= DATEADD(DAY, -90, GETDATE())
                AND at.confidence >= 0.3
                GROUP BY CAST(a.published_at AS DATE), at.topic_id, t.topic_label
            ),
            daily_totals AS (
                SELECT 
                    date_key,
                    SUM(article_count) as daily_total
                FROM daily_stats
                GROUP BY date_key
            )
            SELECT 
                ds.*,
                CAST(ds.article_count * 100.0 / dt.daily_total AS DECIMAL(5,2)) as percentage_of_day
            FROM daily_stats ds
            INNER JOIN daily_totals dt ON ds.date_key = dt.date_key
            """
            
            daily_data = self.conn_manager.execute_query(daily_trends_query)
            
            if not daily_data.empty:
                # Convert to list of dictionaries for upsert
                trends_list = daily_data.to_dict('records')
                return self.conn_manager.upsert_daily_trends(trends_list)
            
            return 0
            
        except Exception as e:
            logger.error(f"❌ Daily trends generation failed: {e}")
            raise
    
    def _generate_hourly_trends(self) -> int:
        """Generate hourly trends aggregation."""
        
        try:
            # Generate hourly trends for last 7 days
            hourly_trends_query = """
            SELECT 
                DATEADD(HOUR, DATEDIFF(HOUR, 0, a.published_at), 0) as hour_key,
                at.topic_id,
                t.topic_label,
                COUNT(*) as article_count,
                AVG(at.confidence) as avg_confidence,
                STDEV(at.confidence) as confidence_std,
                DATEPART(HOUR, a.published_at) as hour_of_day,
                DATENAME(WEEKDAY, a.published_at) as day_of_week
            FROM articles a
            INNER JOIN article_topics at ON a.article_id = at.article_id
            INNER JOIN topics t ON at.topic_id = t.topic_id
            WHERE a.published_at >= DATEADD(DAY, -7, GETDATE())
            AND at.confidence >= 0.3
            GROUP BY 
                DATEADD(HOUR, DATEDIFF(HOUR, 0, a.published_at), 0),
                at.topic_id, 
                t.topic_label,
                DATEPART(HOUR, a.published_at),
                DATENAME(WEEKDAY, a.published_at)
            """
            
            hourly_data = self.conn_manager.execute_query(hourly_trends_query)
            
            if not hourly_data.empty:
                # Convert to list of dictionaries for upsert
                trends_list = hourly_data.to_dict('records')
                return self.conn_manager.upsert_hourly_trends(trends_list)
            
            return 0
            
        except Exception as e:
            logger.error(f"❌ Hourly trends generation failed: {e}")
            raise
    
    def _generate_source_analysis(self) -> int:
        """Generate source analysis aggregation."""
        
        try:
            # Clear existing source analysis
            self.conn_manager.execute_non_query("DELETE FROM source_analysis")
            
            # Generate source analysis
            source_analysis_query = """
            WITH source_topic_stats AS (
                SELECT 
                    a.source_name,
                    at.topic_id,
                    t.topic_label,
                    COUNT(*) as article_count,
                    AVG(at.confidence) as avg_confidence,
                    MAX(a.published_at) as last_article_date
                FROM articles a
                INNER JOIN article_topics at ON a.article_id = at.article_id
                INNER JOIN topics t ON at.topic_id = t.topic_id
                WHERE at.confidence >= 0.3
                GROUP BY a.source_name, at.topic_id, t.topic_label
            ),
            source_totals AS (
                SELECT 
                    source_name,
                    SUM(article_count) as total_articles_from_source,
                    COUNT(DISTINCT topic_id) as topics_covered_by_source
                FROM source_topic_stats
                GROUP BY source_name
            )
            SELECT 
                sts.*,
                st.total_articles_from_source,
                st.topics_covered_by_source,
                CAST(sts.article_count * 100.0 / st.total_articles_from_source AS DECIMAL(5,2)) as percentage_within_source
            FROM source_topic_stats sts
            INNER JOIN source_totals st ON sts.source_name = st.source_name
            """
            
            source_data = self.conn_manager.execute_query(source_analysis_query)
            
            if not source_data.empty:
                return self.conn_manager.bulk_insert_dataframe(
                    source_data, 'source_analysis', if_exists='append'
                )
            
            return 0
            
        except Exception as e:
            logger.error(f"❌ Source analysis generation failed: {e}")
            raise
    
    def _generate_confidence_analysis(self) -> int:
        """Generate confidence analysis aggregation."""
        
        try:
            # Clear existing confidence analysis
            self.conn_manager.execute_non_query("DELETE FROM confidence_analysis")
            
            # Generate confidence analysis
            confidence_analysis_query = """
            SELECT 
                CASE 
                    WHEN at.confidence < 0.3 THEN '0.0-0.3'
                    WHEN at.confidence < 0.5 THEN '0.3-0.5'
                    WHEN at.confidence < 0.7 THEN '0.5-0.7'
                    WHEN at.confidence < 0.9 THEN '0.7-0.9'
                    ELSE '0.9-1.0'
                END as confidence_bin,
                at.topic_id,
                t.topic_label,
                COUNT(*) as article_count,
                AVG(at.confidence) as avg_confidence,
                STDEV(at.confidence) as confidence_std,
                CASE 
                    WHEN at.confidence < 0.3 THEN 0.0
                    WHEN at.confidence < 0.5 THEN 0.3
                    WHEN at.confidence < 0.7 THEN 0.5
                    WHEN at.confidence < 0.9 THEN 0.7
                    ELSE 0.9
                END as bin_min,
                CASE 
                    WHEN at.confidence < 0.3 THEN 0.3
                    WHEN at.confidence < 0.5 THEN 0.5
                    WHEN at.confidence < 0.7 THEN 0.7
                    WHEN at.confidence < 0.9 THEN 0.9
                    ELSE 1.0
                END as bin_max
            FROM article_topics at
            INNER JOIN topics t ON at.topic_id = t.topic_id
            GROUP BY 
                CASE 
                    WHEN at.confidence < 0.3 THEN '0.0-0.3'
                    WHEN at.confidence < 0.5 THEN '0.3-0.5'
                    WHEN at.confidence < 0.7 THEN '0.5-0.7'
                    WHEN at.confidence < 0.9 THEN '0.7-0.9'
                    ELSE '0.9-1.0'
                END,
                at.topic_id,
                t.topic_label,
                CASE 
                    WHEN at.confidence < 0.3 THEN 0.0
                    WHEN at.confidence < 0.5 THEN 0.3
                    WHEN at.confidence < 0.7 THEN 0.5
                    WHEN at.confidence < 0.9 THEN 0.7
                    ELSE 0.9
                END,
                CASE 
                    WHEN at.confidence < 0.3 THEN 0.3
                    WHEN at.confidence < 0.5 THEN 0.5
                    WHEN at.confidence < 0.7 THEN 0.7
                    WHEN at.confidence < 0.9 THEN 0.9
                    ELSE 1.0
                END
            """
            
            confidence_data = self.conn_manager.execute_query(confidence_analysis_query)
            
            if not confidence_data.empty:
                return self.conn_manager.bulk_insert_dataframe(
                    confidence_data, 'confidence_analysis', if_exists='append'
                )
            
            return 0
            
        except Exception as e:
            logger.error(f"❌ Confidence analysis generation failed: {e}")
            raise
    
    def log_pipeline_run(self, 
                        run_id: str,
                        status: str,
                        start_time: datetime,
                        end_time: datetime = None,
                        articles_collected: int = 0,
                        articles_processed: int = 0,
                        articles_classified: int = 0,
                        error_message: str = None) -> bool:
        """
        Log pipeline run information.
        
        Args:
            run_id: Unique run identifier
            status: Run status ('RUNNING', 'SUCCESS', 'FAILED')
            start_time: Run start time
            end_time: Run end time
            articles_collected: Number of articles collected
            articles_processed: Number of articles processed
            articles_classified: Number of articles classified
            error_message: Error message if failed
            
        Returns:
            True if logged successfully
        """
        
        try:
            duration_seconds = None
            if end_time and start_time:
                duration_seconds = int((end_time - start_time).total_seconds())
            
            self.conn_manager.execute_non_query(
                """
                MERGE pipeline_runs AS target
                USING (SELECT ? as run_id) AS source
                ON target.run_id = source.run_id
                WHEN MATCHED THEN
                    UPDATE SET 
                        status = ?,
                        end_time = ?,
                        articles_collected = ?,
                        articles_processed = ?,
                        articles_classified = ?,
                        error_message = ?,
                        duration_seconds = ?
                WHEN NOT MATCHED THEN
                    INSERT (run_id, status, start_time, end_time, articles_collected, 
                           articles_processed, articles_classified, error_message, duration_seconds)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);
                """,
                {
                    'run_id': run_id,
                    'status': status,
                    'end_time': end_time,
                    'articles_collected': articles_collected,
                    'articles_processed': articles_processed,
                    'articles_classified': articles_classified,
                    'error_message': error_message,
                    'duration_seconds': duration_seconds,
                    'start_time': start_time
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Pipeline run logging failed: {e}")
            return False

def main():
    """Test the SQL exporter."""
    
    try:
        # Create connection and exporter
        conn_manager = create_connection_from_config()
        exporter = SQLExporter(conn_manager)
        
        # Test health check
        health = conn_manager.health_check()
        logger.info(f"🏥 Database health: {health}")
        
        # Test aggregated data generation
        results = exporter.generate_aggregated_data()
        logger.info(f"📊 Aggregation results: {results}")
        
    except Exception as e:
        logger.error(f"❌ SQL exporter test failed: {e}")

if __name__ == "__main__":
    main()
