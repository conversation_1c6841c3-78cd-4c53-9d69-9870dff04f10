# 🚀 Power BI DirectQuery Setup Guide

**Complete guide for connecting Power BI to SQL Server with real-time DirectQuery**

## 🎯 **Overview**

This guide shows you how to connect Power BI Desktop to your SQL Server database using DirectQuery mode for real-time topic modeling dashboards. DirectQuery provides live data connections without importing data into Power BI.

## 📋 **Prerequisites**

### **1. SQL Server Setup**
- ✅ SQL Server instance running (local or remote)
- ✅ TopicModelingDB database created
- ✅ Schema and views deployed from `database/schema.sql` and `database/powerbi_views.sql`
- ✅ Pipeline running and populating data

### **2. Power BI Requirements**
- ✅ Power BI Desktop (latest version)
- ✅ SQL Server ODBC Driver 17 or later
- ✅ Network access to SQL Server
- ✅ Appropriate SQL Server permissions

### **3. Data Gateway (if needed)**
- ✅ On-premises data gateway (for local SQL Server)
- ✅ Gateway configured and running
- ✅ SQL Server data source added to gateway

## 🔗 **Step 1: Connect Power BI to SQL Server**

### **1.1 Open Power BI Desktop**
1. Launch Power BI Desktop
2. Click **Get Data** → **More...**
3. Search for "SQL Server" and select **SQL Server database**
4. Click **Connect**

### **1.2 Configure Connection**
```
Server: localhost (or your SQL Server instance)
Database: TopicModelingDB
Data Connectivity mode: DirectQuery ← IMPORTANT!
```

### **1.3 Authentication**
Choose your authentication method:
- **Windows Authentication** (recommended for local)
- **Database Authentication** (username/password)

### **1.4 Select Views**
In the Navigator, select these optimized views:
- ✅ `vw_topic_summary`
- ✅ `vw_kpi_metrics`
- ✅ `vw_daily_trends_enhanced`
- ✅ `vw_hourly_trends_enhanced`
- ✅ `vw_recent_articles`
- ✅ `vw_source_performance`
- ✅ `vw_confidence_distribution`
- ✅ `vw_model_performance`
- ✅ `vw_pipeline_health`

**Important:** Do NOT select the base tables - use views for optimal performance.

## 📊 **Step 2: Create Relationships**

### **2.1 Model View**
1. Click **Model** view in Power BI
2. Power BI should auto-detect relationships
3. Verify these key relationships exist:

```
vw_topic_summary[topic_id] ↔ vw_daily_trends_enhanced[topic_id]
vw_topic_summary[topic_id] ↔ vw_hourly_trends_enhanced[topic_id]
vw_topic_summary[topic_id] ↔ vw_recent_articles[topic_id]
vw_topic_summary[topic_id] ↔ vw_source_performance[topic_id]
vw_topic_summary[topic_id] ↔ vw_confidence_distribution[topic_id]
```

### **2.2 Relationship Settings**
- **Cardinality:** Many-to-One (from detail to summary)
- **Cross filter direction:** Single
- **Make this relationship active:** ✅

## 📈 **Step 3: Build Dashboard Pages**

### **Page 1: Executive Overview**

**Key Metrics Cards:**
```
Total Articles: SUM(vw_kpi_metrics[total_articles])
Active Topics: MAX(vw_kpi_metrics[active_topics])
Avg Confidence: AVERAGE(vw_kpi_metrics[overall_avg_confidence])
Classification Rate: MAX(vw_kpi_metrics[classification_rate])
```

**Topic Distribution (Pie Chart):**
- Values: `total_articles` from `vw_topic_summary`
- Legend: `topic_label`

**Recent Trends (Line Chart):**
- X-axis: `date_key` from `vw_daily_trends_enhanced`
- Y-axis: `article_count`
- Legend: `topic_label`
- Filter: Last 30 days

### **Page 2: Topic Analysis**

**Topic Performance Table:**
- Columns: `topic_label`, `total_articles`, `avg_confidence`, `unique_sources`
- Sort by: `total_articles` descending

**Daily Trends with Moving Average:**
- X-axis: `date_key`
- Y-axis: `article_count` and `rolling_7day_avg`
- Legend: `topic_label`

**Topic Confidence Distribution:**
- X-axis: `confidence_bin` from `vw_confidence_distribution`
- Y-axis: `article_count`
- Legend: `topic_label`

### **Page 3: Time Analysis**

**Hourly Activity Heatmap:**
- Rows: `day_of_week` from `vw_hourly_trends_enhanced`
- Columns: `hour_of_day`
- Values: `article_count`

**Time Period Analysis:**
- X-axis: `time_period` from `vw_hourly_trends_enhanced`
- Y-axis: `article_count`
- Legend: `topic_label`

**Day Type Comparison:**
- X-axis: `day_type` (Weekday vs Weekend)
- Y-axis: Average `article_count`

### **Page 4: Source Analysis**

**Top Sources (Bar Chart):**
- Y-axis: `source_name` from `vw_source_performance`
- X-axis: `article_count`
- Color: `source_quality_tier`

**Source Quality vs Volume (Scatter):**
- X-axis: `total_articles_from_source`
- Y-axis: `avg_confidence`
- Size: `topics_covered_by_source`
- Tooltip: `source_name`

**Source Diversity Analysis:**
- X-axis: `topic_diversity_tier`
- Y-axis: Count of sources

### **Page 5: Model Performance**

**Classification Rate Trend:**
- X-axis: `metric_date` from `vw_model_performance`
- Y-axis: `classification_rate_today`
- Target line at 70%

**Confidence Distribution:**
- X-axis: Confidence ranges
- Y-axis: `high_confidence_today`, `medium_confidence_today`, `low_confidence_today`

**Model Health Indicators:**
- Cards showing latest metrics from `vw_model_performance`

### **Page 6: Pipeline Monitoring**

**Pipeline Status:**
- Table from `vw_pipeline_health`
- Conditional formatting for `health_status`

**Data Freshness:**
- Cards from `vw_data_freshness`
- Show `minutes_since_latest` for each data type

## ⚡ **Step 4: DirectQuery Optimization**

### **4.1 Query Reduction**
```dax
// Use these DAX patterns for better performance

// Instead of complex calculations, use pre-calculated fields
Articles This Hour = 
MAX(vw_hourly_trends_enhanced[article_count])

// Use SELECTEDVALUE for single selections
Selected Topic = 
SELECTEDVALUE(vw_topic_summary[topic_label], "All Topics")

// Avoid complex aggregations across large datasets
```

### **4.2 Filter Optimization**
- Add date slicers with relative date filtering
- Use topic filters with single-select when possible
- Limit initial data load with default filters

### **4.3 Visual Optimization**
- Limit visuals to 1000 data points initially
- Use "Show items with no data" sparingly
- Enable "Reduce query" option in visual settings

## 🔄 **Step 5: Real-Time Refresh**

### **5.1 Automatic Refresh**
1. Go to **File** → **Options and settings** → **Options**
2. Select **DirectQuery**
3. Set **Automatic page refresh** to desired interval (minimum 1 second)

### **5.2 Manual Refresh**
- Click **Refresh** button in Power BI Desktop
- Data updates automatically with DirectQuery

### **5.3 Scheduled Refresh (Power BI Service)**
1. Publish report to Power BI Service
2. Configure data source credentials
3. Set up automatic refresh schedule

## 🌐 **Step 6: Data Gateway Setup (Local SQL Server)**

### **6.1 Install Gateway**
1. Download **On-premises data gateway** from Microsoft
2. Install on machine with SQL Server access
3. Sign in with Power BI account

### **6.2 Configure Data Source**
1. Open **Power BI Service** → **Settings** → **Manage gateways**
2. Add new data source:
   ```
   Data Source Name: TopicModelingDB
   Data Source Type: SQL Server
   Server: localhost
   Database: TopicModelingDB
   Authentication Method: Windows or SQL Server
   ```

### **6.3 Test Connection**
- Click **Test connection**
- Verify successful connection
- Map users to data source

## 📊 **Step 7: Advanced Features**

### **7.1 Row-Level Security (Optional)**
```sql
-- Create security function in SQL Server
CREATE FUNCTION dbo.fn_securitypredicate(@topic_id AS int)
RETURNS TABLE
WITH SCHEMABINDING
AS
RETURN SELECT 1 AS fn_securitypredicate_result
WHERE @topic_id IN (SELECT topic_id FROM user_topic_access WHERE username = USER_NAME())

-- Apply security policy
CREATE SECURITY POLICY TopicFilter
ADD FILTER PREDICATE dbo.fn_securitypredicate(topic_id) ON dbo.vw_recent_articles
WITH (STATE = ON)
```

### **7.2 Custom Measures**
```dax
// Trending Score
Trending Score = 
VAR CurrentPeriod = 
    CALCULATE(
        SUM(vw_daily_trends_enhanced[article_count]),
        DATESINPERIOD(vw_daily_trends_enhanced[date_key], MAX(vw_daily_trends_enhanced[date_key]), -1, DAY)
    )
VAR PreviousPeriod = 
    CALCULATE(
        SUM(vw_daily_trends_enhanced[article_count]),
        DATESINPERIOD(vw_daily_trends_enhanced[date_key], MAX(vw_daily_trends_enhanced[date_key]) - 1, -1, DAY)
    )
RETURN
    DIVIDE(CurrentPeriod - PreviousPeriod, PreviousPeriod)

// Data Quality Score
Data Quality = 
DIVIDE(
    CALCULATE(COUNT(vw_recent_articles[article_id]), vw_recent_articles[confidence_category] = "High"),
    COUNT(vw_recent_articles[article_id])
)
```

### **7.3 Drill-Through Pages**
Create drill-through from summary to detail:
1. Create new page for article details
2. Add `topic_id` to drill-through filters
3. Add detailed visuals for selected topic

## 🛠️ **Troubleshooting**

### **Common Issues**

**Connection Timeout:**
```
Error: "Query timeout expired"
Solution: 
- Increase command timeout in connection string
- Optimize SQL views with better indexing
- Add WHERE clauses to limit data
```

**Performance Issues:**
```
Problem: Slow visual loading
Solutions:
- Use aggregated views instead of base tables
- Add date filters to limit data range
- Enable query caching in SQL Server
- Use columnstore indexes for large tables
```

**Gateway Issues:**
```
Problem: "Can't reach the data source"
Solutions:
- Check gateway service is running
- Verify SQL Server connectivity from gateway machine
- Update data source credentials
- Check firewall settings
```

**DirectQuery Limitations:**
```
Limitations:
- No calculated columns in DirectQuery mode
- Limited DAX functions available
- No data import/transformation
- Requires constant SQL Server connection

Workarounds:
- Create calculated fields in SQL views
- Use measures instead of calculated columns
- Pre-aggregate data in SQL Server
```

## 📈 **Performance Best Practices**

### **SQL Server Optimization**
```sql
-- Add covering indexes for common queries
CREATE INDEX IX_articles_published_topic ON articles(published_at, topic_id) 
INCLUDE (title, source_name, confidence)

-- Update statistics regularly
UPDATE STATISTICS articles
UPDATE STATISTICS article_topics

-- Consider columnstore for large tables
CREATE CLUSTERED COLUMNSTORE INDEX CCI_article_topics ON article_topics
```

### **Power BI Optimization**
- Use **Import mode** for small lookup tables
- Use **Composite models** for mixed scenarios
- Enable **Automatic aggregations** in Power BI Premium
- Use **Query caching** for repeated queries

### **Network Optimization**
- Use **SQL Server compression** for network traffic
- Configure **connection pooling** properly
- Use **VPN** for secure remote connections
- Monitor **network latency** between Power BI and SQL Server

## 🔒 **Security Considerations**

### **SQL Server Security**
- Use **least privilege** principle for Power BI service account
- Enable **SQL Server auditing** for data access
- Use **encrypted connections** (SSL/TLS)
- Implement **row-level security** if needed

### **Power BI Security**
- Configure **workspace security** properly
- Use **app permissions** for end users
- Enable **sensitivity labels** for data classification
- Monitor **usage metrics** and access logs

---

## 🎉 **Congratulations!**

You now have a fully functional real-time Power BI dashboard connected to SQL Server via DirectQuery. Your dashboard will automatically reflect the latest data from your topic modeling pipeline without any manual refresh needed.

**Next Steps:**
1. Test the dashboard with live data
2. Share with your team via Power BI Service
3. Set up monitoring and alerts
4. Optimize performance based on usage patterns

**Support:**
- Check SQL Server logs: `logs/pipeline.log`
- Monitor pipeline health: `python monitor_pipeline.py check`
- Test SQL connection: `python database/sql_connection.py`
