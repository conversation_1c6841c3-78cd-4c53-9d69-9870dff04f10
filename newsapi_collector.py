#!/usr/bin/env python3
"""
Simple NewsAPI Collector
Step 1: [NewsAPI Request] → [Parse JSON]
"""

import requests
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsAPICollector:
    """Simple NewsAPI collector that fetches and parses news articles."""
    
    def __init__(self, api_key: str):
        """
        Initialize NewsAPI collector.
        
        Args:
            api_key: NewsAPI key from https://newsapi.org/
        """
        self.api_key = api_key
        self.base_url = "https://newsapi.org/v2/everything"
        
    def fetch_articles(self, 
                      keywords: List[str] = None, 
                      hours_back: int = 1,
                      max_articles: int = 100) -> List[Dict]:
        """
        Fetch articles from NewsAPI.
        
        Args:
            keywords: List of keywords to search for
            hours_back: How many hours back to search
            max_articles: Maximum number of articles to fetch
            
        Returns:
            List of article dictionaries
        """
        
        if keywords is None:
            keywords = ["technology", "politics", "business", "economy"]
        
        # Calculate time range
        to_time = datetime.now()
        from_time = to_time - timedelta(hours=hours_back)
        
        # Build query
        query = " OR ".join(keywords)
        
        # API parameters
        params = {
            'q': query,
            'apiKey': self.api_key,
            'language': 'en',
            'sortBy': 'publishedAt',
            'from': from_time.isoformat(),
            'to': to_time.isoformat(),
            'pageSize': min(max_articles, 100)  # API limit is 100 per request
        }
        
        logger.info(f"🔍 Fetching articles with keywords: {keywords}")
        logger.info(f"📅 Time range: {from_time.strftime('%Y-%m-%d %H:%M')} to {to_time.strftime('%Y-%m-%d %H:%M')}")
        
        try:
            # Make API request
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Parse JSON response
            data = response.json()
            
            if data['status'] != 'ok':
                logger.error(f"❌ API Error: {data.get('message', 'Unknown error')}")
                return []
            
            articles = data.get('articles', [])
            logger.info(f"✅ Fetched {len(articles)} articles")
            
            # Clean and structure articles
            cleaned_articles = []
            for article in articles:
                cleaned_article = self._clean_article(article)
                if cleaned_article:
                    cleaned_articles.append(cleaned_article)
            
            logger.info(f"📄 Cleaned {len(cleaned_articles)} valid articles")
            return cleaned_articles
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Request failed: {e}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON parsing failed: {e}")
            return []
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
            return []
    
    def _clean_article(self, article: Dict) -> Optional[Dict]:
        """
        Clean and structure article data.
        
        Args:
            article: Raw article from API
            
        Returns:
            Cleaned article dictionary or None if invalid
        """
        
        # Required fields
        title = article.get('title', '').strip()
        description = article.get('description', '').strip()
        content = article.get('content', '').strip()
        
        # Skip articles without meaningful content
        if not title or len(title) < 10:
            return None
        
        # Combine text content
        full_text = f"{title}. {description}. {content}".strip()
        
        # Skip very short articles
        if len(full_text) < 50:
            return None
        
        # Structure cleaned article
        cleaned = {
            'title': title,
            'description': description,
            'content': content,
            'full_text': full_text,
            'source': article.get('source', {}).get('name', 'Unknown'),
            'author': article.get('author', 'Unknown'),
            'url': article.get('url', ''),
            'published_at': article.get('publishedAt', ''),
            'collected_at': datetime.now().isoformat()
        }
        
        return cleaned

def main():
    """Test the NewsAPI collector."""
    
    # Load API key from environment
    api_key = os.getenv('NEWS_API_KEY')
    if not api_key:
        logger.error("❌ NEWS_API_KEY environment variable not set")
        return
    
    # Create collector
    collector = NewsAPICollector(api_key)
    
    # Test fetch
    articles = collector.fetch_articles(
        keywords=["artificial intelligence", "technology", "politics"],
        hours_back=2,
        max_articles=20
    )
    
    if articles:
        logger.info(f"🎉 Successfully collected {len(articles)} articles")
        
        # Show sample article
        sample = articles[0]
        logger.info(f"📰 Sample article: {sample['title'][:50]}...")
        logger.info(f"📝 Content length: {len(sample['full_text'])} characters")
    else:
        logger.warning("⚠️ No articles collected")

if __name__ == "__main__":
    main()
