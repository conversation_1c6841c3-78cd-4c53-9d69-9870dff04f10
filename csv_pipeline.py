#!/usr/bin/env python3
"""
CSV Data Pipeline
Step 2: [Parse JSON] → [Append to CSV]
"""

import pandas as pd
import os
from datetime import datetime
from typing import List, Dict
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CSVPipeline:
    """Simple CSV pipeline for storing and managing article data."""
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize CSV pipeline.
        
        Args:
            data_dir: Directory to store CSV files
        """
        self.data_dir = data_dir
        self.raw_articles_file = os.path.join(data_dir, "raw_articles.csv")
        self.processed_articles_file = os.path.join(data_dir, "processed_articles.csv")
        
        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Initialize CSV files with headers if they don't exist
        self._initialize_csv_files()
    
    def _initialize_csv_files(self):
        """Initialize CSV files with proper headers."""
        
        # Raw articles CSV headers
        raw_headers = [
            'title', 'description', 'content', 'full_text', 'source', 
            'author', 'url', 'published_at', 'collected_at'
        ]
        
        # Processed articles CSV headers (includes topic classification)
        processed_headers = raw_headers + [
            'topic_id', 'topic_label', 'topic_confidence', 'classified_at'
        ]
        
        # Create raw articles file if it doesn't exist
        if not os.path.exists(self.raw_articles_file):
            pd.DataFrame(columns=raw_headers).to_csv(self.raw_articles_file, index=False)
            logger.info(f"📄 Created raw articles CSV: {self.raw_articles_file}")
        
        # Create processed articles file if it doesn't exist
        if not os.path.exists(self.processed_articles_file):
            pd.DataFrame(columns=processed_headers).to_csv(self.processed_articles_file, index=False)
            logger.info(f"📄 Created processed articles CSV: {self.processed_articles_file}")
    
    def append_raw_articles(self, articles: List[Dict]) -> int:
        """
        Append new articles to raw CSV file.
        
        Args:
            articles: List of article dictionaries from NewsAPI
            
        Returns:
            Number of articles successfully appended
        """
        
        if not articles:
            logger.warning("⚠️ No articles to append")
            return 0
        
        try:
            # Convert articles to DataFrame
            df_new = pd.DataFrame(articles)
            
            # Load existing articles to check for duplicates
            if os.path.exists(self.raw_articles_file):
                df_existing = pd.read_csv(self.raw_articles_file)
                
                # Remove duplicates based on URL
                if not df_existing.empty:
                    df_new = df_new[~df_new['url'].isin(df_existing['url'])]
            
            if df_new.empty:
                logger.info("ℹ️ No new articles to append (all duplicates)")
                return 0
            
            # Append to CSV file
            df_new.to_csv(self.raw_articles_file, mode='a', header=False, index=False)
            
            logger.info(f"✅ Appended {len(df_new)} new articles to {self.raw_articles_file}")
            return len(df_new)
            
        except Exception as e:
            logger.error(f"❌ Failed to append articles: {e}")
            return 0
    
    def get_unprocessed_articles(self) -> pd.DataFrame:
        """
        Get articles that haven't been processed by LDA yet.
        
        Returns:
            DataFrame of unprocessed articles
        """
        
        try:
            # Load raw articles
            if not os.path.exists(self.raw_articles_file):
                logger.warning("⚠️ No raw articles file found")
                return pd.DataFrame()
            
            df_raw = pd.read_csv(self.raw_articles_file)
            
            if df_raw.empty:
                logger.info("ℹ️ No raw articles found")
                return pd.DataFrame()
            
            # Load processed articles
            if not os.path.exists(self.processed_articles_file):
                logger.info("ℹ️ No processed articles file found - all articles are unprocessed")
                return df_raw
            
            df_processed = pd.read_csv(self.processed_articles_file)
            
            if df_processed.empty:
                logger.info("ℹ️ No processed articles found - all articles are unprocessed")
                return df_raw
            
            # Find unprocessed articles (not in processed file)
            processed_urls = set(df_processed['url'].tolist())
            df_unprocessed = df_raw[~df_raw['url'].isin(processed_urls)]
            
            logger.info(f"📊 Found {len(df_unprocessed)} unprocessed articles")
            return df_unprocessed
            
        except Exception as e:
            logger.error(f"❌ Failed to get unprocessed articles: {e}")
            return pd.DataFrame()
    
    def append_processed_articles(self, articles_with_topics: List[Dict]) -> int:
        """
        Append articles with topic classifications to processed CSV.
        
        Args:
            articles_with_topics: List of articles with topic information
            
        Returns:
            Number of articles successfully appended
        """
        
        if not articles_with_topics:
            logger.warning("⚠️ No processed articles to append")
            return 0
        
        try:
            # Convert to DataFrame
            df_processed = pd.DataFrame(articles_with_topics)
            
            # Add classification timestamp
            df_processed['classified_at'] = datetime.now().isoformat()
            
            # Append to processed CSV file
            df_processed.to_csv(self.processed_articles_file, mode='a', header=False, index=False)
            
            logger.info(f"✅ Appended {len(df_processed)} processed articles to {self.processed_articles_file}")
            return len(df_processed)
            
        except Exception as e:
            logger.error(f"❌ Failed to append processed articles: {e}")
            return 0
    
    def get_recent_articles(self, hours: int = 24) -> pd.DataFrame:
        """
        Get articles from the last N hours.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            DataFrame of recent articles
        """
        
        try:
            if not os.path.exists(self.processed_articles_file):
                logger.warning("⚠️ No processed articles file found")
                return pd.DataFrame()
            
            df = pd.read_csv(self.processed_articles_file)
            
            if df.empty:
                return df
            
            # Convert timestamp to datetime
            df['published_at'] = pd.to_datetime(df['published_at'])
            
            # Filter recent articles
            cutoff_time = datetime.now() - pd.Timedelta(hours=hours)
            df_recent = df[df['published_at'] >= cutoff_time]
            
            logger.info(f"📊 Found {len(df_recent)} articles from last {hours} hours")
            return df_recent
            
        except Exception as e:
            logger.error(f"❌ Failed to get recent articles: {e}")
            return pd.DataFrame()
    
    def get_stats(self) -> Dict:
        """
        Get pipeline statistics.
        
        Returns:
            Dictionary with pipeline stats
        """
        
        stats = {
            'raw_articles': 0,
            'processed_articles': 0,
            'unprocessed_articles': 0
        }
        
        try:
            # Count raw articles
            if os.path.exists(self.raw_articles_file):
                df_raw = pd.read_csv(self.raw_articles_file)
                stats['raw_articles'] = len(df_raw)
            
            # Count processed articles
            if os.path.exists(self.processed_articles_file):
                df_processed = pd.read_csv(self.processed_articles_file)
                stats['processed_articles'] = len(df_processed)
            
            # Calculate unprocessed
            stats['unprocessed_articles'] = stats['raw_articles'] - stats['processed_articles']
            
        except Exception as e:
            logger.error(f"❌ Failed to get stats: {e}")
        
        return stats

def main():
    """Test the CSV pipeline."""
    
    # Create pipeline
    pipeline = CSVPipeline()
    
    # Test with sample data
    sample_articles = [
        {
            'title': 'Test Article 1',
            'description': 'This is a test article about technology',
            'content': 'Full content of the test article...',
            'full_text': 'Test Article 1. This is a test article about technology. Full content...',
            'source': 'TechNews',
            'author': 'Test Author',
            'url': 'https://example.com/article1',
            'published_at': datetime.now().isoformat(),
            'collected_at': datetime.now().isoformat()
        }
    ]
    
    # Test append
    count = pipeline.append_raw_articles(sample_articles)
    logger.info(f"📊 Appended {count} test articles")
    
    # Get stats
    stats = pipeline.get_stats()
    logger.info(f"📈 Pipeline stats: {stats}")

if __name__ == "__main__":
    main()
