-- =====================================================
-- MySQL Schema for Real-Time Topic Modeling
-- Compatible with MySQL Workbench
-- =====================================================

-- Create database (run this first)
CREATE DATABASE IF NOT EXISTS TopicModelingDB;
USE TopicModelingDB;

-- =====================================================
-- 1. CORE TABLES (MySQL Compatible)
-- =====================================================

-- Raw articles table (stores original JSON response from NewsAPI)
CREATE TABLE raw_articles (
    article_id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    content LONGTEXT,
    source_name VARCHAR(100) NOT NULL,
    source_id VARCHAR(50),
    author VARCHAR(200),
    url VARCHAR(1000) UNIQUE,
    url_to_image VARCHAR(1000),
    published_at DATETIME NOT NULL,
    fetched_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    content_hash VARCHAR(64) UNIQUE,
    language VARCHAR(10) DEFAULT 'en',
    raw_json LONGTEXT, -- Store original JSON response
    processing_status VARCHAR(20) DEFAULT 'pending', -- pending, processed, failed
    
    -- Indexing for performance and timestamp-based filtering
    INDEX IX_raw_articles_published_at (published_at DESC),
    INDEX IX_raw_articles_fetched_at (fetched_at DESC),
    INDEX IX_raw_articles_content_hash (content_hash),
    INDEX IX_raw_articles_status (processing_status),
    INDEX IX_raw_articles_timestamp_filter (published_at DESC, processing_status)
);

-- Preprocessed articles table (stores cleaned and processed text)
CREATE TABLE preprocessed_articles (
    preprocessed_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id VARCHAR(50) NOT NULL,
    cleaned_text LONGTEXT NOT NULL,
    tokens LONGTEXT, -- JSON array of tokens
    lemmatized_text LONGTEXT,
    word_count INT,
    sentence_count INT,
    language_detected VARCHAR(10),
    preprocessing_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    preprocessing_duration_ms INT,
    
    -- Foreign key to raw articles
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    
    -- Unique constraint - one preprocessing per article
    UNIQUE KEY UQ_preprocessed_article (article_id),
    
    -- Indexing for performance
    INDEX IX_preprocessed_articles_timestamp (preprocessing_timestamp DESC),
    INDEX IX_preprocessed_articles_word_count (word_count DESC)
);

-- Topics master table
CREATE TABLE topics (
    topic_id INT PRIMARY KEY,
    topic_label VARCHAR(100) NOT NULL,
    topic_keywords LONGTEXT, -- JSON array of keywords
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX IX_topics_label (topic_label)
);

-- Topic results table (stores LDA model output)
CREATE TABLE topic_results (
    result_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id VARCHAR(50) NOT NULL,
    preprocessed_id BIGINT NOT NULL,
    topic_id INT NOT NULL,
    confidence DECIMAL(8,6) NOT NULL, -- Higher precision for confidence scores
    topic_distribution LONGTEXT, -- JSON array of all topic probabilities
    topic_keywords LONGTEXT, -- JSON array of top keywords for this topic
    classification_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    model_version VARCHAR(20) DEFAULT '1.0',
    processing_duration_ms INT,
    
    -- Foreign keys
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (preprocessed_id) REFERENCES preprocessed_articles(preprocessed_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint - one classification per preprocessed article
    UNIQUE KEY UQ_topic_result_preprocessed (preprocessed_id),
    
    -- Indexing for performance and DirectQuery optimization
    INDEX IX_topic_results_topic_id (topic_id),
    INDEX IX_topic_results_confidence (confidence DESC),
    INDEX IX_topic_results_timestamp (classification_timestamp DESC),
    INDEX IX_topic_results_article_topic (article_id, topic_id),
    INDEX IX_topic_results_directquery (classification_timestamp DESC, topic_id, confidence DESC)
);

-- =====================================================
-- 2. SAMPLE TOPICS (Insert some default topics)
-- =====================================================

INSERT INTO topics (topic_id, topic_label, topic_keywords) VALUES
(0, 'Technology', '["technology", "tech", "innovation", "digital", "software", "ai", "artificial intelligence"]'),
(1, 'Politics', '["politics", "government", "election", "policy", "political", "congress", "senate"]'),
(2, 'Business', '["business", "economy", "market", "financial", "company", "corporate", "industry"]'),
(3, 'Health', '["health", "medical", "healthcare", "medicine", "hospital", "doctor", "patient"]'),
(4, 'Science', '["science", "research", "study", "scientific", "discovery", "experiment", "analysis"]'),
(5, 'Sports', '["sports", "game", "team", "player", "championship", "league", "tournament"]'),
(6, 'Entertainment', '["entertainment", "movie", "music", "celebrity", "film", "show", "actor"]'),
(7, 'Environment', '["environment", "climate", "green", "sustainability", "pollution", "renewable", "carbon"]');

-- =====================================================
-- 3. AGGREGATED TABLES FOR PERFORMANCE
-- =====================================================

-- Daily topic trends (pre-aggregated for performance)
CREATE TABLE topic_trends_daily (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    date_key DATE NOT NULL,
    topic_id INT NOT NULL,
    topic_label VARCHAR(100) NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(8,6) NOT NULL,
    confidence_std DECIMAL(8,6) DEFAULT 0,
    min_confidence DECIMAL(8,6) NOT NULL,
    max_confidence DECIMAL(8,6) NOT NULL,
    percentage_of_day DECIMAL(5,2) DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint
    UNIQUE KEY UQ_daily_trends_date_topic (date_key, topic_id),
    
    -- Indexing for DirectQuery performance
    INDEX IX_daily_trends_date_topic (date_key DESC, topic_id),
    INDEX IX_daily_trends_topic_date (topic_id, date_key DESC),
    INDEX IX_daily_trends_article_count (article_count DESC)
);

-- Hourly topic trends (for real-time analysis)
CREATE TABLE topic_trends_hourly (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    hour_bucket DATETIME NOT NULL,
    topic_id INT NOT NULL,
    topic_label VARCHAR(100) NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(8,6) NOT NULL,
    unique_sources INT DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint
    UNIQUE KEY UQ_hourly_trends_hour_topic (hour_bucket, topic_id),
    
    -- Indexing for real-time queries
    INDEX IX_hourly_trends_hour_desc (hour_bucket DESC, topic_id),
    INDEX IX_hourly_trends_topic_hour (topic_id, hour_bucket DESC)
);

-- =====================================================
-- 4. PIPELINE STATUS TRACKING
-- =====================================================

-- Pipeline execution log
CREATE TABLE pipeline_execution_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    execution_start DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    execution_end DATETIME,
    status VARCHAR(20) NOT NULL, -- running, completed, failed
    articles_fetched INT DEFAULT 0,
    articles_processed INT DEFAULT 0,
    articles_classified INT DEFAULT 0,
    duration_seconds INT,
    error_message TEXT,
    
    INDEX IX_pipeline_log_start (execution_start DESC),
    INDEX IX_pipeline_log_status (status)
);

-- =====================================================
-- 5. VERIFICATION QUERIES
-- =====================================================

-- Check if tables were created successfully
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'TopicModelingDB'
ORDER BY TABLE_NAME;

-- Show table structure
SHOW TABLES;

-- Verify topics were inserted
SELECT * FROM topics ORDER BY topic_id;
