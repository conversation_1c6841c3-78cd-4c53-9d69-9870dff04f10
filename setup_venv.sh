#!/bin/bash
# =====================================================
# Virtual Environment Setup for Topic Modeling Pipeline
# Run this script to create and configure your virtual environment
# =====================================================

echo ""
echo "========================================"
echo "  Topic Modeling Pipeline Setup"
echo "========================================"
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8+ and try again"
    exit 1
fi

echo "✅ Python found:"
python3 --version

# Create virtual environment
echo ""
echo "📦 Creating virtual environment..."
if [ -d "topic_modeling_env" ]; then
    echo "⚠️  Virtual environment already exists"
    read -p "Do you want to recreate it? (y/n): " choice
    if [[ $choice == [Yy]* ]]; then
        echo "🗑️  Removing existing environment..."
        rm -rf topic_modeling_env
    else
        echo "📂 Using existing environment"
    fi
fi

if [ ! -d "topic_modeling_env" ]; then
    python3 -m venv topic_modeling_env
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create virtual environment"
        exit 1
    fi
    echo "✅ Virtual environment created"
fi

# Activate virtual environment
echo ""
echo "🔄 Activating virtual environment..."
source topic_modeling_env/bin/activate
if [ $? -ne 0 ]; then
    echo "❌ Failed to activate virtual environment"
    exit 1
fi

# Upgrade pip
echo ""
echo "⬆️  Upgrading pip..."
python -m pip install --upgrade pip

# Install requirements
echo ""
echo "📥 Installing dependencies from requirements.txt..."
echo "This may take a few minutes..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install some dependencies"
    echo "Check the error messages above"
    exit 1
fi

# Test installation
echo ""
echo "🧪 Testing installation..."
python -c "import pandas, numpy, sklearn, pymysql, sqlalchemy; print('✅ Core packages imported successfully')"
if [ $? -ne 0 ]; then
    echo "❌ Package import test failed"
    exit 1
fi

# Test database connection (if config exists)
echo ""
echo "🔗 Testing database connection..."
if [ -f "config/database.json" ]; then
    cd database
    python test_connection.py
    cd ..
else
    echo "⚠️  Database config not found (config/database.json)"
    echo "Please configure your MySQL connection before running the pipeline"
fi

echo ""
echo "========================================"
echo "  🎉 Setup Complete!"
echo "========================================"
echo ""
echo "Next steps:"
echo "1. Configure MySQL: Edit config/database.json"
echo "2. Set NewsAPI key: export NEWS_API_KEY=your_key"
echo "3. Add LDA model: Place .pkl file in models/ folder"
echo "4. Test pipeline: python automated_pipeline.py --single-run"
echo ""
echo "To activate environment manually:"
echo "  source topic_modeling_env/bin/activate"
echo ""
echo "To deactivate:"
echo "  deactivate"
echo ""
