"""
News data ingestion module for collecting articles from various sources.
Supports NewsAPI and RSS feeds with configurable filters.
"""

import requests
import feedparser
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path
import time
import logging
from urllib.parse import urlencode

from .config import config

# Set up logging
logging.basicConfig(level=getattr(logging, config.get('logging.level', 'INFO')))
logger = logging.getLogger(__name__)

class NewsAPICollector:
    """Collect news articles from NewsAPI."""
    
    def __init__(self):
        """Initialize NewsAPI collector."""
        self.api_key = config.get('news_api.api_key')
        self.base_url = config.get('news_api.base_url')
        self.session = requests.Session()
        
        if not self.api_key or self.api_key == "YOUR_NEWS_API_KEY_HERE":
            logger.warning("NewsAPI key not configured. RSS feeds will be used instead.")
    
    def fetch_articles(self, 
                      query_keywords: List[str] = None,
                      language: str = 'en',
                      sort_by: str = 'publishedAt',
                      page_size: int = 100,
                      max_pages: int = 5,
                      from_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Fetch articles from NewsAPI.
        
        Args:
            query_keywords: List of keywords to search for
            language: Language code (e.g., 'en')
            sort_by: Sort order ('publishedAt', 'relevancy', 'popularity')
            page_size: Number of articles per page
            max_pages: Maximum number of pages to fetch
            from_date: Start date in YYYY-MM-DD format
            
        Returns:
            List of article dictionaries
        """
        if not self.api_key or self.api_key == "YOUR_NEWS_API_KEY_HERE":
            logger.error("NewsAPI key not configured")
            return []
        
        articles = []
        query_keywords = query_keywords or config.get('news_api.query_keywords', [])
        
        # Build query string
        query = ' OR '.join(f'"{keyword}"' for keyword in query_keywords)
        
        # Set default from_date to last 7 days if not provided
        if not from_date:
            from_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        for page in range(1, max_pages + 1):
            params = {
                'q': query,
                'language': language,
                'sortBy': sort_by,
                'pageSize': page_size,
                'page': page,
                'from': from_date,
                'apiKey': self.api_key
            }
            
            try:
                logger.info(f"Fetching page {page} from NewsAPI...")
                response = self.session.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if data.get('status') != 'ok':
                    logger.error(f"NewsAPI error: {data.get('message', 'Unknown error')}")
                    break
                
                page_articles = data.get('articles', [])
                if not page_articles:
                    logger.info("No more articles found")
                    break
                
                articles.extend(page_articles)
                logger.info(f"Fetched {len(page_articles)} articles from page {page}")
                
                # Rate limiting - NewsAPI allows 1000 requests per day for free tier
                time.sleep(0.1)
                
            except requests.exceptions.RequestException as e:
                logger.error(f"Error fetching page {page}: {e}")
                break
            except Exception as e:
                logger.error(f"Unexpected error on page {page}: {e}")
                break
        
        logger.info(f"Total articles fetched from NewsAPI: {len(articles)}")
        return articles

class RSSCollector:
    """Collect news articles from RSS feeds."""
    
    def __init__(self):
        """Initialize RSS collector."""
        self.rss_feeds = config.get('rss_feeds', [])
    
    def fetch_articles(self, max_articles_per_feed: int = 50) -> List[Dict[str, Any]]:
        """
        Fetch articles from RSS feeds.
        
        Args:
            max_articles_per_feed: Maximum articles to fetch per feed
            
        Returns:
            List of article dictionaries
        """
        articles = []
        
        for feed_url in self.rss_feeds:
            try:
                logger.info(f"Fetching from RSS feed: {feed_url}")
                feed = feedparser.parse(feed_url)
                
                if feed.bozo:
                    logger.warning(f"RSS feed may have issues: {feed_url}")
                
                feed_articles = []
                for entry in feed.entries[:max_articles_per_feed]:
                    article = {
                        'title': entry.get('title', ''),
                        'description': entry.get('description', ''),
                        'content': entry.get('content', [{}])[0].get('value', '') if entry.get('content') else entry.get('description', ''),
                        'url': entry.get('link', ''),
                        'publishedAt': self._parse_date(entry.get('published', '')),
                        'source': {'name': feed.feed.get('title', feed_url)},
                        'author': entry.get('author', ''),
                        'urlToImage': entry.get('media_thumbnail', [{}])[0].get('url', '') if entry.get('media_thumbnail') else ''
                    }
                    feed_articles.append(article)
                
                articles.extend(feed_articles)
                logger.info(f"Fetched {len(feed_articles)} articles from {feed_url}")
                
            except Exception as e:
                logger.error(f"Error fetching RSS feed {feed_url}: {e}")
        
        logger.info(f"Total articles fetched from RSS feeds: {len(articles)}")
        return articles
    
    def _parse_date(self, date_str: str) -> str:
        """Parse date string to ISO format."""
        if not date_str:
            return datetime.now().isoformat()
        
        try:
            # Try parsing common RSS date formats
            for fmt in ['%a, %d %b %Y %H:%M:%S %z', '%Y-%m-%dT%H:%M:%S%z', '%Y-%m-%d %H:%M:%S']:
                try:
                    dt = datetime.strptime(date_str.strip(), fmt)
                    return dt.isoformat()
                except ValueError:
                    continue
            
            # If all formats fail, return current time
            logger.warning(f"Could not parse date: {date_str}")
            return datetime.now().isoformat()
            
        except Exception:
            return datetime.now().isoformat()

class NewsDataIngestion:
    """Main class for news data ingestion from multiple sources."""
    
    def __init__(self):
        """Initialize news data ingestion."""
        self.newsapi_collector = NewsAPICollector()
        self.rss_collector = RSSCollector()
        self.output_dir = Path(config.get('output.data_dir', 'data'))
        self.output_dir.mkdir(exist_ok=True)
    
    def collect_articles(self, use_newsapi: bool = True, use_rss: bool = True) -> pd.DataFrame:
        """
        Collect articles from all configured sources.
        
        Args:
            use_newsapi: Whether to use NewsAPI
            use_rss: Whether to use RSS feeds
            
        Returns:
            DataFrame with collected articles
        """
        all_articles = []
        
        # Collect from NewsAPI
        if use_newsapi:
            try:
                newsapi_articles = self.newsapi_collector.fetch_articles()
                all_articles.extend(newsapi_articles)
            except Exception as e:
                logger.error(f"Error collecting from NewsAPI: {e}")
        
        # Collect from RSS feeds
        if use_rss:
            try:
                rss_articles = self.rss_collector.fetch_articles()
                all_articles.extend(rss_articles)
            except Exception as e:
                logger.error(f"Error collecting from RSS feeds: {e}")
        
        if not all_articles:
            logger.warning("No articles collected from any source")
            return pd.DataFrame()
        
        # Convert to DataFrame and clean
        df = pd.DataFrame(all_articles)
        df = self._clean_dataframe(df)
        
        logger.info(f"Total articles collected: {len(df)}")
        return df
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize the articles DataFrame."""
        if df.empty:
            return df
        
        # Remove duplicates based on title and URL
        df = df.drop_duplicates(subset=['title', 'url'], keep='first')
        
        # Remove articles with missing essential fields
        df = df.dropna(subset=['title'])
        
        # Clean text fields
        text_columns = ['title', 'description', 'content']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
                df[col] = df[col].replace('nan', '')
        
        # Standardize date format
        if 'publishedAt' in df.columns:
            df['publishedAt'] = pd.to_datetime(df['publishedAt'], errors='coerce')
        
        # Extract source name
        if 'source' in df.columns:
            df['source_name'] = df['source'].apply(
                lambda x: x.get('name', '') if isinstance(x, dict) else str(x)
            )
        
        # Sort by publication date
        if 'publishedAt' in df.columns:
            df = df.sort_values('publishedAt', ascending=False)
        
        return df
    
    def save_articles(self, df: pd.DataFrame, filename: str = None) -> str:
        """
        Save articles to CSV file.
        
        Args:
            df: DataFrame with articles
            filename: Output filename (optional)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            filename = config.get('output.raw_articles_file', 'raw_articles.csv')
        
        output_path = self.output_dir / filename
        
        # Add timestamp to filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        name_parts = output_path.stem, timestamp, output_path.suffix
        timestamped_path = output_path.parent / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
        
        df.to_csv(output_path, index=False, encoding='utf-8')
        df.to_csv(timestamped_path, index=False, encoding='utf-8')
        
        logger.info(f"Articles saved to: {output_path}")
        logger.info(f"Timestamped backup saved to: {timestamped_path}")
        
        return str(output_path)

def main():
    """Main function for running data ingestion."""
    logger.info("Starting news data ingestion...")
    
    ingestion = NewsDataIngestion()
    articles_df = ingestion.collect_articles()
    
    if not articles_df.empty:
        output_path = ingestion.save_articles(articles_df)
        logger.info(f"Data ingestion completed. {len(articles_df)} articles saved to {output_path}")
    else:
        logger.warning("No articles collected")

if __name__ == "__main__":
    main()
