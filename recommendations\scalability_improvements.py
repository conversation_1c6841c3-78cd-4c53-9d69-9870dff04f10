#!/usr/bin/env python3
"""
Scalability Improvements for Topic Modeling Pipeline
Advanced optimizations for high-throughput real-time processing
"""

import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from typing import List, Dict, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ScalableNewsIngestion:
    """
    High-throughput news ingestion with async processing and rate limiting.
    """
    
    def __init__(self, max_concurrent_requests: int = 10, rate_limit_per_second: int = 5):
        self.max_concurrent = max_concurrent_requests
        self.rate_limit = rate_limit_per_second
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
        
    async def fetch_news_async(self, sources: List[str], api_key: str) -> List[Dict]:
        """
        Async news fetching with rate limiting and error handling.
        
        Improvements:
        - Concurrent API calls for multiple sources
        - Exponential backoff for rate limiting
        - Circuit breaker pattern for failed sources
        """
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            
            for source in sources:
                task = self._fetch_source_with_semaphore(session, source, api_key)
                tasks.append(task)
            
            # Process with rate limiting
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter successful results
            articles = []
            for result in results:
                if isinstance(result, list):
                    articles.extend(result)
                else:
                    logger.error(f"Source fetch failed: {result}")
            
            return articles
    
    async def _fetch_source_with_semaphore(self, session, source: str, api_key: str):
        """Fetch single source with semaphore control."""
        
        async with self.semaphore:
            # Add jitter to prevent thundering herd
            await asyncio.sleep(np.random.uniform(0, 1/self.rate_limit))
            
            try:
                url = f"https://newsapi.org/v2/everything?sources={source}&apiKey={api_key}"
                async with session.get(url, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('articles', [])
                    else:
                        logger.warning(f"API returned status {response.status} for source {source}")
                        return []
            except Exception as e:
                logger.error(f"Error fetching {source}: {e}")
                return []

class ParallelTextProcessor:
    """
    Multi-process text preprocessing for high-throughput processing.
    """
    
    def __init__(self, num_processes: int = None):
        self.num_processes = num_processes or mp.cpu_count()
        
    def process_articles_parallel(self, articles: List[Dict]) -> pd.DataFrame:
        """
        Process articles using multiple processes for CPU-intensive NLP tasks.
        
        Improvements:
        - Parallel spaCy processing
        - Chunked processing for memory efficiency
        - Progress tracking for large batches
        """
        
        # Split articles into chunks for parallel processing
        chunk_size = max(1, len(articles) // self.num_processes)
        chunks = [articles[i:i + chunk_size] for i in range(0, len(articles), chunk_size)]
        
        with ProcessPoolExecutor(max_workers=self.num_processes) as executor:
            # Process chunks in parallel
            futures = [executor.submit(self._process_chunk, chunk) for chunk in chunks]
            
            # Collect results
            processed_chunks = []
            for future in futures:
                try:
                    result = future.result(timeout=300)  # 5 minute timeout
                    processed_chunks.append(result)
                except Exception as e:
                    logger.error(f"Chunk processing failed: {e}")
        
        # Combine results
        if processed_chunks:
            return pd.concat(processed_chunks, ignore_index=True)
        else:
            return pd.DataFrame()
    
    @staticmethod
    def _process_chunk(articles_chunk: List[Dict]) -> pd.DataFrame:
        """Process a chunk of articles in a separate process."""
        
        # Import spaCy in worker process to avoid serialization issues
        import spacy
        from preprocess import AdvancedTextPreprocessor
        
        try:
            # Load spaCy model in worker
            nlp = spacy.load("en_core_web_sm")
            preprocessor = AdvancedTextPreprocessor()
            
            processed_articles = []
            
            for article in articles_chunk:
                try:
                    processed = preprocessor.process_article(article)
                    processed_articles.append(processed)
                except Exception as e:
                    logger.error(f"Article processing failed: {e}")
            
            return pd.DataFrame(processed_articles)
            
        except Exception as e:
            logger.error(f"Chunk processing setup failed: {e}")
            return pd.DataFrame()

class BatchLDAClassifier:
    """
    Optimized batch LDA classification with memory management.
    """
    
    def __init__(self, model_path: str, batch_size: int = 1000):
        self.model_path = model_path
        self.batch_size = batch_size
        self.model = None
        self.vectorizer = None
        
    def load_model(self):
        """Load LDA model with memory optimization."""
        
        import pickle
        import gc
        
        try:
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.vectorizer = model_data['vectorizer']
            
            # Force garbage collection
            gc.collect()
            
            logger.info("LDA model loaded successfully")
            
        except Exception as e:
            logger.error(f"Model loading failed: {e}")
            raise
    
    def classify_batch(self, texts: List[str]) -> List[Dict]:
        """
        Classify texts in batches for memory efficiency.
        
        Improvements:
        - Batch processing to prevent memory overflow
        - Sparse matrix operations for efficiency
        - Memory cleanup between batches
        """
        
        if not self.model or not self.vectorizer:
            self.load_model()
        
        results = []
        
        # Process in batches
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            
            try:
                # Vectorize batch
                batch_vectors = self.vectorizer.transform(batch_texts)
                
                # Get topic probabilities
                topic_probs = self.model.transform(batch_vectors)
                
                # Extract top topics and confidence scores
                for j, probs in enumerate(topic_probs):
                    top_topic = np.argmax(probs)
                    confidence = float(probs[top_topic])
                    
                    results.append({
                        'text_index': i + j,
                        'topic_id': int(top_topic),
                        'confidence': confidence,
                        'all_probabilities': probs.tolist()
                    })
                
                # Memory cleanup
                del batch_vectors, topic_probs
                
            except Exception as e:
                logger.error(f"Batch classification failed: {e}")
                # Add failed classifications with default values
                for j in range(len(batch_texts)):
                    results.append({
                        'text_index': i + j,
                        'topic_id': -1,
                        'confidence': 0.0,
                        'all_probabilities': []
                    })
        
        return results

class HighThroughputSQLExporter:
    """
    Optimized SQL Server export with bulk operations and connection pooling.
    """
    
    def __init__(self, connection_manager, batch_size: int = 5000):
        self.conn_manager = connection_manager
        self.batch_size = batch_size
    
    def bulk_upsert_with_merge(self, df: pd.DataFrame, table_name: str, 
                              key_columns: List[str]) -> int:
        """
        High-performance bulk upsert using SQL Server MERGE statement.
        
        Improvements:
        - MERGE statement for efficient upserts
        - Batch processing for large datasets
        - Optimized data types and indexing
        """
        
        if df.empty:
            return 0
        
        total_processed = 0
        
        # Process in batches
        for i in range(0, len(df), self.batch_size):
            batch_df = df.iloc[i:i + self.batch_size].copy()
            
            try:
                # Create temporary table for batch
                temp_table = f"#{table_name}_temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # Insert batch into temp table
                batch_df.to_sql(
                    temp_table,
                    self.conn_manager.engine,
                    if_exists='replace',
                    index=False,
                    method='multi'
                )
                
                # Execute MERGE statement
                merge_sql = self._generate_merge_sql(table_name, temp_table, key_columns)
                
                with self.conn_manager.get_connection() as conn:
                    result = conn.execute(merge_sql)
                    affected_rows = result.rowcount
                    total_processed += affected_rows
                
                logger.info(f"Processed batch {i//self.batch_size + 1}: {affected_rows} rows")
                
            except Exception as e:
                logger.error(f"Batch upsert failed: {e}")
        
        return total_processed
    
    def _generate_merge_sql(self, target_table: str, source_table: str, 
                           key_columns: List[str]) -> str:
        """Generate optimized MERGE SQL statement."""
        
        # This would generate a proper MERGE statement
        # Implementation depends on your specific table schema
        key_conditions = " AND ".join([f"target.{col} = source.{col}" for col in key_columns])
        
        merge_sql = f"""
        MERGE {target_table} AS target
        USING {source_table} AS source
        ON {key_conditions}
        WHEN MATCHED THEN
            UPDATE SET 
                updated_at = GETDATE(),
                -- Add other columns to update
        WHEN NOT MATCHED THEN
            INSERT (column1, column2, ...)
            VALUES (source.column1, source.column2, ...);
        """
        
        return merge_sql

# Performance monitoring and metrics
class PerformanceMonitor:
    """
    Real-time performance monitoring for pipeline components.
    """
    
    def __init__(self):
        self.metrics = {}
    
    def track_processing_time(self, component: str, processing_time: float):
        """Track processing times for performance analysis."""
        
        if component not in self.metrics:
            self.metrics[component] = []
        
        self.metrics[component].append({
            'timestamp': datetime.now(),
            'processing_time': processing_time
        })
    
    def get_performance_summary(self) -> Dict:
        """Get performance summary for all components."""
        
        summary = {}
        
        for component, times in self.metrics.items():
            if times:
                processing_times = [t['processing_time'] for t in times]
                summary[component] = {
                    'avg_time': np.mean(processing_times),
                    'max_time': np.max(processing_times),
                    'min_time': np.min(processing_times),
                    'total_runs': len(processing_times)
                }
        
        return summary

# Example usage and integration
async def run_scalable_pipeline():
    """Example of how to integrate scalability improvements."""
    
    # Initialize scalable components
    ingestion = ScalableNewsIngestion(max_concurrent_requests=20)
    processor = ParallelTextProcessor(num_processes=8)
    classifier = BatchLDAClassifier("models/lda_model.pkl", batch_size=2000)
    monitor = PerformanceMonitor()
    
    # Fetch news asynchronously
    start_time = datetime.now()
    articles = await ingestion.fetch_news_async(
        sources=['bbc-news', 'cnn', 'reuters'], 
        api_key="your_api_key"
    )
    monitor.track_processing_time('ingestion', (datetime.now() - start_time).total_seconds())
    
    # Process in parallel
    start_time = datetime.now()
    processed_df = processor.process_articles_parallel(articles)
    monitor.track_processing_time('preprocessing', (datetime.now() - start_time).total_seconds())
    
    # Classify in batches
    start_time = datetime.now()
    classifications = classifier.classify_batch(processed_df['cleaned_text'].tolist())
    monitor.track_processing_time('classification', (datetime.now() - start_time).total_seconds())
    
    # Export to SQL Server
    # (Implementation would use HighThroughputSQLExporter)
    
    # Print performance summary
    print("Performance Summary:", monitor.get_performance_summary())

if __name__ == "__main__":
    # Run the scalable pipeline
    asyncio.run(run_scalable_pipeline())
