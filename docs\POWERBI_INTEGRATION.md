# Power BI Integration Guide

This guide explains how to create interactive dashboards in Power BI using the topic modeling pipeline output.

## Overview

The pipeline exports data in Power BI-ready format with three main datasets:
1. **Main Dataset** (`powerbi_data.csv`) - Article-level data with topic assignments
2. **Topic Summary** (`powerbi_topic_summary.csv`) - Aggregated topic statistics
3. **Time Series** (`powerbi_time_series.csv`) - Daily topic trends

## Data Schema

### Main Dataset (`powerbi_data.csv`)

| Column | Type | Description |
|--------|------|-------------|
| Article_Title | Text | Article headline |
| Article_Description | Text | Article summary |
| Article_Content | Text | Full article text |
| Cleaned_Text | Text | Preprocessed text |
| Article_URL | Text | Source URL |
| Published_Date | DateTime | Publication timestamp |
| Source_Name | Text | News source |
| Author | Text | Article author |
| Topic_ID | Number | Assigned topic ID (0-N) |
| Topic_Label | Text | Human-readable topic name |
| Topic_Keywords | Text | Top keywords for topic |
| Topic_Confidence | Number | Confidence score (0-1) |
| Token_Count | Number | Number of processed words |
| Character_Count | Number | Article length in characters |
| Published_Year | Number | Publication year |
| Published_Month | Number | Publication month |
| Published_Day | Number | Publication day |
| Published_Hour | Number | Publication hour |
| Weekday | Text | Day of week |
| Is_Weekend | Boolean | Weekend indicator |
| Content_Length_Category | Text | Short/Medium/Long/Very Long |
| Topic_Strength | Text | Low/Medium/High/Very High |

### Topic Summary (`powerbi_topic_summary.csv`)

| Column | Type | Description |
|--------|------|-------------|
| Topic_ID | Number | Topic identifier |
| Topic_Label | Text | Topic name |
| Topic_Keywords | Text | Key terms |
| Article_Count | Number | Number of articles |
| Topic_Percentage | Number | Percentage of total articles |
| Avg_Confidence | Number | Average confidence score |
| Avg_Content_Length | Number | Average article length |
| Earliest_Article | DateTime | First article date |
| Latest_Article | DateTime | Most recent article date |

### Time Series (`powerbi_time_series.csv`)

| Column | Type | Description |
|--------|------|-------------|
| Date | Date | Daily date |
| Topic_ID | Number | Topic identifier |
| Topic_Label | Text | Topic name |
| Article_Count | Number | Articles published that day |
| Avg_Confidence | Number | Average confidence for the day |

## Setting Up Power BI

### Step 1: Install Power BI Desktop

1. Download from [Microsoft Power BI](https://powerbi.microsoft.com/desktop/)
2. Install and launch Power BI Desktop

### Step 2: Import Data

1. **Get Data**
   - Click "Get Data" → "Text/CSV"
   - Navigate to your project's `data` folder
   - Select `powerbi_data.csv`

2. **Data Preview**
   - Review the data preview
   - Ensure data types are correct
   - Click "Load"

3. **Import Additional Tables**
   - Repeat for `powerbi_topic_summary.csv`
   - Repeat for `powerbi_time_series.csv`

### Step 3: Create Relationships

1. **Open Model View**
   - Click the model icon in the left panel

2. **Create Relationships**
   - Drag `Topic_ID` from main table to `Topic_ID` in summary table
   - Drag `Topic_ID` from main table to `Topic_ID` in time series table
   - Set relationship type to "Many to One"

## Dashboard Visualizations

### 1. Topic Distribution (Pie Chart)

**Data:**
- Values: `Article_Count` (from topic summary)
- Legend: `Topic_Label`

**Purpose:** Show the relative size of each topic

### 2. Topic Trends Over Time (Line Chart)

**Data:**
- Axis: `Date` (from time series)
- Values: `Article_Count`
- Legend: `Topic_Label`

**Purpose:** Track how topics trend over time

### 3. Article Volume by Source (Bar Chart)

**Data:**
- Axis: `Source_Name`
- Values: Count of `Article_Title`

**Purpose:** Show which sources contribute most articles

### 4. Topic Confidence Distribution (Histogram)

**Data:**
- Axis: `Topic_Confidence` (binned)
- Values: Count of articles

**Purpose:** Assess model confidence levels

### 5. Content Length vs Topic (Scatter Plot)

**Data:**
- X-axis: `Character_Count`
- Y-axis: `Topic_Confidence`
- Legend: `Topic_Label`

**Purpose:** Analyze relationship between content length and topic assignment

### 6. Daily Article Volume (Area Chart)

**Data:**
- Axis: `Published_Date`
- Values: Count of articles
- Legend: `Topic_Label` (optional)

**Purpose:** Show overall news volume trends

### 7. Top Keywords by Topic (Table)

**Data:**
- Rows: `Topic_Label`
- Values: `Topic_Keywords`, `Article_Count`, `Topic_Percentage`

**Purpose:** Detailed topic breakdown

### 8. Weekend vs Weekday Analysis (Clustered Bar)

**Data:**
- Axis: `Is_Weekend`
- Values: Count of articles
- Legend: `Topic_Label`

**Purpose:** Analyze publishing patterns

## Advanced Features

### 1. Slicers and Filters

Add slicers for:
- **Date Range**: Filter by publication date
- **Topic**: Focus on specific topics
- **Source**: Filter by news source
- **Content Length**: Filter by article length category

### 2. Calculated Measures

Create custom measures:

**Total Articles:**
```dax
Total Articles = COUNT(powerbi_data[Article_Title])
```

**Average Confidence:**
```dax
Avg Confidence = AVERAGE(powerbi_data[Topic_Confidence])
```

**Articles This Week:**
```dax
Articles This Week = 
CALCULATE(
    COUNT(powerbi_data[Article_Title]),
    DATESINPERIOD(powerbi_data[Published_Date], TODAY(), -7, DAY)
)
```

**Top Topic:**
```dax
Top Topic = 
CALCULATE(
    FIRST(powerbi_data[Topic_Label]),
    TOPN(1, VALUES(powerbi_data[Topic_Label]), COUNT(powerbi_data[Article_Title]))
)
```

### 3. Conditional Formatting

Apply conditional formatting to:
- **Topic Confidence**: Color-code by confidence level
- **Article Count**: Highlight high-volume topics
- **Date**: Highlight recent articles

### 4. Drill-Through Pages

Create drill-through pages for:
- **Topic Details**: Show all articles for a specific topic
- **Source Analysis**: Deep dive into specific news sources
- **Daily Breakdown**: Detailed view of articles by day

## Sample Dashboard Layout

### Page 1: Overview
```
┌─────────────────┬─────────────────┐
│   Topic Pie     │  Trends Line    │
│     Chart       │     Chart       │
├─────────────────┼─────────────────┤
│  Source Bar     │  Confidence     │
│     Chart       │  Histogram      │
├─────────────────┴─────────────────┤
│         Date Range Slicer         │
└───────────────────────────────────┘
```

### Page 2: Topic Analysis
```
┌─────────────────┬─────────────────┐
│  Topic Summary  │  Keywords       │
│     Table       │    Table        │
├─────────────────┼─────────────────┤
│  Content Length │  Time Pattern   │
│   Scatter Plot  │   Bar Chart     │
├─────────────────┴─────────────────┤
│      Topic and Source Slicers     │
└───────────────────────────────────┘
```

### Page 3: Time Analysis
```
┌───────────────────────────────────┐
│        Daily Volume Area Chart    │
├─────────────────┬─────────────────┤
│  Weekday vs     │  Hourly Pattern │
│  Weekend Chart  │   Line Chart    │
├─────────────────┴─────────────────┤
│         Calendar Slicer           │
└───────────────────────────────────┘
```

## Automatic Refresh

### Option 1: Manual Refresh
1. Click "Refresh" in Power BI Desktop
2. Data will reload from CSV files

### Option 2: Power BI Service (Cloud)
1. Publish dashboard to Power BI Service
2. Set up scheduled refresh
3. Configure data source credentials

### Option 3: Real-time Updates
1. Use Power BI streaming datasets
2. Modify pipeline to push data via REST API
3. Enable real-time dashboard updates

## Best Practices

### 1. Performance Optimization
- **Limit Data**: Use date filters to reduce dataset size
- **Aggregations**: Pre-aggregate data where possible
- **Relationships**: Ensure proper relationships between tables
- **Data Types**: Use appropriate data types for each column

### 2. Visual Design
- **Consistent Colors**: Use consistent color scheme for topics
- **Clear Labels**: Ensure all charts have clear titles and labels
- **Responsive Layout**: Design for different screen sizes
- **Tooltips**: Add informative tooltips to charts

### 3. User Experience
- **Intuitive Navigation**: Clear page navigation
- **Logical Grouping**: Group related visualizations
- **Progressive Disclosure**: Start with overview, allow drill-down
- **Export Options**: Enable data export for users

### 4. Data Quality
- **Validation**: Validate data before importing
- **Error Handling**: Handle missing or invalid data gracefully
- **Documentation**: Document data sources and calculations
- **Version Control**: Track dashboard versions

## Troubleshooting

### Common Issues

1. **Data Not Loading**
   - Check file paths
   - Verify CSV format
   - Ensure files are not locked

2. **Incorrect Data Types**
   - Use "Transform Data" to fix types
   - Check date formats
   - Verify numeric columns

3. **Relationships Not Working**
   - Check column names match exactly
   - Verify data types are compatible
   - Remove duplicate relationships

4. **Poor Performance**
   - Reduce data volume
   - Optimize DAX formulas
   - Use aggregated tables

### Data Refresh Issues

1. **File Not Found**
   - Verify file paths in data source settings
   - Check if pipeline completed successfully

2. **Permission Errors**
   - Ensure Power BI has read access to data folder
   - Check file permissions

3. **Outdated Data**
   - Verify pipeline is running on schedule
   - Check last modification time of CSV files

## Next Steps

1. **Create Basic Dashboard**
   - Import the three CSV files
   - Create basic visualizations
   - Set up relationships

2. **Enhance Visualizations**
   - Add calculated measures
   - Implement conditional formatting
   - Create drill-through pages

3. **Set Up Automation**
   - Schedule pipeline runs
   - Configure automatic refresh
   - Set up monitoring

4. **Share and Collaborate**
   - Publish to Power BI Service
   - Share with stakeholders
   - Set up user permissions

5. **Monitor and Improve**
   - Track dashboard usage
   - Gather user feedback
   - Iterate on design and functionality
