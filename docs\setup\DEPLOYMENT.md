# Deployment Guide

This guide covers deployment options for the Topic Modeling Pipeline in different environments.

## Quick Start Deployment

### 1. Local Development Setup

```bash
# Clone or download the project
cd topic_modelling

# Install dependencies
pip install -r requirements.txt

# Run setup script
python setup.py

# Configure API keys
cp .env.example .env
# Edit .env with your API keys

# Test the pipeline
python run_pipeline.py --step ingestion
```

### 2. Production Setup

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run setup
python setup.py

# Configure for production
# Edit config.yaml for production settings
# Set up environment variables
# Configure logging

# Test full pipeline
python run_pipeline.py
```

## Environment-Specific Deployments

### Development Environment

**Characteristics:**
- Small dataset for testing
- Frequent code changes
- Debug logging enabled
- Manual execution

**Configuration:**
```yaml
news_api:
  page_size: 20
  max_pages: 2

topic_modeling:
  num_topics: 5
  max_iter: 50

logging:
  level: "DEBUG"
```

**Setup:**
```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest black flake8

# Run tests
python run_tests.py

# Run pipeline
python run_pipeline.py
```

### Staging Environment

**Characteristics:**
- Production-like data volume
- Automated testing
- Performance monitoring
- Scheduled execution

**Configuration:**
```yaml
news_api:
  page_size: 50
  max_pages: 5

topic_modeling:
  num_topics: 8
  max_iter: 100

logging:
  level: "INFO"

scheduling:
  enabled: true
  frequency: "daily"
```

**Setup:**
```bash
# Production-like setup
pip install -r requirements.txt

# Configure scheduling
python -c "from docs.SCHEDULING import setup_scheduler; setup_scheduler()"

# Monitor logs
tail -f logs/pipeline.log
```

### Production Environment

**Characteristics:**
- Full dataset processing
- High reliability requirements
- Automated monitoring
- Backup and recovery

**Configuration:**
```yaml
news_api:
  page_size: 100
  max_pages: 10

topic_modeling:
  num_topics: 10
  max_iter: 200

logging:
  level: "WARNING"
  
output:
  backup_enabled: true
  retention_days: 30
```

## Cloud Deployment Options

### 1. AWS Deployment

#### EC2 Instance Setup

```bash
# Launch EC2 instance (Ubuntu 20.04 LTS)
# Connect via SSH

# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv -y

# Clone project
git clone <your-repo-url>
cd topic_modelling

# Set up virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Set up systemd service for automation
sudo cp deployment/topic-modeling.service /etc/systemd/system/
sudo systemctl enable topic-modeling
sudo systemctl start topic-modeling
```

#### S3 Integration

```python
# Add to config.yaml
output:
  s3_bucket: "your-topic-modeling-bucket"
  s3_region: "us-east-1"
  upload_to_s3: true

# Install AWS CLI
pip install boto3

# Configure AWS credentials
aws configure
```

#### CloudWatch Monitoring

```python
# Add CloudWatch logging
import boto3

cloudwatch = boto3.client('cloudwatch')

# Send custom metrics
cloudwatch.put_metric_data(
    Namespace='TopicModeling',
    MetricData=[
        {
            'MetricName': 'ArticlesProcessed',
            'Value': article_count,
            'Unit': 'Count'
        }
    ]
)
```

### 2. Google Cloud Platform

#### Compute Engine Setup

```bash
# Create VM instance
gcloud compute instances create topic-modeling-vm \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=n1-standard-2 \
    --zone=us-central1-a

# SSH into instance
gcloud compute ssh topic-modeling-vm

# Follow standard setup process
```

#### Cloud Storage Integration

```python
# Add to requirements.txt
google-cloud-storage

# Configure in config.yaml
output:
  gcs_bucket: "your-topic-modeling-bucket"
  upload_to_gcs: true
```

### 3. Azure Deployment

#### Virtual Machine Setup

```bash
# Create resource group
az group create --name TopicModelingRG --location eastus

# Create VM
az vm create \
    --resource-group TopicModelingRG \
    --name TopicModelingVM \
    --image UbuntuLTS \
    --admin-username azureuser \
    --generate-ssh-keys

# Connect and setup
ssh azureuser@<vm-ip>
```

## Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Download NLTK data
RUN python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"

# Install spaCy model
RUN python -m spacy download en_core_web_sm

# Copy application code
COPY . .

# Create directories
RUN mkdir -p data logs models

# Set environment variables
ENV PYTHONPATH=/app/src

# Run setup
RUN python setup.py

# Default command
CMD ["python", "run_pipeline.py"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  topic-modeling:
    build: .
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./.env:/app/.env
    environment:
      - PYTHONPATH=/app/src
    restart: unless-stopped
    
  scheduler:
    build: .
    command: python scheduler.py
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
    depends_on:
      - topic-modeling
    restart: unless-stopped
```

### Build and Run

```bash
# Build image
docker build -t topic-modeling .

# Run container
docker run -d \
    --name topic-modeling \
    -v $(pwd)/data:/app/data \
    -v $(pwd)/logs:/app/logs \
    -v $(pwd)/.env:/app/.env \
    topic-modeling

# Using docker-compose
docker-compose up -d
```

## Kubernetes Deployment

### Deployment YAML

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: topic-modeling
spec:
  replicas: 1
  selector:
    matchLabels:
      app: topic-modeling
  template:
    metadata:
      labels:
        app: topic-modeling
    spec:
      containers:
      - name: topic-modeling
        image: topic-modeling:latest
        env:
        - name: NEWS_API_KEY
          valueFrom:
            secretKeyRef:
              name: topic-modeling-secrets
              key: news-api-key
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: topic-modeling-data
      - name: logs-volume
        persistentVolumeClaim:
          claimName: topic-modeling-logs
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: topic-modeling-cron
spec:
  schedule: "0 6 * * *"  # Daily at 6 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: topic-modeling
            image: topic-modeling:latest
            command: ["python", "run_pipeline.py"]
          restartPolicy: OnFailure
```

## Monitoring and Alerting

### Health Checks

```python
# health_check.py
import requests
from datetime import datetime, timedelta
from pathlib import Path

def check_pipeline_health():
    """Check if pipeline is running correctly."""
    
    # Check if recent data exists
    data_dir = Path('data')
    latest_file = max(data_dir.glob('*.csv'), key=lambda f: f.stat().st_mtime)
    
    if datetime.now() - datetime.fromtimestamp(latest_file.stat().st_mtime) > timedelta(days=2):
        return False, "No recent data files"
    
    # Check log files for errors
    log_file = Path('logs/pipeline.log')
    if log_file.exists():
        with open(log_file, 'r') as f:
            recent_logs = f.readlines()[-100:]  # Last 100 lines
            
        error_count = sum(1 for line in recent_logs if 'ERROR' in line)
        if error_count > 5:
            return False, f"Too many errors: {error_count}"
    
    return True, "Pipeline healthy"

if __name__ == "__main__":
    healthy, message = check_pipeline_health()
    print(f"Health: {'OK' if healthy else 'FAIL'} - {message}")
    exit(0 if healthy else 1)
```

### Prometheus Metrics

```python
# metrics.py
from prometheus_client import Counter, Histogram, Gauge, start_http_server

# Define metrics
articles_processed = Counter('articles_processed_total', 'Total articles processed')
processing_time = Histogram('processing_time_seconds', 'Time spent processing')
active_topics = Gauge('active_topics', 'Number of active topics')

def record_metrics(article_count, process_time, topic_count):
    """Record metrics for monitoring."""
    articles_processed.inc(article_count)
    processing_time.observe(process_time)
    active_topics.set(topic_count)

# Start metrics server
start_http_server(8000)
```

## Backup and Recovery

### Automated Backups

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/topic_modeling_$DATE"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup data files
cp -r data/ $BACKUP_DIR/
cp -r models/ $BACKUP_DIR/
cp config.yaml $BACKUP_DIR/

# Compress backup
tar -czf "$BACKUP_DIR.tar.gz" $BACKUP_DIR
rm -rf $BACKUP_DIR

# Upload to cloud storage (optional)
# aws s3 cp "$BACKUP_DIR.tar.gz" s3://your-backup-bucket/

echo "Backup completed: $BACKUP_DIR.tar.gz"
```

### Recovery Procedure

```bash
#!/bin/bash
# restore.sh

BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file.tar.gz>"
    exit 1
fi

# Extract backup
tar -xzf $BACKUP_FILE

# Restore files
BACKUP_DIR=$(basename $BACKUP_FILE .tar.gz)
cp -r $BACKUP_DIR/data/ ./
cp -r $BACKUP_DIR/models/ ./
cp $BACKUP_DIR/config.yaml ./

# Clean up
rm -rf $BACKUP_DIR

echo "Recovery completed from $BACKUP_FILE"
```

## Security Considerations

### API Key Management

```bash
# Use environment variables
export NEWS_API_KEY="your_key_here"

# Or use secret management services
# AWS Secrets Manager
# Azure Key Vault
# Google Secret Manager
```

### File Permissions

```bash
# Set appropriate permissions
chmod 600 .env
chmod 755 run_pipeline.py
chmod -R 750 data/
chmod -R 750 logs/
```

### Network Security

```bash
# Firewall rules (example for Ubuntu)
sudo ufw allow ssh
sudo ufw allow 8000/tcp  # For metrics endpoint
sudo ufw enable
```

## Troubleshooting

### Common Issues

1. **Out of Memory**
   - Reduce `max_features` in config
   - Process data in smaller batches
   - Increase system memory

2. **API Rate Limiting**
   - Reduce `page_size` and `max_pages`
   - Add delays between requests
   - Use multiple API keys

3. **Disk Space**
   - Implement log rotation
   - Clean up old data files
   - Monitor disk usage

4. **Network Issues**
   - Implement retry logic
   - Use fallback RSS feeds
   - Check firewall settings

### Log Analysis

```bash
# Check for errors
grep ERROR logs/pipeline.log

# Monitor real-time logs
tail -f logs/pipeline.log

# Analyze performance
grep "execution time" logs/pipeline.log
```

## Performance Optimization

### System Requirements

**Minimum:**
- 2 CPU cores
- 4 GB RAM
- 10 GB disk space

**Recommended:**
- 4 CPU cores
- 8 GB RAM
- 50 GB disk space
- SSD storage

### Optimization Tips

1. **Use SSD storage** for better I/O performance
2. **Increase memory** for larger datasets
3. **Use multiple cores** for parallel processing
4. **Optimize database queries** if using database storage
5. **Implement caching** for frequently accessed data

## Next Steps

1. Choose your deployment environment
2. Follow the appropriate setup guide
3. Configure monitoring and alerting
4. Set up automated backups
5. Test the complete deployment
6. Document any customizations
7. Train your team on operations
