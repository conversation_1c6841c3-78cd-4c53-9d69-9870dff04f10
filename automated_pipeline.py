#!/usr/bin/env python3
"""
Automated News Topic Modeling Pipeline
Implements your exact workflow: NewsAPI → raw_articles → preprocessed_articles → topic_results → Power BI
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

# Add scripts and database directories to path
sys.path.append('scripts')
sys.path.append('database')

# Import our pipeline components
from fetch_news import TimestampBasedNewsIngestion
from preprocess import AdvancedTextPreprocessor
from topic_model import LDATopicModelingService
from unified_sql_connection import UnifiedSQLConnectionManager as SQLConnectionManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automated_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomatedTopicModelingPipeline:
    """
    Complete automated pipeline implementing your exact workflow:
    1. Fetch news articles every X minutes using timestamp-based filtering
    2. Store raw JSON in raw_articles table
    3. Preprocess articles and store in preprocessed_articles table
    4. Apply LDA model and store results in topic_results table
    5. Enable real-time Power BI DirectQuery visualization
    """
    
    def __init__(self, 
                 fetch_interval_minutes: int = 15,
                 powerbi_refresh_minutes: int = 5):
        """
        Initialize the automated pipeline.
        
        Args:
            fetch_interval_minutes: How often to fetch news (default: 15 minutes)
            powerbi_refresh_minutes: Power BI refresh interval (default: 5 minutes)
        """
        
        self.fetch_interval_minutes = fetch_interval_minutes
        self.powerbi_refresh_minutes = powerbi_refresh_minutes
        
        # Initialize components
        self.sql_manager = None
        self.news_ingestion = None
        self.preprocessor = None
        self.topic_service = None
        
        # Pipeline statistics
        self.stats = {
            'total_runs': 0,
            'articles_fetched': 0,
            'articles_processed': 0,
            'articles_classified': 0,
            'last_run': None,
            'last_error': None
        }
        
        logger.info(f"🚀 Automated pipeline initialized (fetch: {fetch_interval_minutes}min, refresh: {powerbi_refresh_minutes}min)")
    
    def initialize_components(self) -> bool:
        """
        Initialize all pipeline components with SQL Server connection.
        
        Returns:
            True if all components initialized successfully
        """
        
        try:
            # 1. Initialize SQL Server connection
            logger.info("🔗 Initializing SQL Server connection...")
            self.sql_manager = SQLConnectionManager.from_config_file('config/database.json')
            
            # Test connection
            with self.sql_manager.get_connection() as conn:
                conn.execute("SELECT 1").fetchone()
            logger.info("✅ SQL Server connection established")
            
            # 2. Initialize news ingestion
            logger.info("📰 Initializing news ingestion...")
            api_key = os.getenv('NEWS_API_KEY')
            if not api_key:
                raise ValueError("NEWS_API_KEY environment variable not set")
            
            self.news_ingestion = TimestampBasedNewsIngestion(
                api_key=api_key,
                sql_connection_manager=self.sql_manager,
                fetch_interval_minutes=self.fetch_interval_minutes
            )
            logger.info("✅ News ingestion initialized")
            
            # 3. Initialize text preprocessor
            logger.info("🧹 Initializing text preprocessor...")
            self.preprocessor = AdvancedTextPreprocessor(
                sql_connection_manager=self.sql_manager,
                use_spacy=True
            )
            logger.info("✅ Text preprocessor initialized")
            
            # 4. Initialize topic modeling service
            logger.info("🧠 Initializing topic modeling service...")
            self.topic_service = LDATopicModelingService(
                model_path="models/lda_model.pkl",
                use_database=True
            )
            
            # Add SQL manager to topic service
            self.topic_service.sql_manager = self.sql_manager
            
            # Load the LDA model
            if not self.topic_service.load_model():
                raise ValueError("Failed to load LDA model from models/lda_model.pkl")
            
            logger.info("✅ Topic modeling service initialized")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Component initialization failed: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    def run_single_cycle(self) -> Dict:
        """
        Run a single pipeline cycle following your exact workflow.
        
        Returns:
            Dictionary with cycle results and statistics
        """
        
        cycle_start = datetime.now()
        cycle_stats = {
            'start_time': cycle_start,
            'articles_fetched': 0,
            'articles_processed': 0,
            'articles_classified': 0,
            'duration_seconds': 0,
            'success': False,
            'errors': []
        }
        
        try:
            logger.info("🔄 Starting pipeline cycle...")
            
            # Step 1: Fetch latest news articles using timestamp-based filtering
            logger.info("📡 Step 1: Fetching latest news articles...")
            
            latest_articles = self.news_ingestion.fetch_latest_articles(
                query="technology OR politics OR business OR economy OR health OR science",
                page_size=100
            )
            
            if latest_articles:
                # Store raw articles in SQL Server
                new_articles_count = self.news_ingestion.store_raw_articles(latest_articles)
                cycle_stats['articles_fetched'] = new_articles_count
                logger.info(f"✅ Fetched and stored {new_articles_count} new articles")
            else:
                logger.info("📄 No new articles found in this cycle")
            
            # Step 2: Preprocess pending articles
            logger.info("🧹 Step 2: Preprocessing pending articles...")
            
            processed_count = self.preprocessor.process_pending_articles()
            cycle_stats['articles_processed'] = processed_count
            
            if processed_count > 0:
                logger.info(f"✅ Preprocessed {processed_count} articles")
            else:
                logger.info("📄 No articles pending preprocessing")
            
            # Step 3: Classify preprocessed articles using LDA model
            logger.info("🧠 Step 3: Classifying articles with LDA model...")
            
            classified_count = self.topic_service.classify_pending_articles()
            cycle_stats['articles_classified'] = classified_count
            
            if classified_count > 0:
                logger.info(f"✅ Classified {classified_count} articles")
            else:
                logger.info("📄 No articles pending classification")
            
            # Step 4: Update pipeline statistics
            cycle_end = datetime.now()
            cycle_stats['duration_seconds'] = (cycle_end - cycle_start).total_seconds()
            cycle_stats['success'] = True
            
            # Update global statistics
            self.stats['total_runs'] += 1
            self.stats['articles_fetched'] += cycle_stats['articles_fetched']
            self.stats['articles_processed'] += cycle_stats['articles_processed']
            self.stats['articles_classified'] += cycle_stats['articles_classified']
            self.stats['last_run'] = cycle_end
            
            logger.info(f"✅ Pipeline cycle completed in {cycle_stats['duration_seconds']:.2f} seconds")
            logger.info(f"📊 Cycle summary: {cycle_stats['articles_fetched']} fetched, "
                       f"{cycle_stats['articles_processed']} processed, "
                       f"{cycle_stats['articles_classified']} classified")
            
            return cycle_stats
            
        except Exception as e:
            logger.error(f"❌ Pipeline cycle failed: {e}")
            cycle_stats['errors'].append(str(e))
            cycle_stats['success'] = False
            self.stats['last_error'] = str(e)
            return cycle_stats
    
    def run_continuous(self, max_cycles: Optional[int] = None) -> None:
        """
        Run the pipeline continuously at specified intervals.
        
        Args:
            max_cycles: Maximum number of cycles to run (None for infinite)
        """
        
        logger.info(f"🔄 Starting continuous pipeline (interval: {self.fetch_interval_minutes} minutes)")
        
        cycle_count = 0
        
        try:
            while True:
                # Check if we've reached max cycles
                if max_cycles and cycle_count >= max_cycles:
                    logger.info(f"🏁 Reached maximum cycles ({max_cycles}), stopping")
                    break
                
                # Run a single cycle
                cycle_result = self.run_single_cycle()
                cycle_count += 1
                
                # Log cycle summary
                if cycle_result['success']:
                    logger.info(f"✅ Cycle {cycle_count} completed successfully")
                else:
                    logger.error(f"❌ Cycle {cycle_count} failed: {cycle_result['errors']}")
                
                # Wait for next cycle (unless this is the last cycle)
                if not max_cycles or cycle_count < max_cycles:
                    wait_seconds = self.fetch_interval_minutes * 60
                    logger.info(f"⏰ Waiting {self.fetch_interval_minutes} minutes until next cycle...")
                    time.sleep(wait_seconds)
                
        except KeyboardInterrupt:
            logger.info("🛑 Pipeline stopped by user")
        except Exception as e:
            logger.error(f"❌ Pipeline failed: {e}")
    
    def get_pipeline_status(self) -> Dict:
        """
        Get current pipeline status and statistics.
        
        Returns:
            Dictionary with pipeline status information
        """
        
        return {
            'configuration': {
                'fetch_interval_minutes': self.fetch_interval_minutes,
                'powerbi_refresh_minutes': self.powerbi_refresh_minutes
            },
            'statistics': self.stats.copy(),
            'components_initialized': all([
                self.sql_manager is not None,
                self.news_ingestion is not None,
                self.preprocessor is not None,
                self.topic_service is not None
            ]),
            'model_loaded': self.topic_service.is_loaded if self.topic_service else False
        }
    
    def save_status_report(self, output_file: str = "logs/pipeline_status.json") -> None:
        """
        Save current pipeline status to JSON file.
        
        Args:
            output_file: Path to save status report
        """
        
        try:
            status = self.get_pipeline_status()
            
            # Convert datetime objects to strings for JSON serialization
            if status['statistics']['last_run']:
                status['statistics']['last_run'] = status['statistics']['last_run'].isoformat()
            
            with open(output_file, 'w') as f:
                json.dump(status, f, indent=2)
            
            logger.info(f"📊 Status report saved: {output_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save status report: {e}")

def main():
    """Main function to run the automated pipeline."""
    
    import argparse
    
    parser = argparse.ArgumentParser(description='Automated Topic Modeling Pipeline')
    parser.add_argument('--fetch-interval', type=int, default=15,
                       help='News fetch interval in minutes (default: 15)')
    parser.add_argument('--powerbi-refresh', type=int, default=5,
                       help='Power BI refresh interval in minutes (default: 5)')
    parser.add_argument('--max-cycles', type=int, default=None,
                       help='Maximum number of cycles to run (default: infinite)')
    parser.add_argument('--single-run', action='store_true',
                       help='Run single cycle and exit')
    parser.add_argument('--status', action='store_true',
                       help='Show pipeline status and exit')
    
    args = parser.parse_args()
    
    # Create pipeline
    pipeline = AutomatedTopicModelingPipeline(
        fetch_interval_minutes=args.fetch_interval,
        powerbi_refresh_minutes=args.powerbi_refresh
    )
    
    # Handle status request
    if args.status:
        if pipeline.initialize_components():
            status = pipeline.get_pipeline_status()
            print(json.dumps(status, indent=2, default=str))
        else:
            print("❌ Failed to initialize pipeline components")
        return
    
    # Initialize components
    if not pipeline.initialize_components():
        logger.error("❌ Failed to initialize pipeline components")
        sys.exit(1)
    
    # Run pipeline
    if args.single_run:
        logger.info("🔄 Running single pipeline cycle...")
        result = pipeline.run_single_cycle()
        pipeline.save_status_report()
        
        if result['success']:
            logger.info("✅ Single cycle completed successfully")
        else:
            logger.error("❌ Single cycle failed")
            sys.exit(1)
    else:
        logger.info("🔄 Starting continuous pipeline...")
        pipeline.run_continuous(max_cycles=args.max_cycles)
        pipeline.save_status_report()

if __name__ == "__main__":
    main()
