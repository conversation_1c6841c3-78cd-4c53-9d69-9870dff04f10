#!/usr/bin/env python3
"""
Advanced Text Preprocessing Module
Comprehensive text cleaning with HTML removal, stopwords, lemmatization using NLTK/spaCy
"""

import re
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Set
import logging
from pathlib import Path
import sqlite3

# NLP libraries
import nltk
import spacy
from bs4 import BeautifulSoup
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import WordNetLemmatizer
from nltk.tag import pos_tag

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedTextPreprocessor:
    """Comprehensive text preprocessing for news articles."""
    
    def __init__(self, 
                 use_spacy: bool = True,
                 language: str = 'en',
                 custom_stopwords: List[str] = None):
        """
        Initialize text preprocessor.
        
        Args:
            use_spacy: Whether to use spaCy for advanced processing
            language: Language code for processing
            custom_stopwords: Additional stopwords to remove
        """
        self.use_spacy = use_spacy
        self.language = language
        
        # Initialize NLTK components
        self.lemmatizer = WordNetLemmatizer()
        
        # Load stopwords
        self.stopwords = self._load_stopwords(custom_stopwords)
        
        # Initialize spaCy if requested
        self.nlp = None
        if use_spacy:
            self._initialize_spacy()
        
        # Compile regex patterns
        self._compile_patterns()
        
        logger.info(f"🔧 Text preprocessor initialized (spaCy: {use_spacy})")
    
    def _load_stopwords(self, custom_stopwords: List[str] = None) -> Set[str]:
        """Load stopwords from NLTK and custom list."""
        
        try:
            # Load NLTK stopwords
            stop_words = set(stopwords.words('english'))
            
            # Add common news-specific stopwords
            news_stopwords = {
                'said', 'says', 'according', 'reuters', 'news', 'report', 
                'reported', 'reports', 'article', 'story', 'told', 'tells',
                'source', 'sources', 'official', 'officials', 'statement',
                'press', 'media', 'journalist', 'correspondent', 'editor',
                'breaking', 'update', 'latest', 'developing', 'exclusive',
                'via', 'http', 'https', 'www', 'com', 'org', 'net'
            }
            
            stop_words.update(news_stopwords)
            
            # Add custom stopwords
            if custom_stopwords:
                stop_words.update(custom_stopwords)
            
            logger.info(f"📚 Loaded {len(stop_words)} stopwords")
            return stop_words
            
        except Exception as e:
            logger.error(f"❌ Error loading stopwords: {e}")
            return set()
    
    def _initialize_spacy(self):
        """Initialize spaCy model."""
        
        try:
            # Try to load English model
            model_names = ['en_core_web_sm', 'en_core_web_md', 'en_core_web_lg']
            
            for model_name in model_names:
                try:
                    self.nlp = spacy.load(model_name)
                    logger.info(f"✅ Loaded spaCy model: {model_name}")
                    break
                except OSError:
                    continue
            
            if self.nlp is None:
                logger.warning("⚠️ No spaCy model found, falling back to NLTK")
                self.use_spacy = False
            else:
                # Disable unnecessary components for speed
                self.nlp.disable_pipes(['parser', 'ner'])
                
        except Exception as e:
            logger.error(f"❌ Error initializing spaCy: {e}")
            self.use_spacy = False
    
    def _compile_patterns(self):
        """Compile regex patterns for text cleaning."""
        
        # URL pattern
        self.url_pattern = re.compile(
            r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        )
        
        # Email pattern
        self.email_pattern = re.compile(r'\S+@\S+\.\S+')
        
        # Social media handles
        self.mention_pattern = re.compile(r'@\w+')
        self.hashtag_pattern = re.compile(r'#\w+')
        
        # Numbers and special characters
        self.number_pattern = re.compile(r'\b\d+\b')
        self.special_chars_pattern = re.compile(r'[^\w\s]')
        
        # Multiple whitespace
        self.whitespace_pattern = re.compile(r'\s+')
        
        # News-specific patterns
        self.news_suffix_pattern = re.compile(r'\[.*?\]$')  # Remove [+X chars] etc.
        self.byline_pattern = re.compile(r'^(By|BY)\s+\w+.*?[-–—]', re.MULTILINE)
        
        logger.info("🔧 Compiled regex patterns")
    
    def clean_html(self, text: str) -> str:
        """Remove HTML tags and entities."""
        
        if not text:
            return ""
        
        try:
            # Parse HTML
            soup = BeautifulSoup(text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text and clean up
            text = soup.get_text()
            
            # Clean up whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            
            return text
            
        except Exception as e:
            logger.debug(f"HTML cleaning error: {e}")
            return text
    
    def remove_urls_and_emails(self, text: str) -> str:
        """Remove URLs and email addresses."""
        
        if not text:
            return ""
        
        # Remove URLs
        text = self.url_pattern.sub('', text)
        
        # Remove emails
        text = self.email_pattern.sub('', text)
        
        # Remove social media handles
        text = self.mention_pattern.sub('', text)
        text = self.hashtag_pattern.sub('', text)
        
        return text
    
    def clean_news_specific(self, text: str) -> str:
        """Clean news-specific artifacts."""
        
        if not text:
            return ""
        
        # Remove news suffixes like [+X chars]
        text = self.news_suffix_pattern.sub('', text)
        
        # Remove bylines
        text = self.byline_pattern.sub('', text)
        
        # Remove common news prefixes
        prefixes_to_remove = [
            r'^BREAKING:?\s*',
            r'^UPDATE:?\s*',
            r'^EXCLUSIVE:?\s*',
            r'^DEVELOPING:?\s*',
            r'^\(Reuters\)\s*[-–—]?\s*',
            r'^\(AP\)\s*[-–—]?\s*',
            r'^\(CNN\)\s*[-–—]?\s*'
        ]
        
        for prefix in prefixes_to_remove:
            text = re.sub(prefix, '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def tokenize_and_clean(self, text: str, 
                          remove_numbers: bool = True,
                          remove_punctuation: bool = True,
                          min_word_length: int = 2,
                          max_word_length: int = 20) -> List[str]:
        """Tokenize text and clean tokens."""
        
        if not text:
            return []
        
        try:
            # Tokenize
            tokens = word_tokenize(text.lower())
            
            # Clean tokens
            cleaned_tokens = []
            
            for token in tokens:
                # Skip if too short or too long
                if len(token) < min_word_length or len(token) > max_word_length:
                    continue
                
                # Remove numbers if requested
                if remove_numbers and token.isdigit():
                    continue
                
                # Remove punctuation if requested
                if remove_punctuation and not token.isalnum():
                    continue
                
                # Skip stopwords
                if token in self.stopwords:
                    continue
                
                # Skip if only punctuation or numbers
                if not any(c.isalpha() for c in token):
                    continue
                
                cleaned_tokens.append(token)
            
            return cleaned_tokens
            
        except Exception as e:
            logger.debug(f"Tokenization error: {e}")
            return []
    
    def lemmatize_tokens_nltk(self, tokens: List[str]) -> List[str]:
        """Lemmatize tokens using NLTK."""
        
        if not tokens:
            return []
        
        try:
            # Get POS tags for better lemmatization
            pos_tags = pos_tag(tokens)
            
            lemmatized = []
            for token, pos in pos_tags:
                # Convert POS tag to WordNet format
                if pos.startswith('V'):
                    wordnet_pos = 'v'  # Verb
                elif pos.startswith('N'):
                    wordnet_pos = 'n'  # Noun
                elif pos.startswith('R'):
                    wordnet_pos = 'r'  # Adverb
                elif pos.startswith('J'):
                    wordnet_pos = 'a'  # Adjective
                else:
                    wordnet_pos = 'n'  # Default to noun
                
                # Lemmatize
                lemma = self.lemmatizer.lemmatize(token, wordnet_pos)
                lemmatized.append(lemma)
            
            return lemmatized
            
        except Exception as e:
            logger.debug(f"NLTK lemmatization error: {e}")
            return tokens
    
    def lemmatize_tokens_spacy(self, text: str) -> List[str]:
        """Lemmatize tokens using spaCy."""
        
        if not text or not self.nlp:
            return []
        
        try:
            doc = self.nlp(text)
            
            lemmatized = []
            for token in doc:
                # Skip if stopword, punctuation, or space
                if (token.is_stop or token.is_punct or 
                    token.is_space or len(token.text) < 2):
                    continue
                
                # Skip if in custom stopwords
                if token.lemma_.lower() in self.stopwords:
                    continue
                
                lemmatized.append(token.lemma_.lower())
            
            return lemmatized
            
        except Exception as e:
            logger.debug(f"spaCy lemmatization error: {e}")
            return []
    
    def preprocess_text(self, text: str,
                       clean_html: bool = True,
                       remove_urls: bool = True,
                       clean_news_artifacts: bool = True,
                       lemmatize: bool = True,
                       return_tokens: bool = False) -> str:
        """
        Complete text preprocessing pipeline.
        
        Args:
            text: Input text to preprocess
            clean_html: Whether to remove HTML tags
            remove_urls: Whether to remove URLs and emails
            clean_news_artifacts: Whether to clean news-specific artifacts
            lemmatize: Whether to lemmatize words
            return_tokens: Whether to return tokens instead of text
            
        Returns:
            Preprocessed text or list of tokens
        """
        
        if not text or not isinstance(text, str):
            return [] if return_tokens else ""
        
        # Step 1: Clean HTML
        if clean_html:
            text = self.clean_html(text)
        
        # Step 2: Remove URLs and emails
        if remove_urls:
            text = self.remove_urls_and_emails(text)
        
        # Step 3: Clean news-specific artifacts
        if clean_news_artifacts:
            text = self.clean_news_specific(text)
        
        # Step 4: Basic cleaning
        text = self.whitespace_pattern.sub(' ', text).strip()
        
        if not text:
            return [] if return_tokens else ""
        
        # Step 5: Tokenization and lemmatization
        if lemmatize:
            if self.use_spacy:
                tokens = self.lemmatize_tokens_spacy(text)
            else:
                tokens = self.tokenize_and_clean(text)
                tokens = self.lemmatize_tokens_nltk(tokens)
        else:
            tokens = self.tokenize_and_clean(text)
        
        if return_tokens:
            return tokens
        else:
            return ' '.join(tokens)
    
    def preprocess_dataframe(self, df: pd.DataFrame,
                           text_column: str = 'full_text',
                           output_column: str = 'processed_text',
                           batch_size: int = 100) -> pd.DataFrame:
        """
        Preprocess text in a DataFrame.
        
        Args:
            df: Input DataFrame
            text_column: Column containing text to preprocess
            output_column: Column name for preprocessed text
            batch_size: Process in batches for memory efficiency
            
        Returns:
            DataFrame with preprocessed text column
        """
        
        if df.empty or text_column not in df.columns:
            logger.warning(f"⚠️ DataFrame empty or column '{text_column}' not found")
            return df
        
        logger.info(f"🔧 Preprocessing {len(df)} articles...")
        
        # Process in batches
        processed_texts = []
        
        for i in range(0, len(df), batch_size):
            batch = df.iloc[i:i+batch_size]
            
            batch_processed = []
            for text in batch[text_column]:
                processed = self.preprocess_text(text)
                batch_processed.append(processed)
            
            processed_texts.extend(batch_processed)
            
            if i % (batch_size * 10) == 0:
                logger.info(f"  Processed {i + len(batch)}/{len(df)} articles")
        
        # Add processed text column
        df_copy = df.copy()
        df_copy[output_column] = processed_texts
        
        # Remove rows with empty processed text
        initial_count = len(df_copy)
        df_copy = df_copy[df_copy[output_column].str.len() > 0]
        final_count = len(df_copy)
        
        if initial_count != final_count:
            logger.info(f"🧹 Removed {initial_count - final_count} articles with empty processed text")
        
        logger.info(f"✅ Preprocessing complete: {final_count} articles ready")
        return df_copy

def main():
    """Test the preprocessing module."""
    
    # Sample news text
    sample_text = """
    <p>BREAKING: According to Reuters, artificial intelligence technology is 
    transforming the healthcare industry. The report says that machine learning 
    algorithms are being used to analyze medical data. 
    Visit https://example.com for more details. [+150 chars]</p>
    """
    
    # Initialize preprocessor
    preprocessor = AdvancedTextPreprocessor(use_spacy=True)
    
    # Test preprocessing
    logger.info("🧪 Testing text preprocessing...")
    logger.info(f"Original: {sample_text}")
    
    processed = preprocessor.preprocess_text(sample_text)
    logger.info(f"Processed: {processed}")
    
    # Test with tokens
    tokens = preprocessor.preprocess_text(sample_text, return_tokens=True)
    logger.info(f"Tokens: {tokens}")
    
    # Test DataFrame processing
    sample_df = pd.DataFrame({
        'title': ['AI News', 'Tech Update'],
        'full_text': [sample_text, 'Machine learning is advancing rapidly.']
    })
    
    processed_df = preprocessor.preprocess_dataframe(sample_df)
    logger.info(f"DataFrame processed: {len(processed_df)} rows")
    
    if not processed_df.empty:
        logger.info(f"Sample processed text: {processed_df['processed_text'].iloc[0]}")

if __name__ == "__main__":
    main()
