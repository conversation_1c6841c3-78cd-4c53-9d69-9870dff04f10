# 📊 Power BI Dashboard Setup Guide

**Complete guide for creating interactive dashboards with your topic modeling data**

## 🎯 **Overview**

The pipeline generates 6 comprehensive datasets optimized for Power BI visualization:

1. **Topic Trends** - Topic evolution over time
2. **Hourly Trends** - Hourly topic analysis with time patterns
3. **Article Details** - Recent articles with full metadata
4. **Topic Summary** - Comprehensive topic statistics
5. **Source Analysis** - News source breakdown by topic
6. **Confidence Analysis** - Model performance metrics

## 📁 **Data Files Location**

All Power BI datasets are exported to: `data/powerbi/`

```
data/powerbi/
├── topic_trends.csv        # Daily topic trends
├── hourly_trends.csv       # Hourly topic patterns
├── article_details.csv     # Recent article details
├── topic_summary.csv       # Topic statistics
├── source_analysis.csv     # Source breakdown
└── confidence_analysis.csv # Model confidence metrics
```

## 🚀 **Quick Setup**

### **Step 1: Import Data Sources**

1. Open Power BI Desktop
2. Click **Get Data** → **Text/CSV**
3. Import each CSV file from `data/powerbi/`
4. Set **Data Type Detection** to "Based on first 200 rows"
5. Click **Transform Data** to open Power Query Editor

### **Step 2: Data Transformation**

For each dataset, apply these transformations:

**All Datasets:**
- Set proper data types for date/time columns
- Remove any empty rows
- Rename columns for clarity

**Specific Transformations:**

**topic_trends.csv:**
```
- date: Date type
- topic_id: Whole Number
- article_count: Whole Number
- avg_confidence: Decimal Number
```

**hourly_trends.csv:**
```
- hour: Date/Time type
- hour_of_day: Whole Number
- day_of_week: Text (categorical)
```

**article_details.csv:**
```
- published_at: Date/Time type
- classified_at: Date/Time type
- topic_confidence: Decimal Number
- confidence_category: Text (categorical)
```

### **Step 3: Create Relationships**

In the **Model** view, create relationships:

1. **topic_trends** ↔ **topic_summary**
   - Relationship: `topic_id` (many-to-one)

2. **hourly_trends** ↔ **topic_summary**
   - Relationship: `topic_id` (many-to-one)

3. **article_details** ↔ **topic_summary**
   - Relationship: `topic_id` (many-to-one)

## 📊 **Recommended Visualizations**

### **Page 1: Topic Overview**

**1. Topic Distribution (Pie Chart)**
- Values: `total_articles` from topic_summary
- Legend: `topic_label`
- Show percentages

**2. Topic Trends Over Time (Line Chart)**
- X-axis: `date` from topic_trends
- Y-axis: `article_count`
- Legend: `topic_label`
- Add trend lines

**3. Top Topics Table**
- Columns: `topic_label`, `total_articles`, `percentage`, `avg_confidence`
- Sort by `total_articles` descending

**4. Key Metrics Cards**
- Total Articles: `SUM(topic_summary[total_articles])`
- Active Topics: `DISTINCTCOUNT(topic_summary[topic_id])`
- Avg Confidence: `AVERAGE(topic_summary[avg_confidence])`

### **Page 2: Hourly Analysis**

**1. Hourly Activity Heatmap**
- Rows: `day_of_week` from hourly_trends
- Columns: `hour_of_day`
- Values: `SUM(article_count)`
- Color saturation by article count

**2. Hourly Trends (Line Chart)**
- X-axis: `hour` from hourly_trends
- Y-axis: `article_count`
- Legend: `topic_label`
- Filter: Last 7 days

**3. Peak Hours Analysis (Column Chart)**
- X-axis: `hour_of_day`
- Y-axis: `SUM(article_count)`
- Show average line

### **Page 3: Article Details**

**1. Recent Articles Table**
- Columns: `title`, `source_name`, `topic_label`, `topic_confidence`, `published_at`
- Sort by `published_at` descending
- Add conditional formatting for confidence

**2. Articles by Source (Bar Chart)**
- Y-axis: `source_name` from article_details
- X-axis: `COUNT(article_id)`
- Color by `topic_label`

**3. Confidence Distribution (Histogram)**
- X-axis: `topic_confidence` (binned)
- Y-axis: Count of articles
- Show distribution curve

### **Page 4: Source Analysis**

**1. Source-Topic Matrix (Matrix Visual)**
- Rows: `source_name`
- Columns: `topic_label`
- Values: `article_count`
- Color by values

**2. Source Diversity (Scatter Plot)**
- X-axis: `total_articles` from source_analysis
- Y-axis: `topics_covered`
- Size: `total_articles`
- Tooltip: `source_name`

**3. Top Sources by Topic (Stacked Bar)**
- Y-axis: `source_name`
- X-axis: `article_count`
- Legend: `topic_label`

### **Page 5: Model Performance**

**1. Confidence by Topic (Box Plot)**
- Category: `topic_label`
- Values: `topic_confidence` from article_details
- Show outliers

**2. Classification Rate Trend (Line Chart)**
- X-axis: Date (from article_details)
- Y-axis: Classification rate (calculated measure)
- Add target line at 70%

**3. Confidence Distribution by Topic (Violin Plot)**
- Category: `topic_label`
- Values: `topic_confidence`
- Show median and quartiles

## 🔧 **Advanced Features**

### **Custom Measures (DAX)**

**Classification Rate:**
```dax
Classification Rate = 
DIVIDE(
    COUNTROWS(FILTER(article_details, article_details[topic_id] <> -1)),
    COUNTROWS(article_details)
)
```

**Articles This Hour:**
```dax
Articles This Hour = 
CALCULATE(
    SUM(hourly_trends[article_count]),
    FILTER(
        hourly_trends,
        hourly_trends[hour] = MAX(hourly_trends[hour])
    )
)
```

**Trending Topics:**
```dax
Trending Score = 
VAR CurrentPeriod = 
    CALCULATE(SUM(topic_trends[article_count]), DATESINPERIOD(topic_trends[date], MAX(topic_trends[date]), -1, DAY))
VAR PreviousPeriod = 
    CALCULATE(SUM(topic_trends[article_count]), DATESINPERIOD(topic_trends[date], MAX(topic_trends[date]) - 1, -1, DAY))
RETURN
    DIVIDE(CurrentPeriod - PreviousPeriod, PreviousPeriod)
```

### **Interactive Filters**

Add these slicers for interactivity:

1. **Date Range Slicer**
   - Field: `date` from topic_trends
   - Type: Between

2. **Topic Filter**
   - Field: `topic_label`
   - Type: Dropdown (multi-select)

3. **Source Filter**
   - Field: `source_name` from article_details
   - Type: List

4. **Confidence Range**
   - Field: `topic_confidence`
   - Type: Range slider

### **Conditional Formatting**

**Confidence Levels:**
- High (>0.7): Green
- Medium (0.5-0.7): Yellow  
- Low (<0.5): Red

**Trend Indicators:**
- Increasing: ↗️ Green
- Stable: ➡️ Blue
- Decreasing: ↘️ Red

## 🔄 **Auto-Refresh Setup**

### **Power BI Service (Online)**

1. Publish dashboard to Power BI Service
2. Go to **Settings** → **Datasets**
3. Configure **Scheduled Refresh**:
   - Frequency: Hourly
   - Time: 10 minutes past each hour
   - Data source: File path to `data/powerbi/`

### **Power BI Desktop (Local)**

1. **Data** → **Refresh**
2. Set up **Auto-refresh** in options:
   - Interval: 1 hour
   - Background refresh: Enabled

## 📈 **Dashboard Best Practices**

### **Performance Optimization**

1. **Limit Data Range**: Filter to last 30 days for better performance
2. **Aggregate Data**: Use summary tables for large datasets
3. **Optimize Visuals**: Limit to 6-8 visuals per page
4. **Use Bookmarks**: Create navigation between pages

### **Visual Design**

1. **Consistent Colors**: Use same color scheme for topics across all visuals
2. **Clear Titles**: Descriptive titles for each visual
3. **Tooltips**: Add relevant context in hover tooltips
4. **Mobile Layout**: Design responsive layout for mobile viewing

### **User Experience**

1. **Landing Page**: Start with high-level overview
2. **Drill-Down**: Enable drill-through from summary to details
3. **Export Options**: Allow users to export filtered data
4. **Help Text**: Add text boxes with usage instructions

## 🚨 **Troubleshooting**

### **Common Issues**

**Data Not Refreshing:**
- Check file paths in data source settings
- Verify CSV files are being updated by pipeline
- Check Power BI refresh logs

**Performance Issues:**
- Reduce date range in filters
- Use aggregated views instead of raw data
- Check for circular relationships in model

**Visualization Errors:**
- Verify data types are correct
- Check for null values in key fields
- Ensure relationships are properly configured

### **Data Quality Checks**

Before creating visuals, verify:
- ✅ Date fields are properly formatted
- ✅ Topic IDs are consistent across datasets
- ✅ No duplicate records in summary tables
- ✅ Confidence values are between 0 and 1
- ✅ Article counts match between datasets

## 📞 **Support**

For dashboard issues:
1. Check pipeline logs: `logs/pipeline.log`
2. Verify data exports: `data/powerbi/export_summary.json`
3. Test data refresh: `python scripts/run_pipeline.py step --step export`
4. Monitor pipeline health: `python monitor_pipeline.py check`

---

**🎉 Your comprehensive topic modeling dashboard is ready!**

**Next**: Set up automated refresh and share with your team for real-time insights into news topic trends.
