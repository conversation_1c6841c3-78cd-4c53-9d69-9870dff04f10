#!/usr/bin/env python3
"""
Test Script for Your Exact Workflow
Tests the complete pipeline: NewsAPI → raw_articles → preprocessed_articles → topic_results
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path

# Add paths
sys.path.append('scripts')
sys.path.append('database')

# Import components
from fetch_news import TimestampBasedNewsIngestion
from preprocess import AdvancedTextPreprocessor
from topic_model import LDATopicModelingService
from sql_connection import SQLConnectionManager

def test_complete_workflow():
    """Test the complete workflow end-to-end."""
    
    print("🧪 TESTING YOUR EXACT WORKFLOW")
    print("=" * 50)
    
    # Step 1: Check prerequisites
    print("\n📋 Step 1: Checking Prerequisites")
    print("-" * 30)
    
    # Check NewsAPI key
    api_key = os.getenv('NEWS_API_KEY')
    if not api_key:
        print("❌ NEWS_API_KEY not found")
        print("💡 Set with: set NEWS_API_KEY=your_key")
        return False
    print(f"✅ NewsAPI key found: {api_key[:10]}...")
    
    # Check LDA model
    model_path = Path("models/lda_model.pkl")
    if not model_path.exists():
        print("❌ LDA model not found at models/lda_model.pkl")
        print("💡 Place your trained model in the models/ directory")
        return False
    print("✅ LDA model found")
    
    # Step 2: Test SQL Server connection
    print("\n🗄️ Step 2: Testing SQL Server Connection")
    print("-" * 40)
    
    try:
        sql_manager = SQLConnectionManager.from_config_file('config/database.json')
        with sql_manager.get_connection() as conn:
            result = conn.execute("SELECT 1 as test").fetchone()
        print("✅ SQL Server connection successful")
    except Exception as e:
        print(f"❌ SQL Server connection failed: {e}")
        return False
    
    # Step 3: Test timestamp-based news fetching
    print("\n📡 Step 3: Testing Timestamp-Based News Fetching")
    print("-" * 50)
    
    try:
        news_ingestion = TimestampBasedNewsIngestion(
            api_key=api_key,
            sql_connection_manager=sql_manager,
            fetch_interval_minutes=60  # Last hour for testing
        )
        
        # Fetch latest articles
        articles = news_ingestion.fetch_latest_articles(
            query="technology OR politics",
            page_size=10  # Small batch for testing
        )
        
        print(f"📰 Fetched {len(articles)} articles")
        
        if articles:
            # Store in raw_articles table
            stored_count = news_ingestion.store_raw_articles(articles)
            print(f"✅ Stored {stored_count} new articles in raw_articles table")
            
            # Show sample
            sample = articles[0]
            print(f"\n📄 Sample Article:")
            print(f"   Title: {sample['title'][:60]}...")
            print(f"   Source: {sample['source_name']}")
            print(f"   Status: {sample['processing_status']}")
        else:
            print("ℹ️ No new articles found (normal during testing)")
            
    except Exception as e:
        print(f"❌ News fetching failed: {e}")
        return False
    
    # Step 4: Test text preprocessing
    print("\n🧹 Step 4: Testing Text Preprocessing")
    print("-" * 40)
    
    try:
        preprocessor = AdvancedTextPreprocessor(
            sql_connection_manager=sql_manager,
            use_spacy=True
        )
        
        # Process pending articles
        processed_count = preprocessor.process_pending_articles()
        print(f"✅ Preprocessed {processed_count} articles")
        
        if processed_count > 0:
            print("📊 Articles stored in preprocessed_articles table")
        else:
            print("ℹ️ No articles pending preprocessing")
            
    except Exception as e:
        print(f"❌ Text preprocessing failed: {e}")
        return False
    
    # Step 5: Test LDA topic classification
    print("\n🧠 Step 5: Testing LDA Topic Classification")
    print("-" * 45)
    
    try:
        topic_service = LDATopicModelingService(
            model_path="models/lda_model.pkl",
            use_database=True
        )
        
        # Add SQL manager
        topic_service.sql_manager = sql_manager
        
        # Load model
        if not topic_service.load_model():
            print("❌ Failed to load LDA model")
            return False
        
        print("✅ LDA model loaded successfully")
        
        # Classify pending articles
        classified_count = topic_service.classify_pending_articles()
        print(f"✅ Classified {classified_count} articles")
        
        if classified_count > 0:
            print("📊 Results stored in topic_results table")
        else:
            print("ℹ️ No articles pending classification")
            
    except Exception as e:
        print(f"❌ Topic classification failed: {e}")
        return False
    
    # Step 6: Verify data in tables
    print("\n📊 Step 6: Verifying Data in Tables")
    print("-" * 35)
    
    try:
        with sql_manager.get_connection() as conn:
            # Check raw_articles
            raw_count = conn.execute("SELECT COUNT(*) as count FROM raw_articles").fetchone().count
            print(f"📋 raw_articles table: {raw_count} articles")
            
            # Check preprocessed_articles
            processed_count = conn.execute("SELECT COUNT(*) as count FROM preprocessed_articles").fetchone().count
            print(f"🧹 preprocessed_articles table: {processed_count} articles")
            
            # Check topic_results
            classified_count = conn.execute("SELECT COUNT(*) as count FROM topic_results").fetchone().count
            print(f"🧠 topic_results table: {classified_count} articles")
            
            # Check processing status
            status_query = """
            SELECT processing_status, COUNT(*) as count 
            FROM raw_articles 
            GROUP BY processing_status
            """
            status_results = conn.execute(status_query).fetchall()
            
            print(f"\n📈 Processing Status:")
            for row in status_results:
                print(f"   {row.processing_status}: {row.count} articles")
                
    except Exception as e:
        print(f"❌ Data verification failed: {e}")
        return False
    
    # Step 7: Test Power BI views
    print("\n📊 Step 7: Testing Power BI Views")
    print("-" * 35)
    
    try:
        with sql_manager.get_connection() as conn:
            # Test topic summary view
            summary_query = "SELECT TOP 5 * FROM vw_topic_summary ORDER BY total_articles DESC"
            summary_results = conn.execute(summary_query).fetchall()
            
            print(f"📈 Topic Summary View: {len(summary_results)} topics")
            
            if summary_results:
                print("   Top Topics:")
                for row in summary_results:
                    print(f"   - Topic {row.topic_id}: {row.total_articles} articles (confidence: {row.avg_confidence:.3f})")
            
            # Test KPI metrics view
            kpi_query = "SELECT * FROM vw_kpi_metrics"
            kpi_result = conn.execute(kpi_query).fetchone()
            
            if kpi_result:
                print(f"\n📊 KPI Metrics:")
                print(f"   Total Articles: {kpi_result.total_articles}")
                print(f"   Classification Rate: {kpi_result.classification_rate}%")
                print(f"   Active Topics: {kpi_result.active_topics}")
                print(f"   Unique Sources: {kpi_result.unique_sources}")
                
    except Exception as e:
        print(f"❌ Power BI views test failed: {e}")
        return False
    
    # Step 8: Success summary
    print("\n🎉 Step 8: Workflow Test Complete")
    print("-" * 35)
    
    print("✅ All components working correctly!")
    print("\n🔄 Your Workflow is Ready:")
    print("   1. ✅ Timestamp-based news fetching")
    print("   2. ✅ Raw JSON storage in raw_articles")
    print("   3. ✅ Text preprocessing pipeline")
    print("   4. ✅ LDA topic classification")
    print("   5. ✅ SQL Server integration")
    print("   6. ✅ Power BI DirectQuery views")
    
    print("\n🚀 Start your automated pipeline:")
    print("   python automated_pipeline.py --fetch-interval 15")
    
    return True

def show_configuration_summary():
    """Show current configuration summary."""
    
    print("\n⚙️ CONFIGURATION SUMMARY")
    print("=" * 30)
    
    # Check API key
    api_key = os.getenv('NEWS_API_KEY')
    print(f"📡 NewsAPI Key: {'✅ Set' if api_key else '❌ Not set'}")
    
    # Check model
    model_exists = Path("models/lda_model.pkl").exists()
    print(f"🧠 LDA Model: {'✅ Found' if model_exists else '❌ Not found'}")
    
    # Check database config
    db_config_exists = Path("config/database.json").exists()
    print(f"🗄️ Database Config: {'✅ Found' if db_config_exists else '❌ Not found'}")
    
    # Show recommended intervals
    print(f"\n⏰ Recommended Intervals:")
    print(f"   📡 News Fetch: 15 minutes (balance of freshness and API limits)")
    print(f"   📊 Power BI Refresh: 5 minutes (near real-time dashboards)")
    
    print(f"\n🎯 Next Steps:")
    if not api_key:
        print("   1. Set NEWS_API_KEY environment variable")
    if not model_exists:
        print("   2. Place your LDA model at models/lda_model.pkl")
    if not db_config_exists:
        print("   3. Configure database connection in config/database.json")
    
    if api_key and model_exists and db_config_exists:
        print("   🚀 Ready to run: python test_workflow.py")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Your Exact Workflow')
    parser.add_argument('--config', action='store_true', help='Show configuration summary')
    
    args = parser.parse_args()
    
    if args.config:
        show_configuration_summary()
    else:
        success = test_complete_workflow()
        if not success:
            print("\n❌ Workflow test failed. Check the errors above.")
            sys.exit(1)
        else:
            print("\n✅ Workflow test passed! Your pipeline is ready.")
