#!/usr/bin/env python3
"""
LDA Integration Service
Loads your trained .pkl model and integrates with the preprocessing pipeline
"""

import pickle
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
import logging
from pathlib import Path
import sqlite3
import os
from datetime import datetime

# Import preprocessing module
from preprocess import AdvancedTextPreprocessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LDATopicModelingService:
    """Complete LDA topic modeling service using your trained model."""
    
    def __init__(self, 
                 model_path: str = "models/lda_model.pkl",
                 data_dir: str = "data",
                 use_database: bool = False):
        """
        Initialize LDA topic modeling service.
        
        Args:
            model_path: Path to your trained .pkl model
            data_dir: Directory containing data files
            use_database: Whether to use database instead of CSV
        """
        self.model_path = Path(model_path)
        self.data_dir = Path(data_dir)
        self.use_database = use_database
        
        # Model components
        self.model = None
        self.vectorizer = None
        self.topics = None
        self.model_metadata = None
        self.is_loaded = False
        
        # File paths
        self.raw_articles_file = self.data_dir / "raw_articles.csv"
        self.processed_articles_file = self.data_dir / "processed_articles.csv"
        self.database_file = self.data_dir / "news.db"
        
        # Initialize preprocessor
        self.preprocessor = AdvancedTextPreprocessor(use_spacy=True)
        
        # Load model
        self.load_model()
        
        logger.info(f"🧠 LDA service initialized (Database: {use_database})")
    
    def load_model(self) -> bool:
        """
        Load your trained LDA model from .pkl file.
        
        Returns:
            True if model loaded successfully, False otherwise
        """
        
        # Check if model file exists
        if not self.model_path.exists():
            logger.error(f"❌ Model file not found: {self.model_path}")
            
            # Try alternative paths
            alternative_paths = [
                Path("models/latest_model.pkl"),
                Path("models/topic_model.pkl"),
                Path("lda_model.pkl")
            ]
            
            for alt_path in alternative_paths:
                if alt_path.exists():
                    logger.info(f"🔄 Found alternative model: {alt_path}")
                    self.model_path = alt_path
                    break
            else:
                logger.error("❌ No model file found in any location")
                return False
        
        try:
            logger.info(f"🧠 Loading LDA model from: {self.model_path}")
            
            # Load pickle file
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # Validate model structure
            if not self._validate_model_structure(model_data):
                return False
            
            # Extract components
            self.model = model_data['model']
            self.vectorizer = model_data['vectorizer']
            self.topics = model_data['topics']
            self.model_metadata = model_data.get('metadata', {})
            
            logger.info(f"✅ Model loaded successfully")
            logger.info(f"📊 Found {len(self.topics)} topics")
            logger.info(f"🔧 Model method: {model_data.get('method', 'unknown')}")
            
            # Display topics
            self._display_topics()
            
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            return False
    
    def _validate_model_structure(self, model_data: Dict) -> bool:
        """Validate the structure of the loaded model."""
        
        required_keys = ['model', 'vectorizer', 'topics']
        missing_keys = []
        
        for key in required_keys:
            if key not in model_data:
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"❌ Model missing required keys: {missing_keys}")
            return False
        
        # Validate topics structure
        topics = model_data['topics']
        if not isinstance(topics, dict):
            logger.error("❌ Topics should be a dictionary")
            return False
        
        # Check topic structure
        for topic_id, topic_info in topics.items():
            if not isinstance(topic_info, dict):
                logger.error(f"❌ Topic {topic_id} should be a dictionary")
                return False
            
            if 'label' not in topic_info:
                logger.error(f"❌ Topic {topic_id} missing 'label'")
                return False
            
            if 'keywords' not in topic_info:
                logger.error(f"❌ Topic {topic_id} missing 'keywords'")
                return False
        
        return True
    
    def _display_topics(self):
        """Display topic information."""
        
        logger.info("📋 Available topics:")
        for topic_id, topic_info in self.topics.items():
            label = topic_info['label']
            keywords = ', '.join(topic_info['keywords'][:5])
            logger.info(f"  Topic {topic_id}: {label}")
            logger.info(f"    Keywords: {keywords}")
    
    def classify_text(self, text: str, 
                     confidence_threshold: float = 0.3,
                     preprocess: bool = True) -> Optional[Dict]:
        """
        Classify a single text using the LDA model.
        
        Args:
            text: Text to classify
            confidence_threshold: Minimum confidence for classification
            preprocess: Whether to preprocess the text
            
        Returns:
            Classification result or None if confidence too low
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return None
        
        if not text or len(text.strip()) < 20:
            logger.debug("⚠️ Text too short for classification")
            return None
        
        try:
            # Preprocess text if requested
            if preprocess:
                processed_text = self.preprocessor.preprocess_text(text)
                if not processed_text:
                    logger.debug("⚠️ Text became empty after preprocessing")
                    return None
            else:
                processed_text = text
            
            # Vectorize text
            text_vector = self.vectorizer.transform([processed_text])
            
            # Get topic probabilities
            topic_probs = self.model.transform(text_vector)[0]
            
            # Find best topic
            best_topic_id = np.argmax(topic_probs)
            best_confidence = topic_probs[best_topic_id]
            
            # Check confidence threshold
            if best_confidence < confidence_threshold:
                logger.debug(f"⚠️ Low confidence: {best_confidence:.3f} < {confidence_threshold}")
                return None
            
            # Get topic information
            topic_info = self.topics[best_topic_id]
            
            result = {
                'topic_id': int(best_topic_id),
                'topic_label': topic_info['label'],
                'topic_confidence': float(best_confidence),
                'topic_keywords': topic_info['keywords'][:5],
                'all_probabilities': topic_probs.tolist()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Classification failed: {e}")
            return None
    
    def classify_articles_batch(self, articles_df: pd.DataFrame,
                               text_column: str = 'full_text',
                               confidence_threshold: float = 0.3,
                               batch_size: int = 100) -> pd.DataFrame:
        """
        Classify multiple articles from DataFrame.
        
        Args:
            articles_df: DataFrame with articles
            text_column: Column containing text to classify
            confidence_threshold: Minimum confidence for classification
            batch_size: Process in batches for memory efficiency
            
        Returns:
            DataFrame with topic classifications added
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return pd.DataFrame()
        
        if articles_df.empty or text_column not in articles_df.columns:
            logger.warning(f"⚠️ DataFrame empty or column '{text_column}' not found")
            return pd.DataFrame()
        
        logger.info(f"🔍 Classifying {len(articles_df)} articles...")
        
        # Preprocess all texts first
        logger.info("🔧 Preprocessing texts...")
        processed_df = self.preprocessor.preprocess_dataframe(
            articles_df, 
            text_column=text_column,
            output_column='processed_text',
            batch_size=batch_size
        )
        
        if processed_df.empty:
            logger.warning("⚠️ No articles left after preprocessing")
            return pd.DataFrame()
        
        # Classify in batches
        classified_articles = []
        successful_classifications = 0
        
        for i in range(0, len(processed_df), batch_size):
            batch = processed_df.iloc[i:i+batch_size]
            
            for idx, row in batch.iterrows():
                try:
                    # Get processed text
                    processed_text = row['processed_text']
                    
                    # Classify (don't preprocess again)
                    classification = self.classify_text(
                        processed_text, 
                        confidence_threshold=confidence_threshold,
                        preprocess=False
                    )
                    
                    # Create result row
                    result_row = row.to_dict()
                    
                    if classification:
                        # Add classification results
                        result_row.update(classification)
                        successful_classifications += 1
                    else:
                        # Add empty classification
                        result_row.update({
                            'topic_id': -1,
                            'topic_label': 'Unknown',
                            'topic_confidence': 0.0,
                            'topic_keywords': [],
                            'all_probabilities': []
                        })
                    
                    # Add classification timestamp
                    result_row['classified_at'] = datetime.now().isoformat()
                    
                    classified_articles.append(result_row)
                    
                except Exception as e:
                    logger.error(f"❌ Failed to classify article {idx}: {e}")
                    continue
            
            # Progress update
            if i % (batch_size * 5) == 0:
                logger.info(f"  Classified {i + len(batch)}/{len(processed_df)} articles")
        
        logger.info(f"✅ Classification complete: {successful_classifications}/{len(processed_df)} successful")
        
        # Convert to DataFrame
        result_df = pd.DataFrame(classified_articles)
        
        # Filter out failed classifications if desired
        # result_df = result_df[result_df['topic_id'] != -1]
        
        return result_df
    
    def load_raw_articles(self) -> pd.DataFrame:
        """Load raw articles from storage."""
        
        if self.use_database:
            try:
                with sqlite3.connect(self.database_file) as conn:
                    query = "SELECT * FROM articles ORDER BY published_at DESC"
                    return pd.read_sql_query(query, conn)
            except Exception as e:
                logger.error(f"❌ Error loading from database: {e}")
                return pd.DataFrame()
        else:
            try:
                if self.raw_articles_file.exists():
                    return pd.read_csv(self.raw_articles_file)
                else:
                    logger.warning(f"⚠️ Raw articles file not found: {self.raw_articles_file}")
                    return pd.DataFrame()
            except Exception as e:
                logger.error(f"❌ Error loading from CSV: {e}")
                return pd.DataFrame()
    
    def get_unprocessed_articles(self) -> pd.DataFrame:
        """Get articles that haven't been processed yet."""
        
        # Load raw articles
        raw_df = self.load_raw_articles()
        
        if raw_df.empty:
            logger.info("ℹ️ No raw articles found")
            return pd.DataFrame()
        
        # Load processed articles
        try:
            if self.processed_articles_file.exists():
                processed_df = pd.read_csv(self.processed_articles_file)
                
                if not processed_df.empty:
                    # Find unprocessed articles (not in processed file)
                    processed_ids = set(processed_df['article_id'].tolist())
                    unprocessed_df = raw_df[~raw_df['article_id'].isin(processed_ids)]
                else:
                    unprocessed_df = raw_df
            else:
                unprocessed_df = raw_df
            
            logger.info(f"📊 Found {len(unprocessed_df)} unprocessed articles")
            return unprocessed_df
            
        except Exception as e:
            logger.error(f"❌ Error getting unprocessed articles: {e}")
            return raw_df
    
    def save_processed_articles(self, processed_df: pd.DataFrame) -> bool:
        """Save processed articles to storage."""
        
        if processed_df.empty:
            logger.warning("⚠️ No processed articles to save")
            return False
        
        try:
            # Save to CSV (append mode)
            if self.processed_articles_file.exists():
                processed_df.to_csv(self.processed_articles_file, mode='a', header=False, index=False)
            else:
                processed_df.to_csv(self.processed_articles_file, index=False)
            
            logger.info(f"✅ Saved {len(processed_df)} processed articles")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save processed articles: {e}")
            return False
    
    def process_new_articles(self, confidence_threshold: float = 0.3) -> Dict:
        """
        Process all new articles through the complete pipeline.
        
        Args:
            confidence_threshold: Minimum confidence for classification
            
        Returns:
            Dictionary with processing results
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return {'success': False, 'error': 'Model not loaded'}
        
        logger.info("🚀 Starting article processing pipeline...")
        
        # Get unprocessed articles
        unprocessed_df = self.get_unprocessed_articles()
        
        if unprocessed_df.empty:
            logger.info("ℹ️ No new articles to process")
            return {'success': True, 'processed_count': 0, 'message': 'No new articles'}
        
        # Classify articles
        classified_df = self.classify_articles_batch(
            unprocessed_df,
            confidence_threshold=confidence_threshold
        )
        
        if classified_df.empty:
            logger.warning("⚠️ No articles were successfully classified")
            return {'success': False, 'error': 'No successful classifications'}
        
        # Save processed articles
        save_success = self.save_processed_articles(classified_df)
        
        if not save_success:
            return {'success': False, 'error': 'Failed to save processed articles'}
        
        # Calculate statistics
        successful_classifications = len(classified_df[classified_df['topic_id'] != -1])
        
        result = {
            'success': True,
            'total_articles': len(unprocessed_df),
            'processed_count': len(classified_df),
            'successful_classifications': successful_classifications,
            'classification_rate': successful_classifications / len(classified_df) if len(classified_df) > 0 else 0
        }
        
        logger.info(f"🎯 Processing complete: {result}")
        return result
    
    def get_topic_statistics(self) -> Dict:
        """Get statistics about topic distribution."""
        
        try:
            if not self.processed_articles_file.exists():
                return {}
            
            df = pd.read_csv(self.processed_articles_file)
            
            if df.empty:
                return {}
            
            # Filter successful classifications
            df = df[df['topic_id'] != -1]
            
            if df.empty:
                return {}
            
            # Calculate statistics
            topic_stats = df.groupby(['topic_id', 'topic_label']).agg({
                'article_id': 'count',
                'topic_confidence': ['mean', 'std']
            }).round(3)
            
            topic_stats.columns = ['article_count', 'avg_confidence', 'confidence_std']
            topic_stats = topic_stats.reset_index()
            
            # Calculate percentages
            total_articles = topic_stats['article_count'].sum()
            topic_stats['percentage'] = (topic_stats['article_count'] / total_articles * 100).round(1)
            
            return topic_stats.to_dict('records')
            
        except Exception as e:
            logger.error(f"❌ Error getting topic statistics: {e}")
            return {}

def main():
    """Test the LDA topic modeling service."""
    
    # Initialize service
    service = LDATopicModelingService()
    
    if not service.is_loaded:
        logger.error("❌ Cannot test - model not loaded")
        return
    
    # Test single classification
    test_text = "Artificial intelligence and machine learning are transforming healthcare with new diagnostic tools."
    
    result = service.classify_text(test_text)
    if result:
        logger.info(f"🎯 Classification result:")
        logger.info(f"  Topic: {result['topic_label']}")
        logger.info(f"  Confidence: {result['topic_confidence']:.3f}")
        logger.info(f"  Keywords: {', '.join(result['topic_keywords'])}")
    
    # Process new articles
    logger.info("🔄 Processing new articles...")
    process_result = service.process_new_articles()
    logger.info(f"📊 Processing result: {process_result}")
    
    # Get topic statistics
    stats = service.get_topic_statistics()
    if stats:
        logger.info("📈 Topic statistics:")
        for stat in stats:
            logger.info(f"  {stat['topic_label']}: {stat['article_count']} articles ({stat['percentage']}%)")

    def get_unclassified_articles(self) -> List[Dict]:
        """
        Get preprocessed articles that haven't been classified yet.

        Returns:
            List of preprocessed articles ready for topic classification
        """

        if not hasattr(self, 'sql_manager') or not self.sql_manager:
            logger.warning("No SQL connection manager provided")
            return []

        try:
            with self.sql_manager.get_connection() as conn:
                query = """
                SELECT p.preprocessed_id, p.article_id, p.cleaned_text,
                       p.lemmatized_text, p.word_count, p.preprocessing_timestamp,
                       r.title, r.source_name, r.published_at
                FROM preprocessed_articles p
                INNER JOIN raw_articles r ON p.article_id = r.article_id
                LEFT JOIN topic_results t ON p.preprocessed_id = t.preprocessed_id
                WHERE t.preprocessed_id IS NULL
                AND p.word_count >= 50  -- Minimum word count for classification
                ORDER BY p.preprocessing_timestamp DESC
                """

                result = conn.execute(query).fetchall()

                articles = []
                for row in result:
                    articles.append({
                        'preprocessed_id': row.preprocessed_id,
                        'article_id': row.article_id,
                        'cleaned_text': row.cleaned_text,
                        'lemmatized_text': row.lemmatized_text,
                        'word_count': row.word_count,
                        'preprocessing_timestamp': row.preprocessing_timestamp,
                        'title': row.title,
                        'source_name': row.source_name,
                        'published_at': row.published_at
                    })

                logger.info(f"📋 Found {len(articles)} articles ready for classification")
                return articles

        except Exception as e:
            logger.error(f"❌ Failed to get unclassified articles: {e}")
            return []

    def classify_pending_articles(self) -> int:
        """
        Classify all pending preprocessed articles using the LDA model.

        Returns:
            Number of articles successfully classified
        """

        # Ensure model is loaded
        if not self.is_loaded:
            if not self.load_model():
                logger.error("❌ Cannot classify articles - model not loaded")
                return 0

        # Get unclassified articles
        unclassified_articles = self.get_unclassified_articles()

        if not unclassified_articles:
            logger.info("📄 No articles pending classification")
            return 0

        classified_count = 0

        for article in unclassified_articles:
            try:
                start_time = time.time()

                # Classify the article
                classification_result = self._classify_single_text(
                    article['cleaned_text'],
                    article['lemmatized_text']
                )

                processing_duration = int((time.time() - start_time) * 1000)  # milliseconds

                # Store classification results
                if self._store_topic_result(article, classification_result, processing_duration):
                    classified_count += 1
                    logger.debug(f"✅ Classified: {article['title'][:50]}...")
                else:
                    logger.error(f"❌ Failed to store classification: {article['title'][:50]}...")

            except Exception as e:
                logger.error(f"❌ Failed to classify article {article['article_id']}: {e}")
                continue

        logger.info(f"✅ Successfully classified {classified_count}/{len(unclassified_articles)} articles")
        return classified_count

    def _classify_single_text(self, cleaned_text: str, lemmatized_text: str) -> Dict:
        """
        Classify a single text using the loaded LDA model.

        Args:
            cleaned_text: Cleaned article text
            lemmatized_text: Lemmatized article text

        Returns:
            Classification results dictionary
        """

        # Use lemmatized text for better topic modeling results
        text_to_classify = lemmatized_text if lemmatized_text else cleaned_text

        try:
            # Transform text using the vectorizer
            text_vector = self.vectorizer.transform([text_to_classify])

            # Get topic probabilities
            topic_probabilities = self.model.transform(text_vector)[0]

            # Find the dominant topic
            dominant_topic_id = int(np.argmax(topic_probabilities))
            confidence = float(topic_probabilities[dominant_topic_id])

            # Get topic keywords
            topic_keywords = self._get_topic_keywords(dominant_topic_id)

            # Prepare topic distribution (all probabilities)
            topic_distribution = [float(prob) for prob in topic_probabilities]

            return {
                'topic_id': dominant_topic_id,
                'confidence': confidence,
                'topic_distribution': topic_distribution,
                'topic_keywords': topic_keywords
            }

        except Exception as e:
            logger.error(f"❌ Classification failed: {e}")
            # Return default classification
            return {
                'topic_id': -1,  # Unknown topic
                'confidence': 0.0,
                'topic_distribution': [],
                'topic_keywords': []
            }

    def _get_topic_keywords(self, topic_id: int, num_keywords: int = 10) -> List[str]:
        """
        Get top keywords for a specific topic.

        Args:
            topic_id: Topic ID
            num_keywords: Number of top keywords to return

        Returns:
            List of top keywords for the topic
        """

        try:
            if hasattr(self.model, 'components_') and hasattr(self.vectorizer, 'get_feature_names_out'):
                # Get feature names
                feature_names = self.vectorizer.get_feature_names_out()

                # Get topic word distribution
                topic_words = self.model.components_[topic_id]

                # Get top word indices
                top_word_indices = topic_words.argsort()[-num_keywords:][::-1]

                # Get top keywords
                keywords = [feature_names[i] for i in top_word_indices]

                return keywords
            else:
                logger.warning("Cannot extract keywords - model structure not recognized")
                return []

        except Exception as e:
            logger.error(f"❌ Failed to get topic keywords: {e}")
            return []

    def _store_topic_result(self, article: Dict, classification_result: Dict, duration_ms: int) -> bool:
        """
        Store topic classification results in topic_results table.

        Args:
            article: Article data
            classification_result: Classification results
            duration_ms: Processing duration in milliseconds

        Returns:
            True if successful, False otherwise
        """

        if not hasattr(self, 'sql_manager') or not self.sql_manager:
            logger.warning("No SQL connection manager provided")
            return False

        try:
            with self.sql_manager.get_connection() as conn:
                insert_query = """
                INSERT INTO topic_results (
                    article_id, preprocessed_id, topic_id, confidence,
                    topic_distribution, topic_keywords, classification_timestamp,
                    model_version, processing_duration_ms
                ) VALUES (?, ?, ?, ?, ?, ?, GETDATE(), ?, ?)
                """

                conn.execute(insert_query, (
                    article['article_id'],
                    article['preprocessed_id'],
                    classification_result['topic_id'],
                    classification_result['confidence'],
                    json.dumps(classification_result['topic_distribution']),
                    json.dumps(classification_result['topic_keywords']),
                    self.model_metadata.get('version', '1.0') if self.model_metadata else '1.0',
                    duration_ms
                ))

                conn.commit()
                return True

        except Exception as e:
            logger.error(f"❌ Failed to store topic result: {e}")
            return False

if __name__ == "__main__":
    main()
