#!/usr/bin/env python3
"""
LDA Integration Service
Loads your trained .pkl model and integrates with the preprocessing pipeline
"""

import pickle
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
import logging
from pathlib import Path
import sqlite3
import os
from datetime import datetime

# Import preprocessing module
from preprocess import AdvancedTextPreprocessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LDATopicModelingService:
    """Complete LDA topic modeling service using your trained model."""
    
    def __init__(self, 
                 model_path: str = "models/lda_model.pkl",
                 data_dir: str = "data",
                 use_database: bool = False):
        """
        Initialize LDA topic modeling service.
        
        Args:
            model_path: Path to your trained .pkl model
            data_dir: Directory containing data files
            use_database: Whether to use database instead of CSV
        """
        self.model_path = Path(model_path)
        self.data_dir = Path(data_dir)
        self.use_database = use_database
        
        # Model components
        self.model = None
        self.vectorizer = None
        self.topics = None
        self.model_metadata = None
        self.is_loaded = False
        
        # File paths
        self.raw_articles_file = self.data_dir / "raw_articles.csv"
        self.processed_articles_file = self.data_dir / "processed_articles.csv"
        self.database_file = self.data_dir / "news.db"
        
        # Initialize preprocessor
        self.preprocessor = AdvancedTextPreprocessor(use_spacy=True)
        
        # Load model
        self.load_model()
        
        logger.info(f"🧠 LDA service initialized (Database: {use_database})")
    
    def load_model(self) -> bool:
        """
        Load your trained LDA model from .pkl file.
        
        Returns:
            True if model loaded successfully, False otherwise
        """
        
        # Check if model file exists
        if not self.model_path.exists():
            logger.error(f"❌ Model file not found: {self.model_path}")
            
            # Try alternative paths
            alternative_paths = [
                Path("models/latest_model.pkl"),
                Path("models/topic_model.pkl"),
                Path("lda_model.pkl")
            ]
            
            for alt_path in alternative_paths:
                if alt_path.exists():
                    logger.info(f"🔄 Found alternative model: {alt_path}")
                    self.model_path = alt_path
                    break
            else:
                logger.error("❌ No model file found in any location")
                return False
        
        try:
            logger.info(f"🧠 Loading LDA model from: {self.model_path}")
            
            # Load pickle file
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # Validate model structure
            if not self._validate_model_structure(model_data):
                return False
            
            # Extract components
            self.model = model_data['model']
            self.vectorizer = model_data['vectorizer']
            self.topics = model_data['topics']
            self.model_metadata = model_data.get('metadata', {})
            
            logger.info(f"✅ Model loaded successfully")
            logger.info(f"📊 Found {len(self.topics)} topics")
            logger.info(f"🔧 Model method: {model_data.get('method', 'unknown')}")
            
            # Display topics
            self._display_topics()
            
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            return False
    
    def _validate_model_structure(self, model_data: Dict) -> bool:
        """Validate the structure of the loaded model."""
        
        required_keys = ['model', 'vectorizer', 'topics']
        missing_keys = []
        
        for key in required_keys:
            if key not in model_data:
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"❌ Model missing required keys: {missing_keys}")
            return False
        
        # Validate topics structure
        topics = model_data['topics']
        if not isinstance(topics, dict):
            logger.error("❌ Topics should be a dictionary")
            return False
        
        # Check topic structure
        for topic_id, topic_info in topics.items():
            if not isinstance(topic_info, dict):
                logger.error(f"❌ Topic {topic_id} should be a dictionary")
                return False
            
            if 'label' not in topic_info:
                logger.error(f"❌ Topic {topic_id} missing 'label'")
                return False
            
            if 'keywords' not in topic_info:
                logger.error(f"❌ Topic {topic_id} missing 'keywords'")
                return False
        
        return True
    
    def _display_topics(self):
        """Display topic information."""
        
        logger.info("📋 Available topics:")
        for topic_id, topic_info in self.topics.items():
            label = topic_info['label']
            keywords = ', '.join(topic_info['keywords'][:5])
            logger.info(f"  Topic {topic_id}: {label}")
            logger.info(f"    Keywords: {keywords}")
    
    def classify_text(self, text: str, 
                     confidence_threshold: float = 0.3,
                     preprocess: bool = True) -> Optional[Dict]:
        """
        Classify a single text using the LDA model.
        
        Args:
            text: Text to classify
            confidence_threshold: Minimum confidence for classification
            preprocess: Whether to preprocess the text
            
        Returns:
            Classification result or None if confidence too low
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return None
        
        if not text or len(text.strip()) < 20:
            logger.debug("⚠️ Text too short for classification")
            return None
        
        try:
            # Preprocess text if requested
            if preprocess:
                processed_text = self.preprocessor.preprocess_text(text)
                if not processed_text:
                    logger.debug("⚠️ Text became empty after preprocessing")
                    return None
            else:
                processed_text = text
            
            # Vectorize text
            text_vector = self.vectorizer.transform([processed_text])
            
            # Get topic probabilities
            topic_probs = self.model.transform(text_vector)[0]
            
            # Find best topic
            best_topic_id = np.argmax(topic_probs)
            best_confidence = topic_probs[best_topic_id]
            
            # Check confidence threshold
            if best_confidence < confidence_threshold:
                logger.debug(f"⚠️ Low confidence: {best_confidence:.3f} < {confidence_threshold}")
                return None
            
            # Get topic information
            topic_info = self.topics[best_topic_id]
            
            result = {
                'topic_id': int(best_topic_id),
                'topic_label': topic_info['label'],
                'topic_confidence': float(best_confidence),
                'topic_keywords': topic_info['keywords'][:5],
                'all_probabilities': topic_probs.tolist()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Classification failed: {e}")
            return None
    
    def classify_articles_batch(self, articles_df: pd.DataFrame,
                               text_column: str = 'full_text',
                               confidence_threshold: float = 0.3,
                               batch_size: int = 100) -> pd.DataFrame:
        """
        Classify multiple articles from DataFrame.
        
        Args:
            articles_df: DataFrame with articles
            text_column: Column containing text to classify
            confidence_threshold: Minimum confidence for classification
            batch_size: Process in batches for memory efficiency
            
        Returns:
            DataFrame with topic classifications added
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return pd.DataFrame()
        
        if articles_df.empty or text_column not in articles_df.columns:
            logger.warning(f"⚠️ DataFrame empty or column '{text_column}' not found")
            return pd.DataFrame()
        
        logger.info(f"🔍 Classifying {len(articles_df)} articles...")
        
        # Preprocess all texts first
        logger.info("🔧 Preprocessing texts...")
        processed_df = self.preprocessor.preprocess_dataframe(
            articles_df, 
            text_column=text_column,
            output_column='processed_text',
            batch_size=batch_size
        )
        
        if processed_df.empty:
            logger.warning("⚠️ No articles left after preprocessing")
            return pd.DataFrame()
        
        # Classify in batches
        classified_articles = []
        successful_classifications = 0
        
        for i in range(0, len(processed_df), batch_size):
            batch = processed_df.iloc[i:i+batch_size]
            
            for idx, row in batch.iterrows():
                try:
                    # Get processed text
                    processed_text = row['processed_text']
                    
                    # Classify (don't preprocess again)
                    classification = self.classify_text(
                        processed_text, 
                        confidence_threshold=confidence_threshold,
                        preprocess=False
                    )
                    
                    # Create result row
                    result_row = row.to_dict()
                    
                    if classification:
                        # Add classification results
                        result_row.update(classification)
                        successful_classifications += 1
                    else:
                        # Add empty classification
                        result_row.update({
                            'topic_id': -1,
                            'topic_label': 'Unknown',
                            'topic_confidence': 0.0,
                            'topic_keywords': [],
                            'all_probabilities': []
                        })
                    
                    # Add classification timestamp
                    result_row['classified_at'] = datetime.now().isoformat()
                    
                    classified_articles.append(result_row)
                    
                except Exception as e:
                    logger.error(f"❌ Failed to classify article {idx}: {e}")
                    continue
            
            # Progress update
            if i % (batch_size * 5) == 0:
                logger.info(f"  Classified {i + len(batch)}/{len(processed_df)} articles")
        
        logger.info(f"✅ Classification complete: {successful_classifications}/{len(processed_df)} successful")
        
        # Convert to DataFrame
        result_df = pd.DataFrame(classified_articles)
        
        # Filter out failed classifications if desired
        # result_df = result_df[result_df['topic_id'] != -1]
        
        return result_df
    
    def load_raw_articles(self) -> pd.DataFrame:
        """Load raw articles from storage."""
        
        if self.use_database:
            try:
                with sqlite3.connect(self.database_file) as conn:
                    query = "SELECT * FROM articles ORDER BY published_at DESC"
                    return pd.read_sql_query(query, conn)
            except Exception as e:
                logger.error(f"❌ Error loading from database: {e}")
                return pd.DataFrame()
        else:
            try:
                if self.raw_articles_file.exists():
                    return pd.read_csv(self.raw_articles_file)
                else:
                    logger.warning(f"⚠️ Raw articles file not found: {self.raw_articles_file}")
                    return pd.DataFrame()
            except Exception as e:
                logger.error(f"❌ Error loading from CSV: {e}")
                return pd.DataFrame()
    
    def get_unprocessed_articles(self) -> pd.DataFrame:
        """Get articles that haven't been processed yet."""
        
        # Load raw articles
        raw_df = self.load_raw_articles()
        
        if raw_df.empty:
            logger.info("ℹ️ No raw articles found")
            return pd.DataFrame()
        
        # Load processed articles
        try:
            if self.processed_articles_file.exists():
                processed_df = pd.read_csv(self.processed_articles_file)
                
                if not processed_df.empty:
                    # Find unprocessed articles (not in processed file)
                    processed_ids = set(processed_df['article_id'].tolist())
                    unprocessed_df = raw_df[~raw_df['article_id'].isin(processed_ids)]
                else:
                    unprocessed_df = raw_df
            else:
                unprocessed_df = raw_df
            
            logger.info(f"📊 Found {len(unprocessed_df)} unprocessed articles")
            return unprocessed_df
            
        except Exception as e:
            logger.error(f"❌ Error getting unprocessed articles: {e}")
            return raw_df
    
    def save_processed_articles(self, processed_df: pd.DataFrame) -> bool:
        """Save processed articles to storage."""
        
        if processed_df.empty:
            logger.warning("⚠️ No processed articles to save")
            return False
        
        try:
            # Save to CSV (append mode)
            if self.processed_articles_file.exists():
                processed_df.to_csv(self.processed_articles_file, mode='a', header=False, index=False)
            else:
                processed_df.to_csv(self.processed_articles_file, index=False)
            
            logger.info(f"✅ Saved {len(processed_df)} processed articles")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save processed articles: {e}")
            return False
    
    def process_new_articles(self, confidence_threshold: float = 0.3) -> Dict:
        """
        Process all new articles through the complete pipeline.
        
        Args:
            confidence_threshold: Minimum confidence for classification
            
        Returns:
            Dictionary with processing results
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return {'success': False, 'error': 'Model not loaded'}
        
        logger.info("🚀 Starting article processing pipeline...")
        
        # Get unprocessed articles
        unprocessed_df = self.get_unprocessed_articles()
        
        if unprocessed_df.empty:
            logger.info("ℹ️ No new articles to process")
            return {'success': True, 'processed_count': 0, 'message': 'No new articles'}
        
        # Classify articles
        classified_df = self.classify_articles_batch(
            unprocessed_df,
            confidence_threshold=confidence_threshold
        )
        
        if classified_df.empty:
            logger.warning("⚠️ No articles were successfully classified")
            return {'success': False, 'error': 'No successful classifications'}
        
        # Save processed articles
        save_success = self.save_processed_articles(classified_df)
        
        if not save_success:
            return {'success': False, 'error': 'Failed to save processed articles'}
        
        # Calculate statistics
        successful_classifications = len(classified_df[classified_df['topic_id'] != -1])
        
        result = {
            'success': True,
            'total_articles': len(unprocessed_df),
            'processed_count': len(classified_df),
            'successful_classifications': successful_classifications,
            'classification_rate': successful_classifications / len(classified_df) if len(classified_df) > 0 else 0
        }
        
        logger.info(f"🎯 Processing complete: {result}")
        return result
    
    def get_topic_statistics(self) -> Dict:
        """Get statistics about topic distribution."""
        
        try:
            if not self.processed_articles_file.exists():
                return {}
            
            df = pd.read_csv(self.processed_articles_file)
            
            if df.empty:
                return {}
            
            # Filter successful classifications
            df = df[df['topic_id'] != -1]
            
            if df.empty:
                return {}
            
            # Calculate statistics
            topic_stats = df.groupby(['topic_id', 'topic_label']).agg({
                'article_id': 'count',
                'topic_confidence': ['mean', 'std']
            }).round(3)
            
            topic_stats.columns = ['article_count', 'avg_confidence', 'confidence_std']
            topic_stats = topic_stats.reset_index()
            
            # Calculate percentages
            total_articles = topic_stats['article_count'].sum()
            topic_stats['percentage'] = (topic_stats['article_count'] / total_articles * 100).round(1)
            
            return topic_stats.to_dict('records')
            
        except Exception as e:
            logger.error(f"❌ Error getting topic statistics: {e}")
            return {}

def main():
    """Test the LDA topic modeling service."""
    
    # Initialize service
    service = LDATopicModelingService()
    
    if not service.is_loaded:
        logger.error("❌ Cannot test - model not loaded")
        return
    
    # Test single classification
    test_text = "Artificial intelligence and machine learning are transforming healthcare with new diagnostic tools."
    
    result = service.classify_text(test_text)
    if result:
        logger.info(f"🎯 Classification result:")
        logger.info(f"  Topic: {result['topic_label']}")
        logger.info(f"  Confidence: {result['topic_confidence']:.3f}")
        logger.info(f"  Keywords: {', '.join(result['topic_keywords'])}")
    
    # Process new articles
    logger.info("🔄 Processing new articles...")
    process_result = service.process_new_articles()
    logger.info(f"📊 Processing result: {process_result}")
    
    # Get topic statistics
    stats = service.get_topic_statistics()
    if stats:
        logger.info("📈 Topic statistics:")
        for stat in stats:
            logger.info(f"  {stat['topic_label']}: {stat['article_count']} articles ({stat['percentage']}%)")

if __name__ == "__main__":
    main()
