{"_comment": "SQL Server Database Configuration for Topic Modeling Pipeline", "_description": "Configure your SQL Server connection settings below", "server": "localhost", "database": "TopicModelingDB", "username": null, "password": null, "trusted_connection": true, "driver": "ODBC Driver 17 for SQL Server", "pool_size": 10, "max_overflow": 20, "pool_timeout": 30, "_examples": {"local_windows_auth": {"server": "localhost", "database": "TopicModelingDB", "trusted_connection": true}, "local_sql_auth": {"server": "localhost", "database": "TopicModelingDB", "username": "your_username", "password": "your_password", "trusted_connection": false}, "remote_server": {"server": "your-server.database.windows.net", "database": "TopicModelingDB", "username": "your_username", "password": "your_password", "trusted_connection": false, "driver": "ODBC Driver 17 for SQL Server"}, "azure_sql": {"server": "your-server.database.windows.net", "database": "TopicModelingDB", "username": "your_username@your-server", "password": "your_password", "trusted_connection": false, "driver": "ODBC Driver 17 for SQL Server"}}, "_notes": ["For Windows Authentication, set trusted_connection to true and leave username/password as null", "For SQL Server Authentication, set trusted_connection to false and provide username/password", "Make sure the ODBC driver is installed on your system", "For Azure SQL Database, include the server name in the username (username@servername)", "Pool settings control connection pooling for better performance"]}