#!/usr/bin/env python3
"""
Project restructuring script to align with real-time workflow.
Reorganizes files and removes redundant components.
"""

import os
import shutil
from pathlib import Path

def create_new_structure():
    """Create the new aligned directory structure."""
    
    # New structure aligned with real-time workflow
    new_structure = {
        'core/': 'Core real-time processing components',
        'core/collectors/': 'Data collection modules',
        'core/models/': 'LDA model management',
        'core/processors/': 'Data processing pipeline',
        'core/exporters/': 'Dashboard data exporters',
        'backend/': 'Backend service orchestration',
        'data/': 'Data storage',
        'data/hourly/': 'Hourly collected news data',
        'data/processed/': 'Processed and classified data',
        'data/dashboard/': 'Power BI ready exports',
        'models/': 'Trained LDA models (.pkl files)',
        'config/': 'Configuration files',
        'docs/': 'Documentation',
        'docs/setup/': 'Installation and setup guides',
        'docs/dashboard/': 'Power BI integration guides',
        'tests/': 'Test suite',
        'logs/': 'Application logs',
        'scripts/': 'Utility and maintenance scripts'
    }
    
    print("🗂️  Creating new directory structure...")
    for directory, description in new_structure.items():
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory} - {description}")

def reorganize_core_files():
    """Reorganize core processing files."""
    
    file_moves = [
        # Core collectors
        ('src/hourly_collector.py', 'core/collectors/hourly_news.py'),
        ('src/data_ingestion.py', 'core/collectors/news_sources.py'),
        
        # Core models
        ('src/lda_service.py', 'core/models/lda_classifier.py'),
        ('src/topic_modeling.py', 'core/models/model_trainer.py'),
        
        # Core processors
        ('src/preprocessing.py', 'core/processors/text_processor.py'),
        
        # Core exporters
        ('src/data_export.py', 'core/exporters/powerbi_exporter.py'),
        
        # Core utilities
        ('src/config.py', 'core/config_manager.py'),
        ('src/utils.py', 'core/utils.py'),
        
        # Backend service
        ('backend_service.py', 'backend/realtime_service.py'),
        
        # Configuration
        ('config.yaml', 'config/pipeline.yaml'),
        ('.env.example', 'config/environment.example'),
        
        # Scripts
        ('setup.py', 'scripts/setup.py'),
        ('run_tests.py', 'scripts/run_tests.py'),
    ]
    
    print("\n📁 Reorganizing core files...")
    for old_path, new_path in file_moves:
        if Path(old_path).exists():
            # Create parent directory if it doesn't exist
            Path(new_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Move file
            shutil.move(old_path, new_path)
            print(f"   ✅ {old_path} → {new_path}")
        else:
            print(f"   ⚠️  {old_path} not found, skipping...")

def reorganize_documentation():
    """Reorganize documentation files."""
    
    doc_moves = [
        # Setup documentation
        ('INSTALL.md', 'docs/setup/INSTALLATION.md'),
        ('docs/CONFIGURATION.md', 'docs/setup/CONFIGURATION.md'),
        ('docs/DEPLOYMENT.md', 'docs/setup/DEPLOYMENT.md'),
        ('docs/SCHEDULING.md', 'docs/setup/SCHEDULING.md'),
        
        # Dashboard documentation
        ('docs/POWERBI_INTEGRATION.md', 'docs/dashboard/POWERBI_SETUP.md'),
        ('docs/REALTIME_POWERBI.md', 'docs/dashboard/REALTIME_DASHBOARD.md'),
        ('docs/powerbi_template.json', 'docs/dashboard/dashboard_template.json'),
    ]
    
    print("\n📚 Reorganizing documentation...")
    for old_path, new_path in doc_moves:
        if Path(old_path).exists():
            Path(new_path).parent.mkdir(parents=True, exist_ok=True)
            shutil.move(old_path, new_path)
            print(f"   ✅ {old_path} → {new_path}")

def create_new_main_scripts():
    """Create new main execution scripts aligned with workflow."""
    
    # Main real-time service launcher
    main_service = """#!/usr/bin/env python3
\"\"\"
Real-Time Topic Modeling Service
Main entry point for the hourly news collection and classification system.
\"\"\"

import sys
from pathlib import Path

# Add core modules to path
sys.path.insert(0, str(Path(__file__).parent / 'core'))
sys.path.insert(0, str(Path(__file__).parent / 'backend'))

from realtime_service import TopicModelingBackend

def main():
    \"\"\"Main entry point.\"\"\"
    import argparse
    
    parser = argparse.ArgumentParser(description="Real-Time Topic Modeling Service")
    parser.add_argument('action', choices=['start', 'stop', 'status', 'train'], 
                       help='Service action')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    
    args = parser.parse_args()
    
    if args.action == 'train':
        # Train initial model
        from model_trainer import train_initial_model
        train_initial_model()
    else:
        # Handle service commands
        backend = TopicModelingBackend()
        
        if args.action == 'start':
            backend.start_backend_service()
            if not args.daemon:
                try:
                    input("Press Enter to stop service...")
                except KeyboardInterrupt:
                    pass
                backend.stop_backend_service()
        elif args.action == 'stop':
            backend.stop_backend_service()
        elif args.action == 'status':
            status = backend.get_service_status()
            print(status)

if __name__ == "__main__":
    main()
"""
    
    with open('start_service.py', 'w') as f:
        f.write(main_service)
    
    print("   ✅ Created start_service.py - Main service launcher")

def remove_redundant_files():
    """Remove files that don't align with the new workflow."""
    
    redundant_files = [
        'run_pipeline.py',  # Replaced by start_service.py
        'pytest.ini',       # Moving to scripts/
        'src/',            # Reorganized into core/
        'docs/POWERBI_INTEGRATION.md',  # Moved and renamed
        'docs/REALTIME_POWERBI.md',     # Moved
    ]
    
    print("\n🗑️  Removing redundant files...")
    for file_path in redundant_files:
        path = Path(file_path)
        if path.exists():
            if path.is_dir():
                shutil.rmtree(path)
                print(f"   ✅ Removed directory: {file_path}")
            else:
                path.unlink()
                print(f"   ✅ Removed file: {file_path}")

def update_imports_in_files():
    """Update import statements in moved files."""
    
    import_updates = {
        'core/collectors/hourly_news.py': [
            ('from .data_ingestion import', 'from .news_sources import'),
            ('from .config import', 'from ..config_manager import'),
            ('from .utils import', 'from ..utils import'),
        ],
        'core/models/lda_classifier.py': [
            ('from .preprocessing import', 'from ..processors.text_processor import'),
            ('from .config import', 'from ..config_manager import'),
            ('from .utils import', 'from ..utils import'),
        ],
        'backend/realtime_service.py': [
            ('from src.hourly_collector import', 'from core.collectors.hourly_news import'),
            ('from src.lda_service import', 'from core.models.lda_classifier import'),
            ('from src.config import', 'from core.config_manager import'),
            ('from src.utils import', 'from core.utils import'),
        ]
    }
    
    print("\n🔄 Updating import statements...")
    for file_path, updates in import_updates.items():
        if Path(file_path).exists():
            with open(file_path, 'r') as f:
                content = f.read()
            
            for old_import, new_import in updates:
                content = content.replace(old_import, new_import)
            
            with open(file_path, 'w') as f:
                f.write(content)
            
            print(f"   ✅ Updated imports in {file_path}")

def create_new_readme():
    """Create updated README for the new structure."""
    
    readme_content = """# 🔍 Real-Time Topic Modeling Pipeline

**Hourly News Collection → CSV Processing → LDA Classification → Live Power BI Dashboard**

## 🎯 **Aligned Workflow**

```
📰 Hourly Collection → 📄 CSV Feed → 🧠 LDA (.pkl) → 📊 Real-Time Dashboard
```

## 🗂️ **Project Structure**

```
topic_modelling/
├── 📁 core/                         # Core processing components
│   ├── collectors/                  # Data collection modules
│   │   ├── hourly_news.py          # Hourly news collection
│   │   └── news_sources.py         # NewsAPI & RSS integration
│   ├── models/                      # LDA model management
│   │   ├── lda_classifier.py       # Real-time classification service
│   │   └── model_trainer.py        # Model training pipeline
│   ├── processors/                  # Data processing
│   │   └── text_processor.py       # Text preprocessing
│   ├── exporters/                   # Dashboard exports
│   │   └── powerbi_exporter.py     # Power BI data preparation
│   ├── config_manager.py           # Configuration management
│   └── utils.py                    # Utility functions
├── 📁 backend/                      # Backend orchestration
│   └── realtime_service.py         # Main backend service
├── 📁 data/                         # Data storage
│   ├── hourly/                     # Hourly collected news
│   ├── processed/                  # Classified articles
│   └── dashboard/                  # Power BI exports
├── 📁 models/                       # Trained LDA models (.pkl)
├── 📁 config/                       # Configuration files
│   ├── pipeline.yaml              # Main configuration
│   └── environment.example        # Environment template
├── 📁 docs/                         # Documentation
│   ├── setup/                      # Installation guides
│   └── dashboard/                  # Power BI guides
├── 📁 scripts/                      # Utility scripts
└── start_service.py                # Main service launcher
```

## 🚀 **Quick Start**

### 1. **Setup**
```bash
# Install dependencies
pip install -r requirements.txt
python scripts/setup.py

# Configure API key
cp config/environment.example config/.env
# Edit config/.env with your NewsAPI key
```

### 2. **Train Initial Model**
```bash
# Train LDA model (one-time setup)
python start_service.py train
```

### 3. **Start Real-Time Service**
```bash
# Start hourly collection and classification
python start_service.py start

# Or run as daemon
python start_service.py start --daemon
```

### 4. **Monitor Status**
```bash
# Check service status
python start_service.py status

# View logs
tail -f logs/realtime_service.log
```

## 📊 **Power BI Dashboard**

The service automatically generates:
- `data/dashboard/hourly_trends.csv` - Hourly topic trends
- `data/dashboard/realtime_articles.csv` - Latest classified articles
- `data/dashboard/topic_summary.csv` - Topic statistics

See `docs/dashboard/REALTIME_DASHBOARD.md` for setup instructions.

## 🔄 **Real-Time Data Flow**

1. **Hourly Collection**: NewsAPI → `data/hourly/news_YYYYMMDD_HH.csv`
2. **LDA Classification**: CSV → Trained Model (.pkl) → Topics
3. **Dashboard Export**: Classified Data → Power BI CSV files
4. **Auto-Refresh**: Power BI updates every hour

## 📚 **Documentation**

- [Installation Guide](docs/setup/INSTALLATION.md)
- [Configuration](docs/setup/CONFIGURATION.md)
- [Real-Time Dashboard](docs/dashboard/REALTIME_DASHBOARD.md)
- [Deployment](docs/setup/DEPLOYMENT.md)

## 🎯 **Key Features**

✅ **Hourly automated news collection**  
✅ **CSV-based data pipeline**  
✅ **Trained LDA models (.pkl format)**  
✅ **Real-time topic classification**  
✅ **Live Power BI dashboard updates**  
✅ **Production-ready backend service**  

---

**Ready for real-time topic insights? Start with the installation guide!** 🚀
"""
    
    with open('README.md', 'w') as f:
        f.write(readme_content)
    
    print("   ✅ Created new aligned README.md")

def main():
    """Main restructuring function."""
    print("🔄 Restructuring project for real-time workflow alignment...")
    print("=" * 60)
    
    # Create new structure
    create_new_structure()
    
    # Reorganize files
    reorganize_core_files()
    reorganize_documentation()
    
    # Create new main scripts
    create_new_main_scripts()
    
    # Update imports
    update_imports_in_files()
    
    # Create new README
    create_new_readme()
    
    # Remove redundant files
    remove_redundant_files()
    
    print("\n" + "=" * 60)
    print("✅ Project restructuring completed!")
    print("\n🎯 New workflow-aligned structure:")
    print("   📰 Hourly Collection → 📄 CSV → 🧠 LDA (.pkl) → 📊 Dashboard")
    print("\n🚀 Next steps:")
    print("   1. python scripts/setup.py")
    print("   2. python start_service.py train")
    print("   3. python start_service.py start")

if __name__ == "__main__":
    main()
