"""
Tests for text preprocessing functionality.
"""

import pytest
import pandas as pd
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Mock the config to avoid dependency issues in tests
with patch('src.preprocessing.config') as mock_config:
    mock_config.preprocessing = {
        'min_word_length': 3,
        'max_word_length': 20,
        'remove_stopwords': True,
        'lemmatize': True,
        'remove_numbers': True,
        'remove_punctuation': True,
        'custom_stopwords': ['test_stopword']
    }
    from src.preprocessing import TextPreprocessor

class TestTextPreprocessor:
    """Test text preprocessing functionality."""
    
    @pytest.fixture
    def preprocessor(self):
        """Create a text preprocessor instance."""
        return TextPreprocessor()
    
    def test_clean_html(self, preprocessor):
        """Test HTML cleaning functionality."""
        # Test HTML tag removal
        html_text = "<p>This is a <strong>test</strong> article.</p>"
        cleaned = preprocessor.clean_html(html_text)
        assert cleaned == "This is a test article."
        
        # Test HTML entity decoding
        entity_text = "This &amp; that &lt;test&gt;"
        cleaned = preprocessor.clean_html(entity_text)
        assert cleaned == "This & that <test>"
        
        # Test empty input
        assert preprocessor.clean_html("") == ""
        assert preprocessor.clean_html(None) == ""
    
    def test_clean_text(self, preprocessor):
        """Test basic text cleaning."""
        # Test URL removal
        text_with_url = "Check this out: https://example.com/article"
        cleaned = preprocessor.clean_text(text_with_url)
        assert "https://example.com/article" not in cleaned
        
        # Test email removal
        text_with_email = "Contact <NAME_EMAIL> for more info"
        cleaned = preprocessor.clean_text(text_with_email)
        assert "<EMAIL>" not in cleaned
        
        # Test whitespace normalization
        text_with_spaces = "This   has    multiple     spaces"
        cleaned = preprocessor.clean_text(text_with_spaces)
        assert cleaned == "This has multiple spaces"
    
    def test_tokenize(self, preprocessor):
        """Test tokenization functionality."""
        text = "This is a test sentence."
        tokens = preprocessor.tokenize(text)
        
        assert isinstance(tokens, list)
        assert len(tokens) > 0
        assert "this" in tokens  # Should be lowercase
        assert "test" in tokens
        
        # Test empty input
        assert preprocessor.tokenize("") == []
        assert preprocessor.tokenize(None) == []
    
    def test_filter_tokens(self, preprocessor):
        """Test token filtering."""
        tokens = ["this", "is", "a", "test", "123", "verylongwordthatshouldbefilteredout", "x"]
        filtered = preprocessor.filter_tokens(tokens)
        
        # Should remove short words (< 3 chars)
        assert "a" not in filtered
        assert "x" not in filtered
        
        # Should remove numbers if configured
        assert "123" not in filtered
        
        # Should remove very long words
        assert "verylongwordthatshouldbefilteredout" not in filtered
        
        # Should keep valid words
        assert "test" in filtered
    
    def test_preprocess_text(self, preprocessor):
        """Test complete text preprocessing pipeline."""
        text = "<p>This is a TEST article with some URLs https://example.com and numbers 123.</p>"
        
        # Test returning processed text
        processed_text = preprocessor.preprocess_text(text)
        assert isinstance(processed_text, str)
        assert len(processed_text) > 0
        assert "https://example.com" not in processed_text
        
        # Test returning tokens
        tokens = preprocessor.preprocess_text(text, return_tokens=True)
        assert isinstance(tokens, list)
        assert len(tokens) > 0
    
    def test_preprocess_dataframe(self, preprocessor):
        """Test DataFrame preprocessing."""
        # Create test DataFrame
        test_data = {
            'title': [
                'Test Article 1',
                'Another Test Article',
                'Third Article'
            ],
            'description': [
                'This is a test description.',
                'Another description here.',
                'Third description.'
            ],
            'content': [
                'This is the full content of the first article.',
                'Content of the second article with more text.',
                'Third article content.'
            ]
        }
        
        df = pd.DataFrame(test_data)
        processed_df = preprocessor.preprocess_dataframe(df)
        
        # Check that required columns are added
        assert 'combined_text' in processed_df.columns
        assert 'cleaned_text' in processed_df.columns
        assert 'tokens' in processed_df.columns
        assert 'token_count' in processed_df.columns
        assert 'char_count' in processed_df.columns
        
        # Check that data is processed
        assert len(processed_df) > 0
        assert all(processed_df['token_count'] > 0)
        assert all(processed_df['char_count'] > 0)
    
    def test_empty_dataframe(self, preprocessor):
        """Test handling of empty DataFrame."""
        empty_df = pd.DataFrame()
        result = preprocessor.preprocess_dataframe(empty_df)
        assert result.empty
    
    def test_missing_columns(self, preprocessor):
        """Test handling of DataFrame with missing required columns."""
        df_no_title = pd.DataFrame({'description': ['test']})
        
        with pytest.raises(ValueError):
            preprocessor.preprocess_dataframe(df_no_title)

class TestTextProcessingEdgeCases:
    """Test edge cases in text processing."""
    
    @pytest.fixture
    def preprocessor(self):
        return TextPreprocessor()
    
    def test_special_characters(self, preprocessor):
        """Test handling of special characters."""
        text = "Text with émojis 😀 and spëcial chàracters"
        processed = preprocessor.preprocess_text(text)
        assert isinstance(processed, str)
    
    def test_very_long_text(self, preprocessor):
        """Test processing of very long text."""
        long_text = "word " * 10000  # 10,000 words
        processed = preprocessor.preprocess_text(long_text)
        assert isinstance(processed, str)
        assert len(processed) > 0
    
    def test_only_stopwords(self, preprocessor):
        """Test text that contains only stopwords."""
        stopword_text = "the and or but"
        processed = preprocessor.preprocess_text(stopword_text)
        # Should return empty string or very short string
        assert len(processed) < len(stopword_text)
    
    def test_mixed_languages(self, preprocessor):
        """Test text with mixed languages."""
        mixed_text = "English text with some français words"
        processed = preprocessor.preprocess_text(mixed_text)
        assert isinstance(processed, str)
        assert "english" in processed.lower()
    
    def test_numeric_only_text(self, preprocessor):
        """Test text that is only numbers."""
        numeric_text = "123 456 789"
        processed = preprocessor.preprocess_text(numeric_text)
        # Should be empty or very short since numbers are filtered
        assert len(processed) == 0 or processed.strip() == ""
