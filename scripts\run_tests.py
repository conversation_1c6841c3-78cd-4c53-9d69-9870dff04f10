#!/usr/bin/env python3
"""
Test runner for the topic modeling pipeline.
Provides different test execution options and reporting.
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_tests(test_type='all', verbose=True, coverage=False, html_report=False):
    """
    Run tests with specified options.
    
    Args:
        test_type: Type of tests to run ('all', 'unit', 'integration', 'fast')
        verbose: Whether to run in verbose mode
        coverage: Whether to generate coverage report
        html_report: Whether to generate HTML coverage report
    """
    
    # Base pytest command
    cmd = ['python', '-m', 'pytest']
    
    # Add verbosity
    if verbose:
        cmd.append('-v')
    
    # Add coverage if requested
    if coverage:
        cmd.extend(['--cov=src', '--cov-report=term-missing'])
        if html_report:
            cmd.append('--cov-report=html')
    
    # Select tests based on type
    if test_type == 'unit':
        cmd.extend(['-m', 'not integration'])
    elif test_type == 'integration':
        cmd.extend(['-m', 'integration'])
    elif test_type == 'fast':
        cmd.extend(['-m', 'not slow'])
    elif test_type == 'all':
        pass  # Run all tests
    else:
        print(f"Unknown test type: {test_type}")
        return False
    
    # Add test directory
    cmd.append('tests/')
    
    print(f"Running command: {' '.join(cmd)}")
    print("=" * 50)
    
    # Run tests
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return False
    except Exception as e:
        print(f"Error running tests: {e}")
        return False

def check_dependencies():
    """Check if required test dependencies are installed."""
    required_packages = ['pytest', 'pandas', 'numpy', 'scikit-learn']
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_test_environment():
    """Set up test environment."""
    # Create test directories if they don't exist
    test_dirs = ['tests', 'data', 'logs', 'models']
    
    for dir_name in test_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("Test environment set up successfully")

def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Run topic modeling pipeline tests")
    
    parser.add_argument(
        '--type', 
        choices=['all', 'unit', 'integration', 'fast'],
        default='all',
        help='Type of tests to run'
    )
    
    parser.add_argument(
        '--coverage',
        action='store_true',
        help='Generate coverage report'
    )
    
    parser.add_argument(
        '--html',
        action='store_true',
        help='Generate HTML coverage report'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Run in quiet mode'
    )
    
    parser.add_argument(
        '--setup',
        action='store_true',
        help='Set up test environment only'
    )
    
    parser.add_argument(
        '--check-deps',
        action='store_true',
        help='Check test dependencies only'
    )
    
    args = parser.parse_args()
    
    # Check dependencies
    if args.check_deps:
        if check_dependencies():
            print("✅ All dependencies are installed")
            return 0
        else:
            return 1
    
    # Set up environment
    if args.setup:
        setup_test_environment()
        return 0
    
    # Check dependencies before running tests
    if not check_dependencies():
        print("❌ Missing dependencies. Use --check-deps to see details.")
        return 1
    
    # Set up test environment
    setup_test_environment()
    
    # Run tests
    print(f"🧪 Running {args.type} tests...")
    
    success = run_tests(
        test_type=args.type,
        verbose=not args.quiet,
        coverage=args.coverage,
        html_report=args.html
    )
    
    if success:
        print("\n✅ All tests passed!")
        if args.coverage and args.html:
            print("📊 Coverage report generated in htmlcov/index.html")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
