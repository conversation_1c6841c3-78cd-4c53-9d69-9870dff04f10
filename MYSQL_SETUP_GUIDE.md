# 🗄️ **MySQL Database Setup Guide**

## 📋 **Step-by-Step Manual Setup in MySQL Workbench**

### **Step 1: Open MySQL Workbench**
1. Launch MySQL Workbench
2. Connect to your MySQL server
3. Open a new SQL tab

### **Step 2: Create Database and Tables**

#### **Copy and paste this entire script into MySQL Workbench:**

```sql
-- =====================================================
-- MySQL Schema for Real-Time Topic Modeling
-- Run this entire script in MySQL Workbench
-- =====================================================

-- Create database
CREATE DATABASE IF NOT EXISTS TopicModelingDB;
USE TopicModelingDB;

-- Raw articles table (stores original JSON response from NewsAPI)
CREATE TABLE raw_articles (
    article_id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    content LONGTEXT,
    source_name VARCHAR(100) NOT NULL,
    source_id VARCHAR(50),
    author VARCHAR(200),
    url VARCHAR(1000) UNIQUE,
    url_to_image VARCHAR(1000),
    published_at DATETIME NOT NULL,
    fetched_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    content_hash VARCHAR(64) UNIQUE,
    language VARCHAR(10) DEFAULT 'en',
    raw_json LONGTEXT,
    processing_status VARCHAR(20) DEFAULT 'pending',
    
    INDEX IX_raw_articles_published_at (published_at DESC),
    INDEX IX_raw_articles_fetched_at (fetched_at DESC),
    INDEX IX_raw_articles_content_hash (content_hash),
    INDEX IX_raw_articles_status (processing_status)
);

-- Preprocessed articles table
CREATE TABLE preprocessed_articles (
    preprocessed_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id VARCHAR(50) NOT NULL,
    cleaned_text LONGTEXT NOT NULL,
    tokens LONGTEXT,
    lemmatized_text LONGTEXT,
    word_count INT,
    sentence_count INT,
    language_detected VARCHAR(10),
    preprocessing_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    preprocessing_duration_ms INT,
    
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    UNIQUE KEY UQ_preprocessed_article (article_id),
    INDEX IX_preprocessed_articles_timestamp (preprocessing_timestamp DESC)
);

-- Topics master table
CREATE TABLE topics (
    topic_id INT PRIMARY KEY,
    topic_label VARCHAR(100) NOT NULL,
    topic_keywords LONGTEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Topic results table
CREATE TABLE topic_results (
    result_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id VARCHAR(50) NOT NULL,
    preprocessed_id BIGINT NOT NULL,
    topic_id INT NOT NULL,
    confidence DECIMAL(8,6) NOT NULL,
    topic_distribution LONGTEXT,
    topic_keywords LONGTEXT,
    classification_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    model_version VARCHAR(20) DEFAULT '1.0',
    processing_duration_ms INT,
    
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (preprocessed_id) REFERENCES preprocessed_articles(preprocessed_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    UNIQUE KEY UQ_topic_result_preprocessed (preprocessed_id),
    INDEX IX_topic_results_topic_id (topic_id),
    INDEX IX_topic_results_timestamp (classification_timestamp DESC)
);

-- Insert sample topics
INSERT INTO topics (topic_id, topic_label, topic_keywords) VALUES
(0, 'Technology', '["technology", "tech", "innovation", "digital", "software", "ai"]'),
(1, 'Politics', '["politics", "government", "election", "policy", "political"]'),
(2, 'Business', '["business", "economy", "market", "financial", "company"]'),
(3, 'Health', '["health", "medical", "healthcare", "medicine", "hospital"]'),
(4, 'Science', '["science", "research", "study", "scientific", "discovery"]'),
(5, 'Sports', '["sports", "game", "team", "player", "championship"]'),
(6, 'Entertainment', '["entertainment", "movie", "music", "celebrity", "film"]'),
(7, 'Environment', '["environment", "climate", "green", "sustainability"]');

-- Verify tables were created
SHOW TABLES;
SELECT * FROM topics;
```

### **Step 3: Create Power BI Views**

#### **Copy and paste this script for Power BI views:**

```sql
-- =====================================================
-- Power BI DirectQuery Views for MySQL
-- =====================================================

USE TopicModelingDB;

-- Topic summary view
CREATE OR REPLACE VIEW vw_topic_summary AS
SELECT 
    t.topic_id,
    t.topic_label,
    COUNT(tr.result_id) as total_articles,
    AVG(tr.confidence) as avg_confidence,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    MAX(tr.classification_timestamp) as last_updated,
    SUM(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 ELSE 0 END) as articles_last_hour,
    SUM(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as articles_last_24h
FROM topics t
LEFT JOIN topic_results tr ON t.topic_id = tr.topic_id
LEFT JOIN raw_articles ra ON tr.article_id = ra.article_id
GROUP BY t.topic_id, t.topic_label;

-- KPI metrics view
CREATE OR REPLACE VIEW vw_kpi_metrics AS
SELECT 
    COUNT(DISTINCT ra.article_id) as total_articles,
    COUNT(DISTINCT t.topic_id) as active_topics,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    AVG(tr.confidence) as overall_avg_confidence,
    MAX(ra.published_at) as latest_article_time,
    MAX(tr.classification_timestamp) as latest_classification_time
FROM raw_articles ra
LEFT JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
LEFT JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
LEFT JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Hourly trends view
CREATE OR REPLACE VIEW vw_hourly_trends AS
SELECT 
    DATE_FORMAT(tr.classification_timestamp, '%Y-%m-%d %H:00:00') as hour_bucket,
    t.topic_id,
    t.topic_label,
    COUNT(tr.result_id) as article_count,
    AVG(tr.confidence) as avg_confidence
FROM topic_results tr
INNER JOIN topics t ON tr.topic_id = t.topic_id
WHERE tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE_FORMAT(tr.classification_timestamp, '%Y-%m-%d %H:00:00'), t.topic_id, t.topic_label
ORDER BY hour_bucket DESC;

-- Latest articles view
CREATE OR REPLACE VIEW vw_realtime_articles AS
SELECT 
    ra.article_id,
    ra.title,
    ra.source_name,
    ra.published_at,
    t.topic_label,
    tr.confidence,
    tr.classification_timestamp
FROM raw_articles ra
INNER JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
INNER JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
INNER JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY ra.published_at DESC
LIMIT 100;

-- Verify views
SELECT 'Views created successfully' as status;
SHOW FULL TABLES WHERE Table_type = 'VIEW';
```

### **Step 4: Update Database Configuration**

Edit `config/database.json` for MySQL:

```json
{
  "server": "localhost",
  "database": "TopicModelingDB",
  "username": "your_mysql_username",
  "password": "your_mysql_password",
  "port": 3306,
  "trusted_connection": false,
  "driver": "mysql+pymysql"
}
```

### **Step 5: Install MySQL Python Driver**

```bash
pip install pymysql mysql-connector-python
```

### **Step 6: Test Your Setup**

```bash
# Test database connection
python -c "
from database.sql_connection import SQLConnectionManager
manager = SQLConnectionManager.from_config_file('config/database.json')
with manager.get_connection() as conn:
    result = conn.execute('SELECT COUNT(*) as count FROM topics').fetchone()
    print(f'Topics in database: {result.count}')
"
```

## 🔧 **Alternative: Use the Complete Schema Files**

### **Option A: Run the complete schema file**
1. Open `database/mysql_schema.sql` in MySQL Workbench
2. Execute the entire file (Ctrl+Shift+Enter)

### **Option B: Run the Power BI views file**
1. Open `database/mysql_powerbi_views.sql` in MySQL Workbench
2. Execute the entire file

## ✅ **Verification Steps**

After running the scripts, verify everything is working:

```sql
-- Check tables were created
SHOW TABLES;

-- Check topics were inserted
SELECT * FROM topics;

-- Check views were created
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- Test a view
SELECT * FROM vw_topic_summary;
```

## 🚀 **Next Steps**

Once your database is set up:

1. **Set your NewsAPI key:**
   ```bash
   set NEWS_API_KEY=your_actual_newsapi_key_here
   ```

2. **Place your LDA model:**
   ```bash
   cp your_model.pkl models/lda_model.pkl
   ```

3. **Test the workflow:**
   ```bash
   python test_workflow.py
   ```

4. **Run the automated pipeline:**
   ```bash
   python automated_pipeline.py --single-run
   ```

## 🔍 **Troubleshooting**

### **Common MySQL Issues:**

- **"Access denied"** → Check username/password in `config/database.json`
- **"Database doesn't exist"** → Run the CREATE DATABASE command first
- **"Table already exists"** → Drop tables first or use IF NOT EXISTS
- **"Foreign key constraint fails"** → Create tables in the correct order

### **Connection Issues:**

- **"No module named 'pymysql'"** → Run `pip install pymysql`
- **"Can't connect to MySQL server"** → Check if MySQL service is running
- **"Unknown database"** → Make sure TopicModelingDB was created

Your MySQL database is now ready for the automated topic modeling pipeline! 🎉
