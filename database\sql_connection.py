#!/usr/bin/env python3
"""
MySQL Connection Manager
High-performance database connection and operations for topic modeling pipeline
"""

try:
    import pymysql
    pymysql.install_as_MySQLdb()
except ImportError:
    pymysql = None

import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text, MetaData, Table
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from contextlib import contextmanager
import os
from pathlib import Path
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SQLConnectionManager:
    """
    High-performance MySQL connection manager with connection pooling,
    retry logic, and optimized bulk operations.
    """
    
    def __init__(self,
                 server: str,
                 database: str,
                 username: str = None,
                 password: str = None,
                 driver: str = "mysql+pymysql",
                 trusted_connection: bool = False,
                 pool_size: int = 10,
                 max_overflow: int = 20,
                 pool_timeout: int = 30,
                 port: int = 3306):
        """
        Initialize MySQL connection manager.

        Args:
            server: MySQL server instance name or IP
            database: Database name
            username: MySQL username
            password: MySQL password
            driver: Database driver (mysql+pymysql)
            trusted_connection: Not used for MySQL
            pool_size: Connection pool size
            max_overflow: Maximum overflow connections
            pool_timeout: Pool timeout in seconds
            port: MySQL port (default: 3306)
        """
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.driver = driver
        self.port = port

        # Build connection string
        self.connection_string = self._build_connection_string()

        # Create SQLAlchemy engine with connection pooling
        self.engine = create_engine(
            self.connection_string,
            poolclass=QueuePool,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=pool_timeout,
            pool_pre_ping=True,  # Validate connections before use
            echo=False  # Set to True for SQL debugging
        )

        # Create session factory
        self.SessionLocal = sessionmaker(bind=self.engine)

        # Test connection
        self._test_connection()

        logger.info(f"✅ MySQL connection manager initialized for {server}/{database}")
    
    def _build_connection_string(self) -> str:
        """Build MySQL connection string."""

        if self.username and self.password:
            return f"mysql+pymysql://{self.username}:{self.password}@{self.server}:{self.port}/{self.database}"
        else:
            return f"mysql+pymysql://{self.server}:{self.port}/{self.database}"

    
    def _test_connection(self):
        """Test database connection."""
        
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                
                if test_value != 1:
                    raise Exception("Connection test failed")
                
                logger.info("🔗 Database connection test successful")
                
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup."""
        
        conn = None
        try:
            conn = self.engine.connect()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"❌ Database operation failed: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    @contextmanager
    def get_session(self):
        """Get SQLAlchemy session with automatic cleanup."""
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"❌ Database session failed: {e}")
            raise
        finally:
            session.close()
    
    def execute_query(self, query: str, params: Dict = None) -> pd.DataFrame:
        """
        Execute SELECT query and return DataFrame.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            DataFrame with query results
        """
        
        try:
            with self.get_connection() as conn:
                df = pd.read_sql(query, conn, params=params)
                logger.debug(f"📊 Query returned {len(df)} rows")
                return df
                
        except Exception as e:
            logger.error(f"❌ Query execution failed: {e}")
            raise
    
    def execute_non_query(self, query: str, params: Dict = None) -> int:
        """
        Execute INSERT/UPDATE/DELETE query.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Number of affected rows
        """
        
        try:
            with self.get_connection() as conn:
                result = conn.execute(text(query), params or {})
                conn.commit()
                
                affected_rows = result.rowcount
                logger.debug(f"📝 Query affected {affected_rows} rows")
                return affected_rows
                
        except Exception as e:
            logger.error(f"❌ Non-query execution failed: {e}")
            raise
    
    def bulk_insert_dataframe(self, 
                             df: pd.DataFrame, 
                             table_name: str,
                             if_exists: str = 'append',
                             chunk_size: int = 1000,
                             method: str = 'multi') -> int:
        """
        High-performance bulk insert of DataFrame.
        
        Args:
            df: DataFrame to insert
            table_name: Target table name
            if_exists: What to do if table exists ('append', 'replace', 'fail')
            chunk_size: Number of rows per batch
            method: Insert method ('multi' for bulk)
            
        Returns:
            Number of rows inserted
        """
        
        if df.empty:
            logger.warning("⚠️ Empty DataFrame - no data to insert")
            return 0
        
        try:
            start_time = time.time()
            
            # Clean DataFrame for SQL Server
            df_clean = self._prepare_dataframe_for_sql(df)
            
            # Bulk insert
            rows_inserted = df_clean.to_sql(
                name=table_name,
                con=self.engine,
                if_exists=if_exists,
                index=False,
                chunksize=chunk_size,
                method=method
            )
            
            duration = time.time() - start_time
            rows_per_second = len(df_clean) / duration if duration > 0 else 0
            
            logger.info(f"✅ Bulk inserted {len(df_clean)} rows to {table_name} "
                       f"in {duration:.2f}s ({rows_per_second:.0f} rows/sec)")
            
            return len(df_clean)
            
        except Exception as e:
            logger.error(f"❌ Bulk insert failed for {table_name}: {e}")
            raise
    
    def _prepare_dataframe_for_sql(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare DataFrame for SQL Server insertion."""
        
        df_clean = df.copy()
        
        # Handle datetime columns
        datetime_columns = df_clean.select_dtypes(include=['datetime64']).columns
        for col in datetime_columns:
            # Ensure timezone-naive datetimes for SQL Server
            if df_clean[col].dt.tz is not None:
                df_clean[col] = df_clean[col].dt.tz_localize(None)
        
        # Handle NaN values
        df_clean = df_clean.replace({np.nan: None})
        
        # Handle infinite values
        numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            df_clean[col] = df_clean[col].replace([np.inf, -np.inf], None)
        
        # Truncate text fields that might be too long
        text_columns = df_clean.select_dtypes(include=['object']).columns
        for col in text_columns:
            if col in ['title', 'description']:
                df_clean[col] = df_clean[col].astype(str).str[:500]
            elif col in ['content', 'full_text']:
                df_clean[col] = df_clean[col].astype(str).str[:8000]  # SQL Server text limit
            elif col in ['url']:
                df_clean[col] = df_clean[col].astype(str).str[:1000]
        
        return df_clean
    
    def call_stored_procedure(self, 
                             proc_name: str, 
                             params: Dict = None) -> Optional[pd.DataFrame]:
        """
        Call stored procedure with parameters.
        
        Args:
            proc_name: Stored procedure name
            params: Procedure parameters
            
        Returns:
            DataFrame if procedure returns results, None otherwise
        """
        
        try:
            with self.get_connection() as conn:
                # Build parameter string
                if params:
                    param_string = ", ".join([f"@{k} = :{k}" for k in params.keys()])
                    query = f"EXEC {proc_name} {param_string}"
                else:
                    query = f"EXEC {proc_name}"
                
                result = conn.execute(text(query), params or {})
                
                # Check if procedure returns results
                if result.returns_rows:
                    df = pd.DataFrame(result.fetchall(), columns=result.keys())
                    logger.debug(f"📊 Stored procedure returned {len(df)} rows")
                    return df
                else:
                    conn.commit()
                    logger.debug(f"✅ Stored procedure {proc_name} executed successfully")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Stored procedure {proc_name} failed: {e}")
            raise
    
    def upsert_daily_trends(self, trends_data: List[Dict]) -> int:
        """
        Upsert daily trends using stored procedure.
        
        Args:
            trends_data: List of daily trend dictionaries
            
        Returns:
            Number of records processed
        """
        
        if not trends_data:
            return 0
        
        try:
            processed_count = 0
            
            for trend in trends_data:
                self.call_stored_procedure('usp_UpsertDailyTrends', {
                    'date_key': trend['date_key'],
                    'topic_id': trend['topic_id'],
                    'topic_label': trend['topic_label'],
                    'article_count': trend['article_count'],
                    'avg_confidence': trend['avg_confidence'],
                    'confidence_std': trend.get('confidence_std', 0),
                    'min_confidence': trend['min_confidence'],
                    'max_confidence': trend['max_confidence'],
                    'percentage_of_day': trend.get('percentage_of_day', 0)
                })
                processed_count += 1
            
            logger.info(f"✅ Upserted {processed_count} daily trend records")
            return processed_count
            
        except Exception as e:
            logger.error(f"❌ Daily trends upsert failed: {e}")
            raise
    
    def upsert_hourly_trends(self, trends_data: List[Dict]) -> int:
        """
        Upsert hourly trends using stored procedure.
        
        Args:
            trends_data: List of hourly trend dictionaries
            
        Returns:
            Number of records processed
        """
        
        if not trends_data:
            return 0
        
        try:
            processed_count = 0
            
            for trend in trends_data:
                self.call_stored_procedure('usp_UpsertHourlyTrends', {
                    'hour_key': trend['hour_key'],
                    'topic_id': trend['topic_id'],
                    'topic_label': trend['topic_label'],
                    'article_count': trend['article_count'],
                    'avg_confidence': trend['avg_confidence'],
                    'confidence_std': trend.get('confidence_std', 0),
                    'hour_of_day': trend['hour_of_day'],
                    'day_of_week': trend['day_of_week']
                })
                processed_count += 1
            
            logger.info(f"✅ Upserted {processed_count} hourly trend records")
            return processed_count
            
        except Exception as e:
            logger.error(f"❌ Hourly trends upsert failed: {e}")
            raise
    
    def get_connection_info(self) -> Dict:
        """Get connection information for diagnostics."""
        
        return {
            'server': self.server,
            'database': self.database,
            'driver': self.driver,
            'trusted_connection': self.trusted_connection,
            'pool_size': self.engine.pool.size(),
            'checked_out_connections': self.engine.pool.checkedout(),
            'overflow_connections': self.engine.pool.overflow(),
            'checked_in_connections': self.engine.pool.checkedin()
        }
    
    def health_check(self) -> Dict:
        """Perform comprehensive health check."""
        
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'connection_status': 'UNKNOWN',
            'response_time_ms': None,
            'error': None
        }
        
        try:
            start_time = time.time()
            
            with self.get_connection() as conn:
                # Test basic connectivity
                result = conn.execute(text("SELECT GETDATE() as current_time, @@VERSION as version"))
                row = result.fetchone()
                
                response_time = (time.time() - start_time) * 1000
                
                health_status.update({
                    'connection_status': 'HEALTHY',
                    'response_time_ms': round(response_time, 2),
                    'server_time': row[0].isoformat() if row[0] else None,
                    'sql_version': row[1][:100] if row[1] else None  # Truncate version string
                })
                
        except Exception as e:
            health_status.update({
                'connection_status': 'FAILED',
                'error': str(e)
            })
        
        return health_status

def create_connection_from_config(config_path: str = "config/database.json") -> SQLConnectionManager:
    """
    Create connection manager from configuration file.
    
    Args:
        config_path: Path to database configuration file
        
    Returns:
        Configured SQLConnectionManager instance
    """
    
    try:
        # Try to load from config file
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            # Fallback to environment variables
            config = {
                'server': os.getenv('SQL_SERVER', 'localhost'),
                'database': os.getenv('SQL_DATABASE', 'TopicModelingDB'),
                'username': os.getenv('SQL_USERNAME'),
                'password': os.getenv('SQL_PASSWORD'),
                'trusted_connection': os.getenv('SQL_TRUSTED_CONNECTION', 'false').lower() == 'true'
            }
        
        return SQLConnectionManager(**config)
        
    except Exception as e:
        logger.error(f"❌ Failed to create connection from config: {e}")
        raise

def main():
    """Test the SQL connection manager."""
    
    # Test connection
    try:
        # Create connection from environment variables
        conn_manager = create_connection_from_config()
        
        # Test health check
        health = conn_manager.health_check()
        logger.info(f"🏥 Health check: {health}")
        
        # Test simple query
        df = conn_manager.execute_query("SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES")
        logger.info(f"📊 Database has {df.iloc[0]['table_count']} tables")
        
        # Test connection info
        info = conn_manager.get_connection_info()
        logger.info(f"🔗 Connection info: {info}")
        
    except Exception as e:
        logger.error(f"❌ Connection test failed: {e}")

if __name__ == "__main__":
    main()
