# Topic Modeling Pipeline Cron Jobs
# Add these lines to your crontab using: crontab -e

# Run pipeline every hour at 5 minutes past the hour
5 * * * * cd /path/to/topic_modelling && python scripts/run_pipeline.py run >> logs/cron.log 2>&1

# Alternative schedules (uncomment as needed):

# Run every 2 hours at 5 minutes past
# 5 */2 * * * cd /path/to/topic_modelling && python scripts/run_pipeline.py run >> logs/cron.log 2>&1

# Run daily at 9:05 AM
# 5 9 * * * cd /path/to/topic_modelling && python scripts/run_pipeline.py run >> logs/cron.log 2>&1

# Run twice daily (9:05 AM and 9:05 PM)
# 5 9,21 * * * cd /path/to/topic_modelling && python scripts/run_pipeline.py run >> logs/cron.log 2>&1

# Run only on weekdays at 9:05 AM
# 5 9 * * 1-5 cd /path/to/topic_modelling && python scripts/run_pipeline.py run >> logs/cron.log 2>&1

# Run weekly on Mondays at 9:05 AM
# 5 9 * * 1 cd /path/to/topic_modelling && python scripts/run_pipeline.py run >> logs/cron.log 2>&1

# Status check every 6 hours
# 0 */6 * * * cd /path/to/topic_modelling && python scripts/run_pipeline.py status >> logs/status.log 2>&1

# Instructions:
# 1. Replace "/path/to/topic_modelling" with your actual project path
# 2. Choose one of the schedule options above
# 3. Add to crontab: crontab -e
# 4. Paste the selected line(s)
# 5. Save and exit
# 6. Verify with: crontab -l
