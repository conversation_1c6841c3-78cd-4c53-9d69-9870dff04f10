#!/usr/bin/env python3
"""
Comprehensive Power BI Export System
Creates multiple visualization-ready datasets for Power BI dashboards
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from pathlib import Path
import sqlite3
import os

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PowerBIExportSystem:
    """Comprehensive Power BI export system for topic modeling results."""
    
    def __init__(self, 
                 data_dir: str = "data",
                 export_dir: str = "data/powerbi",
                 use_database: bool = False):
        """
        Initialize Power BI export system.
        
        Args:
            data_dir: Directory containing processed data
            export_dir: Directory for Power BI export files
            use_database: Whether to use database instead of CSV
        """
        self.data_dir = Path(data_dir)
        self.export_dir = Path(export_dir)
        self.use_database = use_database
        
        # Create export directory
        self.export_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.processed_articles_file = self.data_dir / "processed_articles.csv"
        self.database_file = self.data_dir / "news.db"
        
        # Export file paths
        self.exports = {
            'topic_trends': self.export_dir / "topic_trends.csv",
            'hourly_trends': self.export_dir / "hourly_trends.csv",
            'daily_trends': self.export_dir / "daily_trends.csv",
            'article_details': self.export_dir / "article_details.csv",
            'topic_summary': self.export_dir / "topic_summary.csv",
            'keyword_analysis': self.export_dir / "keyword_analysis.csv",
            'source_analysis': self.export_dir / "source_analysis.csv",
            'confidence_analysis': self.export_dir / "confidence_analysis.csv"
        }
        
        logger.info(f"📊 Power BI export system initialized")
    
    def load_processed_articles(self) -> pd.DataFrame:
        """Load processed articles from storage."""
        
        if self.use_database:
            try:
                with sqlite3.connect(self.database_file) as conn:
                    # Assuming processed articles are in the same table with topic columns
                    query = """
                        SELECT * FROM articles 
                        WHERE topic_id IS NOT NULL 
                        ORDER BY published_at DESC
                    """
                    return pd.read_sql_query(query, conn)
            except Exception as e:
                logger.error(f"❌ Error loading from database: {e}")
                return pd.DataFrame()
        else:
            try:
                if self.processed_articles_file.exists():
                    df = pd.read_csv(self.processed_articles_file)
                    
                    # Convert timestamps
                    df['published_at'] = pd.to_datetime(df['published_at'])
                    df['classified_at'] = pd.to_datetime(df['classified_at'])
                    
                    return df
                else:
                    logger.warning(f"⚠️ Processed articles file not found: {self.processed_articles_file}")
                    return pd.DataFrame()
            except Exception as e:
                logger.error(f"❌ Error loading processed articles: {e}")
                return pd.DataFrame()
    
    def create_topic_trends(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create topic trends over time for Power BI."""
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Filter successful classifications
            df_classified = df[df['topic_id'] != -1].copy()
            
            if df_classified.empty:
                return pd.DataFrame()
            
            # Create time-based trends (daily)
            df_classified['date'] = df_classified['published_at'].dt.date
            
            trends = df_classified.groupby(['date', 'topic_id', 'topic_label']).agg({
                'article_id': 'count',
                'topic_confidence': ['mean', 'std']
            }).reset_index()
            
            # Flatten column names
            trends.columns = [
                'date', 'topic_id', 'topic_label', 
                'article_count', 'avg_confidence', 'confidence_std'
            ]
            
            # Fill NaN std with 0
            trends['confidence_std'] = trends['confidence_std'].fillna(0)
            
            # Round values
            trends['avg_confidence'] = trends['avg_confidence'].round(3)
            trends['confidence_std'] = trends['confidence_std'].round(3)
            
            # Calculate percentage of daily total
            daily_totals = trends.groupby('date')['article_count'].sum().reset_index()
            daily_totals.columns = ['date', 'daily_total']
            
            trends = trends.merge(daily_totals, on='date')
            trends['percentage_of_day'] = (trends['article_count'] / trends['daily_total'] * 100).round(1)
            
            # Sort by date and article count
            trends = trends.sort_values(['date', 'article_count'], ascending=[True, False])
            
            logger.info(f"📈 Created topic trends: {len(trends)} data points")
            return trends
            
        except Exception as e:
            logger.error(f"❌ Failed to create topic trends: {e}")
            return pd.DataFrame()
    
    def create_hourly_trends(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create hourly topic trends for Power BI."""
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Filter successful classifications
            df_classified = df[df['topic_id'] != -1].copy()
            
            if df_classified.empty:
                return pd.DataFrame()
            
            # Create hourly trends
            df_classified['hour'] = df_classified['published_at'].dt.floor('H')
            
            hourly_trends = df_classified.groupby(['hour', 'topic_id', 'topic_label']).agg({
                'article_id': 'count',
                'topic_confidence': ['mean', 'std']
            }).reset_index()
            
            # Flatten column names
            hourly_trends.columns = [
                'hour', 'topic_id', 'topic_label', 
                'article_count', 'avg_confidence', 'confidence_std'
            ]
            
            # Fill NaN std with 0
            hourly_trends['confidence_std'] = hourly_trends['confidence_std'].fillna(0)
            
            # Round values
            hourly_trends['avg_confidence'] = hourly_trends['avg_confidence'].round(3)
            hourly_trends['confidence_std'] = hourly_trends['confidence_std'].round(3)
            
            # Add day of week and hour of day
            hourly_trends['day_of_week'] = hourly_trends['hour'].dt.day_name()
            hourly_trends['hour_of_day'] = hourly_trends['hour'].dt.hour
            
            # Sort by hour and article count
            hourly_trends = hourly_trends.sort_values(['hour', 'article_count'], ascending=[True, False])
            
            logger.info(f"⏰ Created hourly trends: {len(hourly_trends)} data points")
            return hourly_trends
            
        except Exception as e:
            logger.error(f"❌ Failed to create hourly trends: {e}")
            return pd.DataFrame()
    
    def create_article_details(self, df: pd.DataFrame, recent_hours: int = 72) -> pd.DataFrame:
        """Create detailed article view for Power BI."""
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Filter recent articles
            cutoff_time = datetime.now() - timedelta(hours=recent_hours)
            recent_df = df[df['published_at'] >= cutoff_time].copy()
            
            if recent_df.empty:
                logger.warning(f"⚠️ No articles found in last {recent_hours} hours")
                return pd.DataFrame()
            
            # Select relevant columns for Power BI
            columns_for_powerbi = [
                'article_id', 'title', 'description', 'source_name', 'author',
                'published_at', 'topic_id', 'topic_label', 'topic_confidence',
                'url', 'classified_at'
            ]
            
            # Filter columns that exist
            available_columns = [col for col in columns_for_powerbi if col in recent_df.columns]
            article_details = recent_df[available_columns].copy()
            
            # Add derived fields
            article_details['title_length'] = article_details['title'].str.len()
            article_details['has_description'] = article_details['description'].notna()
            article_details['confidence_category'] = pd.cut(
                article_details['topic_confidence'], 
                bins=[0, 0.5, 0.7, 0.9, 1.0], 
                labels=['Low', 'Medium', 'High', 'Very High']
            )
            
            # Sort by published time (newest first)
            article_details = article_details.sort_values('published_at', ascending=False)
            
            # Round confidence
            article_details['topic_confidence'] = article_details['topic_confidence'].round(3)
            
            logger.info(f"📰 Created article details: {len(article_details)} articles")
            return article_details
            
        except Exception as e:
            logger.error(f"❌ Failed to create article details: {e}")
            return pd.DataFrame()
    
    def create_topic_summary(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive topic summary for Power BI."""
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Filter successful classifications
            df_classified = df[df['topic_id'] != -1].copy()
            
            if df_classified.empty:
                return pd.DataFrame()
            
            # Group by topic
            topic_summary = df_classified.groupby(['topic_id', 'topic_label']).agg({
                'article_id': 'count',
                'topic_confidence': ['mean', 'std', 'min', 'max'],
                'published_at': ['min', 'max'],
                'source_name': 'nunique'
            }).reset_index()
            
            # Flatten column names
            topic_summary.columns = [
                'topic_id', 'topic_label', 'total_articles',
                'avg_confidence', 'confidence_std', 'min_confidence', 'max_confidence',
                'first_article', 'latest_article', 'unique_sources'
            ]
            
            # Fill NaN std with 0
            topic_summary['confidence_std'] = topic_summary['confidence_std'].fillna(0)
            
            # Round confidence values
            confidence_cols = ['avg_confidence', 'confidence_std', 'min_confidence', 'max_confidence']
            for col in confidence_cols:
                topic_summary[col] = topic_summary[col].round(3)
            
            # Calculate percentage of total articles
            total_articles = topic_summary['total_articles'].sum()
            topic_summary['percentage'] = (topic_summary['total_articles'] / total_articles * 100).round(1)
            
            # Add topic keywords (if available in the data)
            if 'topic_keywords' in df_classified.columns:
                keywords = df_classified.groupby('topic_id')['topic_keywords'].first().reset_index()
                topic_summary = topic_summary.merge(keywords, on='topic_id', how='left')
            
            # Sort by total articles
            topic_summary = topic_summary.sort_values('total_articles', ascending=False)
            
            logger.info(f"📊 Created topic summary: {len(topic_summary)} topics")
            return topic_summary
            
        except Exception as e:
            logger.error(f"❌ Failed to create topic summary: {e}")
            return pd.DataFrame()
    
    def create_source_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create source analysis for Power BI."""
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Filter successful classifications
            df_classified = df[df['topic_id'] != -1].copy()
            
            if df_classified.empty:
                return pd.DataFrame()
            
            # Group by source and topic
            source_analysis = df_classified.groupby(['source_name', 'topic_id', 'topic_label']).agg({
                'article_id': 'count',
                'topic_confidence': 'mean'
            }).reset_index()
            
            source_analysis.columns = [
                'source_name', 'topic_id', 'topic_label', 
                'article_count', 'avg_confidence'
            ]
            
            # Round confidence
            source_analysis['avg_confidence'] = source_analysis['avg_confidence'].round(3)
            
            # Calculate source totals
            source_totals = df_classified.groupby('source_name').agg({
                'article_id': 'count',
                'topic_id': 'nunique'
            }).reset_index()
            
            source_totals.columns = ['source_name', 'total_articles', 'topics_covered']
            
            # Merge with source analysis
            source_analysis = source_analysis.merge(source_totals, on='source_name')
            
            # Calculate percentage within source
            source_analysis['percentage_within_source'] = (
                source_analysis['article_count'] / source_analysis['total_articles'] * 100
            ).round(1)
            
            # Sort by source and article count
            source_analysis = source_analysis.sort_values(['source_name', 'article_count'], ascending=[True, False])
            
            logger.info(f"📰 Created source analysis: {len(source_analysis)} data points")
            return source_analysis
            
        except Exception as e:
            logger.error(f"❌ Failed to create source analysis: {e}")
            return pd.DataFrame()
    
    def create_confidence_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create confidence analysis for Power BI."""
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Filter successful classifications
            df_classified = df[df['topic_id'] != -1].copy()
            
            if df_classified.empty:
                return pd.DataFrame()
            
            # Create confidence bins
            df_classified['confidence_bin'] = pd.cut(
                df_classified['topic_confidence'],
                bins=[0, 0.3, 0.5, 0.7, 0.9, 1.0],
                labels=['0.0-0.3', '0.3-0.5', '0.5-0.7', '0.7-0.9', '0.9-1.0']
            )
            
            # Group by confidence bin and topic
            confidence_analysis = df_classified.groupby(['confidence_bin', 'topic_id', 'topic_label']).agg({
                'article_id': 'count',
                'topic_confidence': ['mean', 'std']
            }).reset_index()
            
            # Flatten column names
            confidence_analysis.columns = [
                'confidence_bin', 'topic_id', 'topic_label',
                'article_count', 'avg_confidence', 'confidence_std'
            ]
            
            # Fill NaN std with 0
            confidence_analysis['confidence_std'] = confidence_analysis['confidence_std'].fillna(0)
            
            # Round values
            confidence_analysis['avg_confidence'] = confidence_analysis['avg_confidence'].round(3)
            confidence_analysis['confidence_std'] = confidence_analysis['confidence_std'].round(3)
            
            # Sort by confidence bin and article count
            confidence_analysis = confidence_analysis.sort_values(['confidence_bin', 'article_count'], ascending=[True, False])
            
            logger.info(f"📊 Created confidence analysis: {len(confidence_analysis)} data points")
            return confidence_analysis
            
        except Exception as e:
            logger.error(f"❌ Failed to create confidence analysis: {e}")
            return pd.DataFrame()
    
    def export_all_datasets(self) -> Dict[str, bool]:
        """
        Export all Power BI datasets.
        
        Returns:
            Dictionary with export status for each dataset
        """
        
        logger.info("🚀 Starting comprehensive Power BI export...")
        
        # Load processed articles
        df = self.load_processed_articles()
        
        if df.empty:
            logger.error("❌ No processed articles to export")
            return {name: False for name in self.exports.keys()}
        
        export_status = {}
        
        # Define export functions
        export_functions = {
            'topic_trends': self.create_topic_trends,
            'hourly_trends': self.create_hourly_trends,
            'article_details': self.create_article_details,
            'topic_summary': self.create_topic_summary,
            'source_analysis': self.create_source_analysis,
            'confidence_analysis': self.create_confidence_analysis
        }
        
        # Export each dataset
        for dataset_name, export_func in export_functions.items():
            try:
                logger.info(f"📊 Creating {dataset_name}...")
                
                dataset = export_func(df)
                
                if not dataset.empty:
                    export_path = self.exports[dataset_name]
                    dataset.to_csv(export_path, index=False)
                    logger.info(f"✅ Exported {dataset_name}: {len(dataset)} rows -> {export_path}")
                    export_status[dataset_name] = True
                else:
                    logger.warning(f"⚠️ Empty dataset: {dataset_name}")
                    export_status[dataset_name] = False
                    
            except Exception as e:
                logger.error(f"❌ Failed to export {dataset_name}: {e}")
                export_status[dataset_name] = False
        
        # Create daily trends (alias for topic trends)
        if export_status.get('topic_trends', False):
            try:
                daily_trends_path = self.exports['daily_trends']
                topic_trends_path = self.exports['topic_trends']
                
                # Copy topic trends as daily trends
                df_topic_trends = pd.read_csv(topic_trends_path)
                df_topic_trends.to_csv(daily_trends_path, index=False)
                
                export_status['daily_trends'] = True
                logger.info(f"✅ Created daily trends alias")
                
            except Exception as e:
                logger.error(f"❌ Failed to create daily trends: {e}")
                export_status['daily_trends'] = False
        
        # Summary
        successful_exports = sum(export_status.values())
        total_exports = len(export_status)
        
        logger.info(f"🎯 Export complete: {successful_exports}/{total_exports} datasets exported successfully")
        
        # Create export summary
        summary = {
            'export_timestamp': datetime.now().isoformat(),
            'total_articles': len(df),
            'successful_exports': successful_exports,
            'total_exports': total_exports,
            'export_status': export_status
        }
        
        # Save export summary
        summary_path = self.export_dir / "export_summary.json"
        import json
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        return export_status

def main():
    """Test the Power BI export system."""
    
    # Create export system
    exporter = PowerBIExportSystem()
    
    # Run comprehensive export
    status = exporter.export_all_datasets()
    
    # Show results
    logger.info("📊 Export Status Summary:")
    for dataset, success in status.items():
        status_icon = "✅" if success else "❌"
        logger.info(f"  {status_icon} {dataset}")
    
    # Show export directory contents
    export_files = list(exporter.export_dir.glob("*.csv"))
    logger.info(f"\n📁 Export directory contains {len(export_files)} CSV files:")
    for file_path in sorted(export_files):
        file_size = file_path.stat().st_size / 1024  # KB
        logger.info(f"  📄 {file_path.name} ({file_size:.1f} KB)")

if __name__ == "__main__":
    main()
