# 🗂️ **Aligned Project Structure**

## 🎯 **Perfect Match with Your Vision**

**Hourly News Collection → CSV Feed → Trained LDA (.pkl) → Real-Time Power BI Dashboard**

## 📁 **New Directory Structure**

```
topic_modelling/
├── 📁 core/                         # Core real-time processing components
│   ├── 📁 collectors/               # Data collection modules
│   │   ├── hourly_news.py          # ⏰ Hourly news collection service
│   │   └── news_sources.py         # 📰 NewsAPI & RSS integration
│   ├── 📁 models/                   # LDA model management
│   │   ├── lda_classifier.py       # 🧠 Real-time classification (.pkl)
│   │   └── model_trainer.py        # 🎓 LDA model training pipeline
│   ├── 📁 processors/               # Data processing
│   │   └── text_processor.py       # 📝 Text preprocessing & cleaning
│   ├── 📁 exporters/                # Dashboard exports
│   │   └── powerbi_exporter.py     # 📊 Power BI data preparation
│   ├── config_manager.py           # ⚙️ Configuration management
│   └── utils.py                    # 🔧 Utility functions
├── 📁 backend/                      # Backend orchestration
│   └── realtime_service.py         # 🚀 Main backend service
├── 📁 data/                         # Data storage
│   ├── 📁 hourly/                  # 📄 Hourly collected news CSV files
│   ├── 📁 processed/               # 🔄 Classified articles
│   └── 📁 dashboard/               # 📊 Power BI ready exports
├── 📁 models/                       # 🧠 Trained LDA models (.pkl files)
├── 📁 config/                       # Configuration files
│   ├── pipeline.yaml              # ⚙️ Main configuration
│   └── environment.example        # 🔐 Environment template
├── 📁 docs/                         # Documentation
│   ├── 📁 setup/                   # Installation guides
│   │   ├── INSTALLATION.md         # 📦 Setup instructions
│   │   ├── CONFIGURATION.md        # ⚙️ Configuration guide
│   │   ├── DEPLOYMENT.md           # 🚀 Production deployment
│   │   └── SCHEDULING.md           # ⏰ Automation setup
│   └── 📁 dashboard/               # Power BI guides
│       ├── POWERBI_SETUP.md        # 📊 Dashboard creation
│       ├── REALTIME_DASHBOARD.md   # ⚡ Real-time setup
│       └── dashboard_template.json # 📋 Dashboard template
├── 📁 scripts/                      # Utility scripts
│   ├── setup.py                   # 🛠️ Installation script
│   ├── run_tests.py               # 🧪 Test runner
│   └── pytest.ini                 # 🧪 Test configuration
├── 📁 tests/                        # Test suite
│   ├── test_config.py             # ⚙️ Configuration tests
│   ├── test_preprocessing.py      # 📝 Text processing tests
│   ├── test_topic_modeling.py     # 🧠 ML model tests
│   └── test_integration.py        # 🔄 End-to-end tests
├── 📁 logs/                         # Application logs
├── 📄 start_service.py             # 🚀 Main service launcher
├── 📄 requirements.txt             # 📦 Python dependencies
└── 📄 README.md                    # 📖 Project overview
```

## 🔄 **Aligned Workflow**

### **1. Hourly Data Collection**
```
📰 NewsAPI/RSS → 📄 hourly_news_YYYYMMDD_HH.csv → data/hourly/
```
- **File**: `core/collectors/hourly_news.py`
- **Frequency**: Every hour at :05 minutes
- **Output**: CSV files in `data/hourly/`

### **2. LDA Classification**
```
📄 CSV → 🧠 trained_model.pkl → 🏷️ Topic Labels → data/processed/
```
- **File**: `core/models/lda_classifier.py`
- **Input**: Latest CSV from hourly collection
- **Model**: Trained `.pkl` files in `models/`
- **Output**: Classified articles with topic assignments

### **3. Power BI Export**
```
🏷️ Classified Data → 📊 Dashboard CSV → data/dashboard/
```
- **File**: `core/exporters/powerbi_exporter.py`
- **Output**: Power BI ready files for real-time dashboard

### **4. Backend Orchestration**
```
⏰ Schedule → 🔄 Collect → 🧠 Classify → 📊 Export → 🔁 Repeat
```
- **File**: `backend/realtime_service.py`
- **Function**: Coordinates entire workflow automatically

## 🚀 **Quick Start Commands**

### **1. Setup**
```bash
# Install dependencies
pip install -r requirements.txt
python scripts/setup.py

# Configure API key
cp config/environment.example config/.env
# Edit config/.env with your NewsAPI key
```

### **2. Train Initial Model**
```bash
# Train LDA model (one-time setup)
python start_service.py train
```

### **3. Start Real-Time Service**
```bash
# Start hourly collection and classification
python start_service.py start

# Check status
python start_service.py status
```

## 📊 **Data Flow**

```
⏰ Every Hour:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   NewsAPI   │───▶│ CSV Export  │───▶│ LDA Model   │───▶│ Power BI    │
│   RSS Feeds │    │ (hourly/)   │    │ (.pkl)      │    │ Dashboard   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       ▼                   ▼                   ▼                   ▼
  Collect News      Save to CSV        Classify Topics    Update Trends
```

## 🎯 **Key Improvements**

### **✅ Removed Redundant Files:**
- ❌ `src/` directory (reorganized into `core/`)
- ❌ `run_pipeline.py` (replaced by `start_service.py`)
- ❌ Old documentation structure
- ❌ Batch processing components not needed for real-time

### **✅ Added Aligned Components:**
- ✅ `core/collectors/hourly_news.py` - Hourly collection service
- ✅ `core/models/lda_classifier.py` - Real-time .pkl model usage
- ✅ `backend/realtime_service.py` - Backend orchestration
- ✅ `start_service.py` - Simple service launcher
- ✅ Proper directory separation by function

### **✅ Workflow Alignment:**
- ✅ **Hourly collection** → CSV files
- ✅ **CSV processing** → LDA classification
- ✅ **Trained models** in .pkl format
- ✅ **Backend management** of entire flow
- ✅ **Real-time dashboard** updates

## 🔧 **Configuration**

### **Main Config**: `config/pipeline.yaml`
```yaml
news_api:
  api_key: "from_environment"
  query_keywords: ["technology", "politics", "economy"]
  
hourly_collection:
  enabled: true
  schedule: "every hour at :05"
  
topic_modeling:
  model_file: "models/latest_model.pkl"
  num_topics: 8
```

### **Environment**: `config/.env`
```bash
NEWS_API_KEY=your_actual_api_key_here
```

## 📈 **Monitoring**

### **Service Status**
```bash
python start_service.py status
```

### **Log Files**
```bash
tail -f logs/realtime_service.log
```

### **Data Files**
```bash
# Latest hourly collection
ls -la data/hourly/

# Processed classifications
ls -la data/processed/

# Dashboard exports
ls -la data/dashboard/
```

## 🎉 **Perfect Alignment Achieved!**

The project structure now **exactly matches** your vision:
- ⏰ **Hourly automated collection**
- 📄 **CSV-based data pipeline**
- 🧠 **Trained LDA models (.pkl)**
- 🔄 **Backend-managed workflow**
- 📊 **Real-time Power BI updates**

**Ready to start collecting and analyzing news trends in real-time!** 🚀
