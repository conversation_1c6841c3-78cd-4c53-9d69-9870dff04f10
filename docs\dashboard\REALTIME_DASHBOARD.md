# Real-Time Power BI Dashboard Setup

This guide shows how to create a real-time Power BI dashboard that updates hourly with new topic classifications.

## 📊 **Real-Time Data Flow**

```
Hourly News Collection → CSV Feed → LDA Classification → Power BI Auto-Refresh
        ↓                   ↓              ↓                    ↓
   NewsAPI/RSS         latest_hourly.csv  .pkl Model      Dashboard Updates
```

## 🔄 **Data Sources for Real-Time Dashboard**

### 1. **Hourly Topic Summary** (`data/hourly_topic_summary.csv`)
Real-time topic trends updated every hour:

| Column | Description |
|--------|-------------|
| hour | Hour timestamp (YYYY-MM-DD HH:00:00) |
| topic_id | Topic identifier |
| topic_label | Human-readable topic name |
| topic_keywords | Top 5 keywords |
| article_count | Articles in this hour |
| avg_confidence | Average classification confidence |

### 2. **Real-Time Articles** (`data/powerbi_realtime.csv`)
Latest classified articles:

| Column | Description |
|--------|-------------|
| title | Article headline |
| source_name | News source |
| publishedAt | Publication time |
| topic_id | Assigned topic |
| topic_label | Topic name |
| topic_confidence | Classification confidence |
| hour | Hour of classification |
| date | Date of classification |

### 3. **Backend Status** (`data/backend_status.json`)
Service health monitoring:

```json
{
  "backend_running": true,
  "last_collection": "2024-01-15T14:05:00",
  "articles_collected": 25,
  "status": "active"
}
```

## 🚀 **Setting Up Real-Time Dashboard**

### Step 1: Start the Backend Service

```bash
# First, ensure you have a trained model
python run_pipeline.py --step modeling

# Start the real-time backend
python backend_service.py start

# Check status
python backend_service.py status
```

### Step 2: Configure Power BI Data Sources

1. **Open Power BI Desktop**
2. **Get Data** → **Text/CSV**
3. **Add these data sources:**
   - `data/hourly_topic_summary.csv` (main real-time data)
   - `data/powerbi_realtime.csv` (article details)
   - `data/backend_status.json` (service status)

### Step 3: Set Up Auto-Refresh

#### Option A: File-Based Auto-Refresh (Recommended for Local)

1. **Data Source Settings**:
   - Right-click data source → **Refresh**
   - Set refresh interval to **1 hour**

2. **Automatic Refresh**:
   ```
   File → Options → Data Load → 
   ☑ Refresh data when opening file
   ☑ Enable background refresh
   ```

#### Option B: Power BI Service (Cloud)

1. **Publish to Power BI Service**
2. **Configure Scheduled Refresh**:
   - Dataset Settings → Scheduled Refresh
   - Set to refresh **every hour**
   - Configure data source credentials

## 📈 **Real-Time Dashboard Visualizations**

### 1. **Hourly Topic Trends** (Line Chart)
- **X-axis**: `hour` (from hourly_topic_summary)
- **Y-axis**: `article_count`
- **Legend**: `topic_label`
- **Auto-refresh**: Every hour

### 2. **Current Hour Topic Distribution** (Pie Chart)
- **Values**: `article_count` (filtered to current hour)
- **Legend**: `topic_label`
- **Filter**: `hour = MAX(hour)`

### 3. **Real-Time Article Feed** (Table)
- **Columns**: `title`, `source_name`, `topic_label`, `publishedAt`
- **Sort**: `publishedAt` (descending)
- **Filter**: Last 24 hours

### 4. **Topic Confidence Gauge** (Gauge)
- **Value**: `AVERAGE(avg_confidence)`
- **Min**: 0, **Max**: 1
- **Target**: 0.8

### 5. **Service Status Indicator** (Card)
- **Value**: Backend status from JSON
- **Conditional formatting**: Green if active, Red if stopped

### 6. **Articles per Hour** (Column Chart)
- **X-axis**: `hour` (last 24 hours)
- **Y-axis**: `SUM(article_count)`
- **Shows**: Hourly collection volume

## 🔧 **Advanced Real-Time Features**

### 1. **Live Data Streaming** (Power BI Service)

If using Power BI Service, you can set up live streaming:

```python
# Add to lda_service.py for live streaming
import requests

def push_to_powerbi_stream(data):
    """Push data to Power BI streaming dataset."""
    stream_url = "https://api.powerbi.com/beta/your-workspace/datasets/your-dataset/rows"
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_ACCESS_TOKEN'
    }
    
    response = requests.post(stream_url, json=data, headers=headers)
    return response.status_code == 200
```

### 2. **Real-Time Alerts**

Set up alerts in Power BI for:
- **High article volume**: When hourly count > threshold
- **Low confidence**: When average confidence < 0.6
- **Service down**: When backend status = "stopped"

### 3. **Mobile Dashboard**

Configure mobile-optimized views:
- **Phone layout**: Key metrics only
- **Tablet layout**: Condensed visualizations
- **Push notifications**: For alerts

## 📱 **Dashboard Layout for Real-Time Monitoring**

### **Main Page: Real-Time Overview**
```
┌─────────────────────────────────────────────────────────────┐
│                    🕐 REAL-TIME TOPIC TRENDS                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Current Hour  │  Topic Trends   │    Service Status       │
│   Distribution  │   (24 hours)    │                         │
│   (Pie Chart)   │  (Line Chart)   │  ● Backend: Active      │
│                 │                 │  ● Last Update: 14:05   │
│                 │                 │  ● Articles: 25/hour    │
├─────────────────┼─────────────────┴─────────────────────────┤
│  Confidence     │           Recent Articles Feed            │
│    Gauge        │  (Table with live updates)                │
│   📊 0.85       │                                           │
└─────────────────┴───────────────────────────────────────────┘
```

### **Drill-Down Page: Topic Details**
```
┌─────────────────────────────────────────────────────────────┐
│              📊 TOPIC: Technology & AI (Selected)           │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Hourly Trend  │   Keywords      │    Source Distribution  │
│   (Line Chart)  │   (Word Cloud)  │    (Bar Chart)          │
├─────────────────┼─────────────────┼─────────────────────────┤
│   Confidence    │   Article Count │    Recent Articles      │
│   Distribution  │   (Big Number)  │    (Filtered Table)     │
│   (Histogram)   │      📈 45      │                         │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## ⚙️ **Configuration for Your Setup**

### 1. **Adjust Collection Frequency**

Edit `src/hourly_collector.py`:
```python
# For more frequent updates (every 30 minutes)
schedule.every(30).minutes.do(self.run_hourly_collection)

# For less frequent updates (every 2 hours)
schedule.every(2).hours.do(self.run_hourly_collection)
```

### 2. **Customize Topic Keywords**

Edit `config.yaml`:
```yaml
news_api:
  query_keywords: 
    - "your custom keywords"
    - "specific topics"
    - "industry terms"
```

### 3. **Adjust Model Sensitivity**

Edit `config.yaml`:
```yaml
topic_modeling:
  num_topics: 8          # Increase for more granular topics
  min_df: 2             # Lower for more keywords
  max_df: 0.8           # Adjust for common words
```

## 🔍 **Monitoring and Troubleshooting**

### 1. **Check Backend Status**
```bash
# Check if services are running
python backend_service.py status

# View logs
tail -f logs/pipeline.log
```

### 2. **Verify Data Updates**
```bash
# Check latest data files
ls -la data/hourly_topic_summary.csv
ls -la data/powerbi_realtime.csv

# Check file timestamps
stat data/latest_classified.csv
```

### 3. **Power BI Refresh Issues**
- **File permissions**: Ensure Power BI can read CSV files
- **File locks**: Close CSV files in Excel/other programs
- **Path changes**: Verify file paths in data source settings

## 🚀 **Going Live**

### 1. **Production Deployment**
```bash
# Start backend as service
python backend_service.py start --daemon

# Or use systemd (Linux)
sudo systemctl start topic-modeling-backend
```

### 2. **Publish Dashboard**
- **Power BI Service**: Publish and configure auto-refresh
- **Embed in website**: Use Power BI embed codes
- **Share with team**: Set up workspace permissions

### 3. **Monitor Performance**
- **API usage**: Track NewsAPI limits
- **Processing time**: Monitor classification speed
- **Data quality**: Check confidence scores

## 📞 **Support**

For real-time dashboard issues:
1. **Check backend status**: `python backend_service.py status`
2. **Review logs**: `logs/pipeline.log`
3. **Verify model**: Ensure `.pkl` model exists in `models/`
4. **Test manually**: `python src/lda_service.py classify data/latest_hourly.csv`

Your real-time topic modeling dashboard is now ready to provide hourly insights into news trends! 🎉
