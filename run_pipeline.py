#!/usr/bin/env python3
"""
Main pipeline orchestration script for the topic modeling system.
Coordinates data ingestion, preprocessing, topic modeling, and export.
"""

import sys
import argparse
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from loguru import logger
from src.config import config
from src.utils import setup_logging, ensure_directories, format_duration, create_summary_stats
from src.data_ingestion import NewsDataIngestion
from src.preprocessing import TextPreprocessor
from src.topic_modeling import TopicModeler
from src.data_export import PowerBIExporter

class TopicModelingPipeline:
    """Main pipeline orchestrator for topic modeling workflow."""
    
    def __init__(self, skip_ingestion: bool = False, skip_preprocessing: bool = False, 
                 skip_modeling: bool = False, skip_export: bool = False):
        """
        Initialize the pipeline.
        
        Args:
            skip_ingestion: Skip data ingestion step
            skip_preprocessing: Skip preprocessing step
            skip_modeling: Skip topic modeling step
            skip_export: Skip data export step
        """
        self.skip_ingestion = skip_ingestion
        self.skip_preprocessing = skip_preprocessing
        self.skip_modeling = skip_modeling
        self.skip_export = skip_export
        
        # Initialize components
        self.data_ingestion = NewsDataIngestion()
        self.preprocessor = TextPreprocessor()
        self.topic_modeler = TopicModeler()
        self.exporter = PowerBIExporter()
        
        # Pipeline state
        self.pipeline_start_time = None
        self.step_times = {}
        self.results = {}
    
    def run_data_ingestion(self) -> bool:
        """
        Run data ingestion step.
        
        Returns:
            True if successful, False otherwise
        """
        if self.skip_ingestion:
            logger.info("Skipping data ingestion step")
            return True
        
        logger.info("=" * 50)
        logger.info("STEP 1: DATA INGESTION")
        logger.info("=" * 50)
        
        step_start = time.time()
        
        try:
            # Collect articles
            articles_df = self.data_ingestion.collect_articles()
            
            if articles_df.empty:
                logger.error("No articles collected")
                return False
            
            # Save articles
            output_path = self.data_ingestion.save_articles(articles_df)
            
            # Store results
            self.results['ingestion'] = {
                'articles_count': len(articles_df),
                'output_path': output_path,
                'summary_stats': create_summary_stats(articles_df)
            }
            
            logger.info(f"✅ Data ingestion completed: {len(articles_df)} articles collected")
            return True
            
        except Exception as e:
            logger.error(f"❌ Data ingestion failed: {e}")
            return False
        
        finally:
            self.step_times['ingestion'] = time.time() - step_start
    
    def run_preprocessing(self) -> bool:
        """
        Run text preprocessing step.
        
        Returns:
            True if successful, False otherwise
        """
        if self.skip_preprocessing:
            logger.info("Skipping preprocessing step")
            return True
        
        logger.info("=" * 50)
        logger.info("STEP 2: TEXT PREPROCESSING")
        logger.info("=" * 50)
        
        step_start = time.time()
        
        try:
            # Load raw articles
            data_dir = Path(config.get('output.data_dir', 'data'))
            raw_file = config.get('output.raw_articles_file', 'raw_articles.csv')
            input_path = data_dir / raw_file
            
            if not input_path.exists():
                logger.error(f"Raw articles file not found: {input_path}")
                return False
            
            # Load and preprocess
            from src.utils import load_articles
            df = load_articles(str(input_path))
            
            if df.empty:
                logger.error("No articles to preprocess")
                return False
            
            processed_df = self.preprocessor.preprocess_dataframe(df)
            
            if processed_df.empty:
                logger.error("Preprocessing resulted in empty dataset")
                return False
            
            # Save processed articles
            processed_file = config.get('output.processed_articles_file', 'processed_articles.csv')
            output_path = data_dir / processed_file
            
            from src.utils import save_dataframe
            save_dataframe(processed_df, str(output_path))
            
            # Store results
            self.results['preprocessing'] = {
                'input_articles': len(df),
                'output_articles': len(processed_df),
                'output_path': str(output_path),
                'avg_tokens': processed_df['token_count'].mean() if 'token_count' in processed_df.columns else 0
            }
            
            logger.info(f"✅ Preprocessing completed: {len(processed_df)} articles processed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Preprocessing failed: {e}")
            return False
        
        finally:
            self.step_times['preprocessing'] = time.time() - step_start
    
    def run_topic_modeling(self) -> bool:
        """
        Run topic modeling step.
        
        Returns:
            True if successful, False otherwise
        """
        if self.skip_modeling:
            logger.info("Skipping topic modeling step")
            return True
        
        logger.info("=" * 50)
        logger.info("STEP 3: TOPIC MODELING")
        logger.info("=" * 50)
        
        step_start = time.time()
        
        try:
            # Load processed articles
            data_dir = Path(config.get('output.data_dir', 'data'))
            processed_file = config.get('output.processed_articles_file', 'processed_articles.csv')
            input_path = data_dir / processed_file
            
            if not input_path.exists():
                logger.error(f"Processed articles file not found: {input_path}")
                return False
            
            from src.utils import load_articles
            df = load_articles(str(input_path))
            
            if df.empty or 'cleaned_text' not in df.columns:
                logger.error("No preprocessed articles found")
                return False
            
            # Filter articles with sufficient content
            df = df[df['cleaned_text'].str.len() > 50]
            
            if len(df) < 10:
                logger.error("Insufficient articles for topic modeling")
                return False
            
            # Train topic model
            documents = df['cleaned_text'].tolist()
            self.topic_modeler.fit(documents)
            
            # Predict topics
            predictions = self.topic_modeler.predict_topics(documents)
            
            # Add predictions to dataframe
            for i, pred in enumerate(predictions):
                df.loc[df.index[i], 'topic_id'] = pred['topic_id']
                df.loc[df.index[i], 'topic_label'] = pred['topic_label']
                df.loc[df.index[i], 'topic_keywords'] = ', '.join(pred['topic_keywords'])
                df.loc[df.index[i], 'topic_confidence'] = pred['confidence']
            
            # Save results
            topic_file = config.get('output.topic_trends_file', 'topic_trends.csv')
            output_path = data_dir / topic_file
            
            from src.utils import save_dataframe
            save_dataframe(df, str(output_path))
            
            # Save model in .pkl format for real-time service
            model_path = self.topic_modeler.save_model()

            # Save topic summary
            topic_summary = self.topic_modeler.get_topic_summary()
            summary_path = data_dir / 'topic_summary.csv'
            save_dataframe(topic_summary, str(summary_path), add_timestamp=False)

            # Create a symlink to latest model for real-time service
            latest_model_path = data_dir.parent / 'models' / 'latest_model.pkl'
            if latest_model_path.exists():
                latest_model_path.unlink()
            latest_model_path.symlink_to(Path(model_path).resolve())
            
            # Store results
            self.results['topic_modeling'] = {
                'input_articles': len(df),
                'num_topics': len(self.topic_modeler.topics),
                'output_path': str(output_path),
                'model_path': model_path,
                'summary_path': str(summary_path),
                'topics': self.topic_modeler.topics
            }
            
            logger.info(f"✅ Topic modeling completed: {len(self.topic_modeler.topics)} topics identified")
            return True
            
        except Exception as e:
            logger.error(f"❌ Topic modeling failed: {e}")
            return False
        
        finally:
            self.step_times['topic_modeling'] = time.time() - step_start
    
    def run_data_export(self) -> bool:
        """
        Run data export step.
        
        Returns:
            True if successful, False otherwise
        """
        if self.skip_export:
            logger.info("Skipping data export step")
            return True
        
        logger.info("=" * 50)
        logger.info("STEP 4: DATA EXPORT")
        logger.info("=" * 50)
        
        step_start = time.time()
        
        try:
            # Export for Power BI
            exported_files = self.exporter.export_for_powerbi()
            
            if not exported_files:
                logger.error("No files exported")
                return False
            
            # Store results
            self.results['export'] = {
                'exported_files': exported_files,
                'file_count': len(exported_files)
            }
            
            logger.info(f"✅ Data export completed: {len(exported_files)} files exported")
            return True
            
        except Exception as e:
            logger.error(f"❌ Data export failed: {e}")
            return False
        
        finally:
            self.step_times['export'] = time.time() - step_start
    
    def run_full_pipeline(self) -> bool:
        """
        Run the complete pipeline.
        
        Returns:
            True if successful, False otherwise
        """
        logger.info("🚀 Starting Topic Modeling Pipeline")
        logger.info(f"Pipeline configuration: {config.get('news_api.query_keywords', [])}")
        
        self.pipeline_start_time = time.time()
        
        # Ensure directories exist
        ensure_directories()
        
        # Run pipeline steps
        steps = [
            ("Data Ingestion", self.run_data_ingestion),
            ("Text Preprocessing", self.run_preprocessing),
            ("Topic Modeling", self.run_topic_modeling),
            ("Data Export", self.run_data_export)
        ]
        
        for step_name, step_function in steps:
            success = step_function()
            if not success:
                logger.error(f"Pipeline failed at step: {step_name}")
                return False
        
        # Pipeline completed successfully
        total_time = time.time() - self.pipeline_start_time
        self._log_pipeline_summary(total_time)
        
        return True
    
    def _log_pipeline_summary(self, total_time: float) -> None:
        """Log pipeline execution summary."""
        logger.info("=" * 50)
        logger.info("🎉 PIPELINE COMPLETED SUCCESSFULLY")
        logger.info("=" * 50)
        
        logger.info(f"Total execution time: {format_duration(total_time)}")
        
        # Step timing breakdown
        logger.info("\nStep timing breakdown:")
        for step, duration in self.step_times.items():
            percentage = (duration / total_time) * 100
            logger.info(f"  {step}: {format_duration(duration)} ({percentage:.1f}%)")
        
        # Results summary
        logger.info("\nResults summary:")
        if 'ingestion' in self.results:
            logger.info(f"  Articles collected: {self.results['ingestion']['articles_count']}")
        
        if 'preprocessing' in self.results:
            logger.info(f"  Articles processed: {self.results['preprocessing']['output_articles']}")
        
        if 'topic_modeling' in self.results:
            logger.info(f"  Topics identified: {self.results['topic_modeling']['num_topics']}")
        
        if 'export' in self.results:
            logger.info(f"  Files exported: {self.results['export']['file_count']}")
        
        # Next steps
        logger.info("\n📊 Next steps:")
        logger.info("1. Open Power BI Desktop")
        logger.info("2. Import data from: data/powerbi_data.csv")
        logger.info("3. Create visualizations using the exported data")
        logger.info("4. Set up automatic refresh if needed")

def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Topic Modeling Pipeline")
    parser.add_argument('--skip-ingestion', action='store_true', 
                       help='Skip data ingestion step')
    parser.add_argument('--skip-preprocessing', action='store_true',
                       help='Skip text preprocessing step')
    parser.add_argument('--skip-modeling', action='store_true',
                       help='Skip topic modeling step')
    parser.add_argument('--skip-export', action='store_true',
                       help='Skip data export step')
    parser.add_argument('--step', choices=['ingestion', 'preprocessing', 'modeling', 'export'],
                       help='Run only a specific step')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Create pipeline
    if args.step:
        # Run specific step
        skip_ingestion = args.step != 'ingestion'
        skip_preprocessing = args.step != 'preprocessing'
        skip_modeling = args.step != 'modeling'
        skip_export = args.step != 'export'
    else:
        # Use command line flags
        skip_ingestion = args.skip_ingestion
        skip_preprocessing = args.skip_preprocessing
        skip_modeling = args.skip_modeling
        skip_export = args.skip_export
    
    pipeline = TopicModelingPipeline(
        skip_ingestion=skip_ingestion,
        skip_preprocessing=skip_preprocessing,
        skip_modeling=skip_modeling,
        skip_export=skip_export
    )
    
    # Run pipeline
    try:
        success = pipeline.run_full_pipeline()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Pipeline failed with unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
