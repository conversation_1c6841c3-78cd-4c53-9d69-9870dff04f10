#!/usr/bin/env python3
"""
Real-Time Topic Modeling Pipeline with SQL Server Integration
Complete pipeline orchestrator with Power BI DirectQuery support
Replaces CSV exports with real-time SQL Server database operations
"""

import os
import sys
import time
import schedule
import argparse
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import logging
from pathlib import Path
import json

# Add scripts directory to path
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent / "database"))

# Import pipeline components
from fetch_news import NewsDataIngestion
from preprocess import AdvancedTextPreprocessor
from topic_model import LDATopicModelingService

# Import SQL Server components (replaces CSV export)
from sql_connection import SQLConnectionManager, create_connection_from_config
from sql_exporter import SQLExporter

# Load environment variables
from dotenv import load_dotenv
load_dotenv('config/.env')

# Setup logging
def setup_logging(log_level: str = "INFO", log_file: str = "logs/pipeline.log"):
    """Setup comprehensive logging."""
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

logger = setup_logging()

class TopicModelingPipeline:
    """
    Real-time topic modeling pipeline with SQL Server integration.
    Provides seamless Power BI DirectQuery support.
    """

    def __init__(self,
                 use_database: bool = True,  # Default to SQL Server
                 confidence_threshold: float = 0.3):
        """
        Initialize the complete pipeline with SQL Server integration.

        Args:
            use_database: Whether to use SQL Server (True) or CSV files (False)
            confidence_threshold: Minimum confidence for topic classification
        """
        self.use_database = use_database
        self.confidence_threshold = confidence_threshold

        # Get API key
        self.api_key = os.getenv('NEWS_API_KEY')
        if not self.api_key:
            logger.error("❌ NEWS_API_KEY not found in environment variables")
            raise ValueError("NEWS_API_KEY is required")

        # Initialize pipeline components
        logger.info("🚀 Initializing pipeline components...")

        self.news_ingestion = NewsDataIngestion(
            api_key=self.api_key,
            use_database=False  # Always use CSV for ingestion, then export to SQL
        )

        self.topic_service = LDATopicModelingService(
            use_database=False  # Always use CSV for processing, then export to SQL
        )

        # Initialize SQL Server components if using database
        if self.use_database:
            try:
                self.sql_connection = create_connection_from_config()
                self.sql_exporter = SQLExporter(self.sql_connection)
                logger.info("✅ SQL Server connection established")
            except Exception as e:
                logger.error(f"❌ Failed to connect to SQL Server: {e}")
                logger.info("🔄 Falling back to CSV export mode")
                self.use_database = False
                self.sql_connection = None
                self.sql_exporter = None
        else:
            # Fallback to CSV export (legacy mode)
            from export_to_csv import PowerBIExportSystem
            self.powerbi_exporter = PowerBIExportSystem(use_database=False)
            self.sql_connection = None
            self.sql_exporter = None
        
        # Pipeline statistics
        self.stats = {
            'runs': 0,
            'total_articles_collected': 0,
            'total_articles_processed': 0,
            'last_run': None,
            'last_success': None,
            'errors': []
        }
        
        logger.info("✅ Pipeline initialized successfully")
    
    def run_data_ingestion(self, 
                          query: str = None,
                          hours_back: int = 1,
                          max_pages: int = 3) -> Dict:
        """
        Run data ingestion step.
        
        Args:
            query: Search query for news articles
            hours_back: Hours to look back for articles
            max_pages: Maximum pages to fetch from API
            
        Returns:
            Dictionary with ingestion results
        """
        
        logger.info("📰 Step 1: Data Ingestion")
        
        try:
            # Default query
            if not query:
                query = "technology OR artificial intelligence OR politics OR business OR economy"
            
            # Fetch articles
            articles = self.news_ingestion.fetch_articles(
                query=query,
                from_date=datetime.now() - timedelta(hours=hours_back),
                to_date=datetime.now(),
                max_pages=max_pages
            )
            
            if not articles:
                logger.warning("⚠️ No articles fetched")
                return {'success': False, 'articles_fetched': 0, 'articles_saved': 0}
            
            # Save articles
            saved_count = self.news_ingestion.save_articles(articles)
            
            result = {
                'success': True,
                'articles_fetched': len(articles),
                'articles_saved': saved_count,
                'total_in_storage': self.news_ingestion.get_article_count()
            }
            
            logger.info(f"✅ Ingestion complete: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Data ingestion failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_topic_modeling(self) -> Dict:
        """
        Run topic modeling step.
        
        Returns:
            Dictionary with modeling results
        """
        
        logger.info("🧠 Step 2: Topic Modeling")
        
        try:
            if not self.topic_service.is_loaded:
                logger.error("❌ LDA model not loaded")
                return {'success': False, 'error': 'LDA model not loaded'}
            
            # Process new articles
            result = self.topic_service.process_new_articles(
                confidence_threshold=self.confidence_threshold
            )
            
            logger.info(f"✅ Topic modeling complete: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Topic modeling failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_powerbi_export(self) -> Dict:
        """
        Run Power BI export step.
        
        Returns:
            Dictionary with export results
        """
        
        logger.info("📊 Step 3: Power BI Export")
        
        try:
            # Export all datasets
            export_status = self.powerbi_exporter.export_all_datasets()
            
            successful_exports = sum(export_status.values())
            total_exports = len(export_status)
            
            result = {
                'success': successful_exports > 0,
                'successful_exports': successful_exports,
                'total_exports': total_exports,
                'export_status': export_status
            }
            
            logger.info(f"✅ Power BI export complete: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Power BI export failed: {e}")
            return {'success': False, 'error': str(e)}

    def run_sql_export(self) -> Dict:
        """
        Run SQL Server export step.

        Returns:
            Dictionary with export results
        """

        logger.info("🗄️ Step 3: SQL Server Export")

        if not self.sql_exporter:
            logger.error("❌ SQL exporter not initialized")
            return {'success': False, 'error': 'SQL exporter not available'}

        try:
            # Generate unique run ID for tracking
            run_id = str(uuid.uuid4())
            start_time = datetime.now()

            # Log pipeline run start
            self.sql_exporter.log_pipeline_run(
                run_id=run_id,
                status='RUNNING',
                start_time=start_time
            )

            export_results = {}
            total_exported = 0

            # Step 1: Export articles
            try:
                articles_df = self._load_articles_for_export()
                if not articles_df.empty:
                    articles_exported = self.sql_exporter.export_articles(articles_df)
                    export_results['articles'] = articles_exported
                    total_exported += articles_exported
                    logger.info(f"✅ Exported {articles_exported} articles")
                else:
                    export_results['articles'] = 0
                    logger.info("ℹ️ No new articles to export")
            except Exception as e:
                logger.error(f"❌ Article export failed: {e}")
                export_results['articles'] = 0

            # Step 2: Export article topics
            try:
                topics_df = self._load_article_topics_for_export()
                if not topics_df.empty:
                    topics_exported = self.sql_exporter.export_article_topics(topics_df)
                    export_results['article_topics'] = topics_exported
                    total_exported += topics_exported
                    logger.info(f"✅ Exported {topics_exported} topic classifications")
                else:
                    export_results['article_topics'] = 0
                    logger.info("ℹ️ No new topic classifications to export")
            except Exception as e:
                logger.error(f"❌ Topic classifications export failed: {e}")
                export_results['article_topics'] = 0

            # Step 3: Update topics master table
            try:
                topics_data = self._get_topics_data()
                if topics_data:
                    topics_updated = self.sql_exporter.update_topics(topics_data)
                    export_results['topics_updated'] = topics_updated
                    logger.info(f"✅ Updated {topics_updated} topics")
                else:
                    export_results['topics_updated'] = 0
            except Exception as e:
                logger.error(f"❌ Topics update failed: {e}")
                export_results['topics_updated'] = 0

            # Step 4: Generate aggregated data for Power BI
            try:
                aggregation_results = self.sql_exporter.generate_aggregated_data()
                export_results['aggregations'] = aggregation_results
                total_exported += sum(aggregation_results.values())
                logger.info(f"✅ Generated aggregated data: {aggregation_results}")
            except Exception as e:
                logger.error(f"❌ Aggregation generation failed: {e}")
                export_results['aggregations'] = {}

            # Determine success
            success = total_exported > 0
            end_time = datetime.now()

            # Log pipeline run completion
            self.sql_exporter.log_pipeline_run(
                run_id=run_id,
                status='SUCCESS' if success else 'FAILED',
                start_time=start_time,
                end_time=end_time,
                articles_collected=export_results.get('articles', 0),
                articles_processed=export_results.get('article_topics', 0),
                articles_classified=export_results.get('article_topics', 0),
                error_message=None if success else 'No data exported'
            )

            result = {
                'success': success,
                'total_exported': total_exported,
                'export_results': export_results,
                'run_id': run_id,
                'duration_seconds': (end_time - start_time).total_seconds()
            }

            logger.info(f"✅ SQL Server export complete: {result}")
            return result

        except Exception as e:
            # Log pipeline run failure
            try:
                self.sql_exporter.log_pipeline_run(
                    run_id=run_id,
                    status='FAILED',
                    start_time=start_time,
                    end_time=datetime.now(),
                    error_message=str(e)
                )
            except:
                pass  # Don't fail if logging fails

            logger.error(f"❌ SQL Server export failed: {e}")
            return {'success': False, 'error': str(e)}

    def _load_articles_for_export(self) -> pd.DataFrame:
        """Load articles from CSV for SQL export."""

        try:
            articles_file = Path("data/raw_articles.csv")
            if articles_file.exists():
                df = pd.read_csv(articles_file)

                # Add required columns if missing
                if 'article_id' not in df.columns:
                    df['article_id'] = df.index.astype(str)

                if 'content_hash' not in df.columns:
                    import hashlib
                    df['content_hash'] = df['title'].apply(
                        lambda x: hashlib.md5(str(x).encode()).hexdigest()
                    )

                if 'collected_at' not in df.columns:
                    df['collected_at'] = datetime.now()

                # Rename columns to match SQL schema
                column_mapping = {
                    'source': 'source_name',
                    'publishedAt': 'published_at',
                    'urlToImage': 'url_to_image'
                }

                df = df.rename(columns=column_mapping)

                return df
            else:
                logger.warning("⚠️ No articles file found for export")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"❌ Failed to load articles for export: {e}")
            return pd.DataFrame()

    def _load_article_topics_for_export(self) -> pd.DataFrame:
        """Load article topics from CSV for SQL export."""

        try:
            topics_file = Path("data/processed_articles.csv")
            if topics_file.exists():
                df = pd.read_csv(topics_file)

                # Filter only classified articles
                df = df[df['topic_id'] != -1]

                # Add required columns if missing
                if 'classified_at' not in df.columns:
                    df['classified_at'] = datetime.now()

                if 'model_version' not in df.columns:
                    df['model_version'] = '1.0'

                # Rename confidence column if needed
                if 'topic_confidence' in df.columns:
                    df['confidence'] = df['topic_confidence']

                return df[['article_id', 'topic_id', 'confidence', 'classified_at', 'model_version']]
            else:
                logger.warning("⚠️ No processed articles file found for export")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"❌ Failed to load article topics for export: {e}")
            return pd.DataFrame()

    def _get_topics_data(self) -> Dict[int, Dict]:
        """Get topics data for master table update."""

        try:
            # Try to get topics from the LDA service
            if hasattr(self.topic_service, 'get_topic_info'):
                return self.topic_service.get_topic_info()

            # Fallback: extract from processed articles
            topics_file = Path("data/processed_articles.csv")
            if topics_file.exists():
                df = pd.read_csv(topics_file)

                topics_data = {}
                for topic_id in df['topic_id'].unique():
                    if topic_id != -1:
                        topics_data[topic_id] = {
                            'label': f'Topic {topic_id}',
                            'keywords': []
                        }

                return topics_data

            return {}

        except Exception as e:
            logger.error(f"❌ Failed to get topics data: {e}")
            return {}

    def run_complete_pipeline(self,
                             query: str = None,
                             hours_back: int = 1,
                             max_pages: int = 3) -> Dict:
        """
        Run the complete pipeline.
        
        Args:
            query: Search query for news articles
            hours_back: Hours to look back for articles
            max_pages: Maximum pages to fetch from API
            
        Returns:
            Dictionary with complete pipeline results
        """
        
        start_time = datetime.now()
        logger.info(f"🚀 Starting complete pipeline at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Update statistics
        self.stats['runs'] += 1
        self.stats['last_run'] = start_time.isoformat()
        
        pipeline_result = {
            'start_time': start_time.isoformat(),
            'steps': {},
            'overall_success': False,
            'errors': []
        }
        
        try:
            # Step 1: Data Ingestion
            ingestion_result = self.run_data_ingestion(query, hours_back, max_pages)
            pipeline_result['steps']['ingestion'] = ingestion_result
            
            if ingestion_result['success']:
                self.stats['total_articles_collected'] += ingestion_result.get('articles_saved', 0)
            else:
                pipeline_result['errors'].append('Data ingestion failed')
            
            # Step 2: Topic Modeling
            modeling_result = self.run_topic_modeling()
            pipeline_result['steps']['modeling'] = modeling_result
            
            if modeling_result['success']:
                self.stats['total_articles_processed'] += modeling_result.get('processed_count', 0)
            else:
                pipeline_result['errors'].append('Topic modeling failed')
            
            # Step 3: Data Export (SQL Server or CSV)
            if self.use_database:
                export_result = self.run_sql_export()
                pipeline_result['steps']['sql_export'] = export_result

                if not export_result['success']:
                    pipeline_result['errors'].append('SQL Server export failed')
            else:
                export_result = self.run_powerbi_export()
                pipeline_result['steps']['csv_export'] = export_result

                if not export_result['success']:
                    pipeline_result['errors'].append('CSV export failed')
            
            # Determine overall success
            pipeline_result['overall_success'] = (
                ingestion_result['success'] and 
                modeling_result['success'] and 
                export_result['success']
            )
            
            if pipeline_result['overall_success']:
                self.stats['last_success'] = start_time.isoformat()
            
        except Exception as e:
            logger.error(f"❌ Pipeline failed: {e}")
            pipeline_result['errors'].append(str(e))
            self.stats['errors'].append({
                'timestamp': start_time.isoformat(),
                'error': str(e)
            })
        
        # Calculate duration
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        pipeline_result['end_time'] = end_time.isoformat()
        pipeline_result['duration_seconds'] = duration
        
        # Log summary
        if pipeline_result['overall_success']:
            logger.info(f"🎯 Pipeline completed successfully in {duration:.1f} seconds")
        else:
            logger.error(f"❌ Pipeline completed with errors in {duration:.1f} seconds")
            for error in pipeline_result['errors']:
                logger.error(f"  - {error}")
        
        return pipeline_result
    
    def run_specific_step(self, step: str, **kwargs) -> Dict:
        """
        Run a specific pipeline step.
        
        Args:
            step: Step name ('ingestion', 'modeling', 'export')
            **kwargs: Additional arguments for the step
            
        Returns:
            Dictionary with step results
        """
        
        if step == 'ingestion':
            return self.run_data_ingestion(**kwargs)
        elif step == 'modeling':
            return self.run_topic_modeling()
        elif step == 'export':
            return self.run_powerbi_export()
        else:
            logger.error(f"❌ Unknown step: {step}")
            return {'success': False, 'error': f'Unknown step: {step}'}
    
    def get_pipeline_status(self) -> Dict:
        """Get current pipeline status and statistics."""
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'statistics': self.stats.copy(),
            'components': {
                'news_ingestion': bool(self.api_key),
                'topic_modeling': self.topic_service.is_loaded,
                'powerbi_export': True
            },
            'storage': {
                'total_articles': self.news_ingestion.get_article_count(),
                'use_database': self.use_database
            }
        }
        
        # Get topic statistics
        try:
            topic_stats = self.topic_service.get_topic_statistics()
            status['topic_statistics'] = topic_stats
        except Exception as e:
            logger.error(f"❌ Error getting topic statistics: {e}")
            status['topic_statistics'] = []
        
        return status
    
    def save_status_report(self, output_file: str = "logs/pipeline_status.json"):
        """Save detailed status report to file."""
        
        status = self.get_pipeline_status()
        
        try:
            with open(output_file, 'w') as f:
                json.dump(status, f, indent=2)
            
            logger.info(f"📊 Status report saved: {output_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save status report: {e}")

def schedule_pipeline(pipeline: TopicModelingPipeline, 
                     frequency: str = "hourly",
                     time_spec: str = ":05"):
    """
    Schedule the pipeline to run automatically.
    
    Args:
        pipeline: Pipeline instance
        frequency: Schedule frequency ('hourly', 'daily', 'weekly')
        time_spec: Time specification (e.g., ":05" for 5 minutes past hour)
    """
    
    logger.info(f"⏰ Scheduling pipeline: {frequency} at {time_spec}")
    
    def run_scheduled_pipeline():
        """Wrapper function for scheduled runs."""
        try:
            result = pipeline.run_complete_pipeline()
            
            # Save status report after each run
            pipeline.save_status_report()
            
            if not result['overall_success']:
                logger.error("❌ Scheduled pipeline run failed")
            
        except Exception as e:
            logger.error(f"❌ Scheduled pipeline error: {e}")
    
    # Schedule based on frequency
    if frequency == "hourly":
        schedule.every().hour.at(time_spec).do(run_scheduled_pipeline)
    elif frequency == "daily":
        schedule.every().day.at(time_spec).do(run_scheduled_pipeline)
    elif frequency == "weekly":
        # For weekly, time_spec should be like "monday" or "tuesday:09:00"
        if ":" in time_spec:
            day, time = time_spec.split(":", 1)
            getattr(schedule.every(), day.lower()).at(time).do(run_scheduled_pipeline)
        else:
            getattr(schedule.every(), time_spec.lower()).do(run_scheduled_pipeline)
    else:
        logger.error(f"❌ Unknown frequency: {frequency}")
        return
    
    logger.info("🔄 Pipeline scheduled successfully")
    
    # Keep running
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    except KeyboardInterrupt:
        logger.info("⏹️ Stopping scheduled pipeline")

def main():
    """Main entry point with command line interface."""
    
    parser = argparse.ArgumentParser(description="Topic Modeling Pipeline")
    parser.add_argument('command', choices=['run', 'schedule', 'status', 'step'], 
                       help='Command to execute')
    parser.add_argument('--step', choices=['ingestion', 'modeling', 'export'],
                       help='Specific step to run (for step command)')
    parser.add_argument('--query', type=str, help='Search query for news articles')
    parser.add_argument('--hours-back', type=int, default=1, help='Hours to look back')
    parser.add_argument('--max-pages', type=int, default=3, help='Maximum pages to fetch')
    parser.add_argument('--frequency', choices=['hourly', 'daily', 'weekly'], 
                       default='hourly', help='Schedule frequency')
    parser.add_argument('--time', type=str, default=':05', help='Schedule time')
    parser.add_argument('--database', action='store_true', help='Use database instead of CSV')
    parser.add_argument('--confidence', type=float, default=0.3, help='Confidence threshold')
    
    args = parser.parse_args()
    
    try:
        # Initialize pipeline
        pipeline = TopicModelingPipeline(
            use_database=args.database,
            confidence_threshold=args.confidence
        )
        
        if args.command == 'run':
            # Run complete pipeline
            result = pipeline.run_complete_pipeline(
                query=args.query,
                hours_back=args.hours_back,
                max_pages=args.max_pages
            )
            
            # Print summary
            print(f"\n📊 Pipeline Results:")
            print(f"  Overall Success: {'✅' if result['overall_success'] else '❌'}")
            print(f"  Duration: {result['duration_seconds']:.1f} seconds")
            
            for step_name, step_result in result['steps'].items():
                success_icon = "✅" if step_result.get('success', False) else "❌"
                print(f"  {step_name.title()}: {success_icon}")
            
            if result['errors']:
                print(f"  Errors: {len(result['errors'])}")
                for error in result['errors']:
                    print(f"    - {error}")
        
        elif args.command == 'step':
            # Run specific step
            if not args.step:
                print("❌ --step argument required for step command")
                return 1
            
            result = pipeline.run_specific_step(
                args.step,
                query=args.query,
                hours_back=args.hours_back,
                max_pages=args.max_pages
            )
            
            print(f"\n📊 Step '{args.step}' Results:")
            print(f"  Success: {'✅' if result.get('success', False) else '❌'}")
            if 'error' in result:
                print(f"  Error: {result['error']}")
        
        elif args.command == 'schedule':
            # Run scheduled pipeline
            schedule_pipeline(pipeline, args.frequency, args.time)
        
        elif args.command == 'status':
            # Show pipeline status
            status = pipeline.get_pipeline_status()
            
            print(f"\n📊 Pipeline Status ({status['timestamp']}):")
            print(f"  Components:")
            for component, is_ready in status['components'].items():
                icon = "✅" if is_ready else "❌"
                print(f"    {icon} {component}")
            
            print(f"  Statistics:")
            stats = status['statistics']
            print(f"    Total runs: {stats['runs']}")
            print(f"    Articles collected: {stats['total_articles_collected']}")
            print(f"    Articles processed: {stats['total_articles_processed']}")
            print(f"    Last run: {stats['last_run'] or 'Never'}")
            print(f"    Last success: {stats['last_success'] or 'Never'}")
            
            print(f"  Storage:")
            print(f"    Total articles: {status['storage']['total_articles']}")
            print(f"    Using database: {status['storage']['use_database']}")
            
            # Save status report
            pipeline.save_status_report()
            print(f"  📄 Detailed report saved: logs/pipeline_status.json")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Pipeline failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
