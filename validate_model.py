#!/usr/bin/env python3
"""
LDA Model Validation Script
Validates that your .pkl file is properly formatted for the real-time system.
"""

import pickle
import sys
from pathlib import Path

def validate_model(model_path):
    """
    Validate LDA model file format.
    
    Args:
        model_path: Path to .pkl file
        
    Returns:
        bool: True if valid, False otherwise
    """
    
    print(f"🔍 Validating model: {model_path}")
    print("=" * 50)
    
    # Check if file exists
    if not Path(model_path).exists():
        print(f"❌ File not found: {model_path}")
        return False
    
    try:
        # Load the model
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        print("✅ Model file loaded successfully")
        
        # Check required keys
        required_keys = ['model', 'vectorizer', 'topics']
        missing_keys = []
        
        for key in required_keys:
            if key not in model_data:
                missing_keys.append(key)
            else:
                print(f"✅ Found required key: {key}")
        
        if missing_keys:
            print(f"❌ Missing required keys: {missing_keys}")
            return False
        
        # Validate topics structure
        topics = model_data['topics']
        if not isinstance(topics, dict):
            print("❌ Topics should be a dictionary")
            return False
        
        print(f"✅ Found {len(topics)} topics")
        
        # Check topic structure
        for topic_id, topic_info in topics.items():
            if not isinstance(topic_info, dict):
                print(f"❌ Topic {topic_id} should be a dictionary")
                return False
            
            if 'label' not in topic_info:
                print(f"❌ Topic {topic_id} missing 'label'")
                return False
            
            if 'keywords' not in topic_info:
                print(f"❌ Topic {topic_id} missing 'keywords'")
                return False
            
            if not isinstance(topic_info['keywords'], list):
                print(f"❌ Topic {topic_id} keywords should be a list")
                return False
        
        print("✅ Topic structure is valid")
        
        # Display topic information
        print("\n📊 Topic Summary:")
        print("-" * 30)
        for topic_id, topic_info in topics.items():
            label = topic_info['label']
            keywords = ', '.join(topic_info['keywords'][:5])
            print(f"Topic {topic_id}: {label}")
            print(f"  Keywords: {keywords}")
        
        # Check model type
        method = model_data.get('method', 'unknown')
        print(f"\n🧠 Model method: {method}")
        
        # Try to access model and vectorizer
        model = model_data['model']
        vectorizer = model_data['vectorizer']
        
        print(f"✅ Model type: {type(model).__name__}")
        print(f"✅ Vectorizer type: {type(vectorizer).__name__}")
        
        # Check if vectorizer has required methods
        if not hasattr(vectorizer, 'transform'):
            print("❌ Vectorizer missing 'transform' method")
            return False
        
        if not hasattr(vectorizer, 'get_feature_names_out'):
            print("⚠️  Vectorizer missing 'get_feature_names_out' method (may be older version)")
        
        print("\n✅ Model validation successful!")
        print("🚀 Your model is ready for real-time classification")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def main():
    """Main validation function."""
    
    # Default model paths to check
    model_paths = [
        "models/lda_model.pkl",
        "models/latest_model.pkl"
    ]
    
    # Check command line argument
    if len(sys.argv) > 1:
        model_paths = [sys.argv[1]]
    
    print("🧠 LDA Model Validation Tool")
    print("=" * 50)
    
    valid_model_found = False
    
    for model_path in model_paths:
        if Path(model_path).exists():
            if validate_model(model_path):
                valid_model_found = True
                break
            print()  # Add spacing between validations
        else:
            print(f"⚠️  Model not found: {model_path}")
    
    if not valid_model_found:
        print("\n❌ No valid model found!")
        print("\n💡 To fix this:")
        print("1. Place your trained LDA model in models/lda_model.pkl")
        print("2. Ensure the .pkl file contains:")
        print("   - 'model': Trained LDA model")
        print("   - 'vectorizer': Fitted CountVectorizer")
        print("   - 'topics': Dictionary with topic info")
        print("\n📖 See README.md for detailed requirements")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
