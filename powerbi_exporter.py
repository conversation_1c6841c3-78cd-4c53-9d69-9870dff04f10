#!/usr/bin/env python3
"""
Power BI Export Module
Step 4: [Classified Data] → [Power BI]
"""

import pandas as pd
import os
from datetime import datetime, timedelta
from typing import Dict, List
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PowerBIExporter:
    """Simple Power BI data exporter for classified articles."""
    
    def __init__(self, 
                 processed_articles_file: str = "data/processed_articles.csv",
                 export_dir: str = "data/powerbi"):
        """
        Initialize Power BI exporter.
        
        Args:
            processed_articles_file: Path to processed articles CSV
            export_dir: Directory for Power BI export files
        """
        self.processed_articles_file = processed_articles_file
        self.export_dir = export_dir
        
        # Create export directory
        os.makedirs(export_dir, exist_ok=True)
        
        # Define export file paths
        self.hourly_trends_file = os.path.join(export_dir, "hourly_trends.csv")
        self.topic_summary_file = os.path.join(export_dir, "topic_summary.csv")
        self.recent_articles_file = os.path.join(export_dir, "recent_articles.csv")
        self.daily_trends_file = os.path.join(export_dir, "daily_trends.csv")
    
    def load_processed_articles(self) -> pd.DataFrame:
        """
        Load processed articles from CSV.
        
        Returns:
            DataFrame of processed articles
        """
        
        if not os.path.exists(self.processed_articles_file):
            logger.warning(f"⚠️ Processed articles file not found: {self.processed_articles_file}")
            return pd.DataFrame()
        
        try:
            df = pd.read_csv(self.processed_articles_file)
            
            if df.empty:
                logger.warning("⚠️ No processed articles found")
                return df
            
            # Convert timestamps
            df['published_at'] = pd.to_datetime(df['published_at'])
            df['classified_at'] = pd.to_datetime(df['classified_at'])
            
            logger.info(f"📊 Loaded {len(df)} processed articles")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load processed articles: {e}")
            return pd.DataFrame()
    
    def create_hourly_trends(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create hourly topic trends for Power BI.
        
        Args:
            df: DataFrame of processed articles
            
        Returns:
            DataFrame with hourly trends
        """
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Group by hour and topic
            df['hour'] = df['published_at'].dt.floor('H')
            
            hourly_trends = df.groupby(['hour', 'topic_id', 'topic_label']).agg({
                'title': 'count',  # Article count
                'topic_confidence': ['mean', 'std']  # Average confidence
            }).reset_index()
            
            # Flatten column names
            hourly_trends.columns = [
                'hour', 'topic_id', 'topic_label', 
                'article_count', 'avg_confidence', 'confidence_std'
            ]
            
            # Fill NaN std with 0
            hourly_trends['confidence_std'] = hourly_trends['confidence_std'].fillna(0)
            
            # Round confidence values
            hourly_trends['avg_confidence'] = hourly_trends['avg_confidence'].round(3)
            hourly_trends['confidence_std'] = hourly_trends['confidence_std'].round(3)
            
            # Sort by hour and article count
            hourly_trends = hourly_trends.sort_values(['hour', 'article_count'], ascending=[True, False])
            
            logger.info(f"📈 Created hourly trends: {len(hourly_trends)} data points")
            return hourly_trends
            
        except Exception as e:
            logger.error(f"❌ Failed to create hourly trends: {e}")
            return pd.DataFrame()
    
    def create_topic_summary(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create topic summary for Power BI.
        
        Args:
            df: DataFrame of processed articles
            
        Returns:
            DataFrame with topic summary
        """
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Group by topic
            topic_summary = df.groupby(['topic_id', 'topic_label']).agg({
                'title': 'count',  # Total articles
                'topic_confidence': ['mean', 'std', 'min', 'max'],  # Confidence stats
                'published_at': ['min', 'max']  # Time range
            }).reset_index()
            
            # Flatten column names
            topic_summary.columns = [
                'topic_id', 'topic_label', 'total_articles',
                'avg_confidence', 'confidence_std', 'min_confidence', 'max_confidence',
                'first_article', 'latest_article'
            ]
            
            # Fill NaN std with 0
            topic_summary['confidence_std'] = topic_summary['confidence_std'].fillna(0)
            
            # Round confidence values
            for col in ['avg_confidence', 'confidence_std', 'min_confidence', 'max_confidence']:
                topic_summary[col] = topic_summary[col].round(3)
            
            # Calculate percentage of total articles
            total_articles = topic_summary['total_articles'].sum()
            topic_summary['percentage'] = (topic_summary['total_articles'] / total_articles * 100).round(1)
            
            # Sort by total articles
            topic_summary = topic_summary.sort_values('total_articles', ascending=False)
            
            logger.info(f"📊 Created topic summary: {len(topic_summary)} topics")
            return topic_summary
            
        except Exception as e:
            logger.error(f"❌ Failed to create topic summary: {e}")
            return pd.DataFrame()
    
    def create_recent_articles(self, df: pd.DataFrame, hours: int = 24) -> pd.DataFrame:
        """
        Create recent articles export for Power BI.
        
        Args:
            df: DataFrame of processed articles
            hours: Number of hours to look back
            
        Returns:
            DataFrame with recent articles
        """
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Filter recent articles
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_df = df[df['published_at'] >= cutoff_time].copy()
            
            if recent_df.empty:
                logger.warning(f"⚠️ No articles found in last {hours} hours")
                return pd.DataFrame()
            
            # Select relevant columns for Power BI
            columns_for_powerbi = [
                'title', 'source', 'author', 'published_at',
                'topic_id', 'topic_label', 'topic_confidence',
                'url'
            ]
            
            recent_articles = recent_df[columns_for_powerbi].copy()
            
            # Sort by published time (newest first)
            recent_articles = recent_articles.sort_values('published_at', ascending=False)
            
            # Round confidence
            recent_articles['topic_confidence'] = recent_articles['topic_confidence'].round(3)
            
            logger.info(f"📰 Created recent articles: {len(recent_articles)} articles from last {hours} hours")
            return recent_articles
            
        except Exception as e:
            logger.error(f"❌ Failed to create recent articles: {e}")
            return pd.DataFrame()
    
    def create_daily_trends(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create daily topic trends for Power BI.
        
        Args:
            df: DataFrame of processed articles
            
        Returns:
            DataFrame with daily trends
        """
        
        if df.empty:
            return pd.DataFrame()
        
        try:
            # Group by day and topic
            df['date'] = df['published_at'].dt.date
            
            daily_trends = df.groupby(['date', 'topic_id', 'topic_label']).agg({
                'title': 'count',  # Article count
                'topic_confidence': 'mean'  # Average confidence
            }).reset_index()
            
            # Flatten column names
            daily_trends.columns = ['date', 'topic_id', 'topic_label', 'article_count', 'avg_confidence']
            
            # Round confidence
            daily_trends['avg_confidence'] = daily_trends['avg_confidence'].round(3)
            
            # Sort by date and article count
            daily_trends = daily_trends.sort_values(['date', 'article_count'], ascending=[True, False])
            
            logger.info(f"📅 Created daily trends: {len(daily_trends)} data points")
            return daily_trends
            
        except Exception as e:
            logger.error(f"❌ Failed to create daily trends: {e}")
            return pd.DataFrame()
    
    def export_all(self, hours_for_recent: int = 24) -> Dict[str, bool]:
        """
        Export all Power BI files.
        
        Args:
            hours_for_recent: Hours to look back for recent articles
            
        Returns:
            Dictionary with export status for each file
        """
        
        logger.info("🚀 Starting Power BI export...")
        
        # Load processed articles
        df = self.load_processed_articles()
        
        if df.empty:
            logger.error("❌ No processed articles to export")
            return {
                'hourly_trends': False,
                'topic_summary': False,
                'recent_articles': False,
                'daily_trends': False
            }
        
        export_status = {}
        
        # Export hourly trends
        try:
            hourly_trends = self.create_hourly_trends(df)
            if not hourly_trends.empty:
                hourly_trends.to_csv(self.hourly_trends_file, index=False)
                logger.info(f"✅ Exported hourly trends: {self.hourly_trends_file}")
                export_status['hourly_trends'] = True
            else:
                export_status['hourly_trends'] = False
        except Exception as e:
            logger.error(f"❌ Failed to export hourly trends: {e}")
            export_status['hourly_trends'] = False
        
        # Export topic summary
        try:
            topic_summary = self.create_topic_summary(df)
            if not topic_summary.empty:
                topic_summary.to_csv(self.topic_summary_file, index=False)
                logger.info(f"✅ Exported topic summary: {self.topic_summary_file}")
                export_status['topic_summary'] = True
            else:
                export_status['topic_summary'] = False
        except Exception as e:
            logger.error(f"❌ Failed to export topic summary: {e}")
            export_status['topic_summary'] = False
        
        # Export recent articles
        try:
            recent_articles = self.create_recent_articles(df, hours_for_recent)
            if not recent_articles.empty:
                recent_articles.to_csv(self.recent_articles_file, index=False)
                logger.info(f"✅ Exported recent articles: {self.recent_articles_file}")
                export_status['recent_articles'] = True
            else:
                export_status['recent_articles'] = False
        except Exception as e:
            logger.error(f"❌ Failed to export recent articles: {e}")
            export_status['recent_articles'] = False
        
        # Export daily trends
        try:
            daily_trends = self.create_daily_trends(df)
            if not daily_trends.empty:
                daily_trends.to_csv(self.daily_trends_file, index=False)
                logger.info(f"✅ Exported daily trends: {self.daily_trends_file}")
                export_status['daily_trends'] = True
            else:
                export_status['daily_trends'] = False
        except Exception as e:
            logger.error(f"❌ Failed to export daily trends: {e}")
            export_status['daily_trends'] = False
        
        # Summary
        successful_exports = sum(export_status.values())
        total_exports = len(export_status)
        
        logger.info(f"🎯 Export complete: {successful_exports}/{total_exports} files exported successfully")
        
        return export_status

def main():
    """Test the Power BI exporter."""
    
    # Create exporter
    exporter = PowerBIExporter()
    
    # Run export
    status = exporter.export_all()
    
    # Show results
    logger.info("📊 Export Status:")
    for file_type, success in status.items():
        status_icon = "✅" if success else "❌"
        logger.info(f"  {status_icon} {file_type}")

if __name__ == "__main__":
    main()
