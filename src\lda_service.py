"""
LDA Model Service for real-time topic classification.
Loads trained .pkl models and classifies incoming CSV data.
"""

import pandas as pd
import pickle
import numpy as np
from pathlib import Path
from datetime import datetime
import logging
from typing import Dict, Any, List, Optional
import json

from .preprocessing import TextPreprocessor
from .config import config
from .utils import save_dataframe

logger = logging.getLogger(__name__)

class LDAModelService:
    """Service for loading and using trained LDA models."""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize LDA service.
        
        Args:
            model_path: Path to trained .pkl model file
        """
        self.model_path = model_path
        self.model = None
        self.vectorizer = None
        self.topics = {}
        self.preprocessor = TextPreprocessor()
        
        # Paths
        self.data_dir = Path(config.get('output.data_dir', 'data'))
        self.models_dir = Path(config.get('output.model_dir', 'models'))
        self.results_dir = self.data_dir / 'realtime_results'
        self.results_dir.mkdir(exist_ok=True)
        
        # Load model if path provided
        if model_path:
            self.load_model(model_path)
        else:
            self._load_latest_model()
    
    def _load_latest_model(self):
        """Load the most recent trained model."""
        model_files = list(self.models_dir.glob('topic_model_*.pkl'))
        
        if not model_files:
            logger.error("No trained models found. Please train a model first.")
            return False
        
        # Get the most recent model
        latest_model = max(model_files, key=lambda f: f.stat().st_mtime)
        return self.load_model(str(latest_model))
    
    def load_model(self, model_path: str) -> bool:
        """
        Load trained LDA model from .pkl file.
        
        Args:
            model_path: Path to .pkl model file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.vectorizer = model_data.get('vectorizer')
            self.topics = model_data['topics']
            self.method = model_data.get('method', 'sklearn')
            
            logger.info(f"✅ LDA model loaded successfully from {model_path}")
            logger.info(f"Model has {len(self.topics)} topics")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load model from {model_path}: {e}")
            return False
    
    def classify_csv_data(self, csv_file_path: str) -> Dict[str, Any]:
        """
        Classify articles from CSV file using trained LDA model.
        
        Args:
            csv_file_path: Path to CSV file with articles
            
        Returns:
            Classification results
        """
        if self.model is None:
            logger.error("No model loaded. Cannot classify data.")
            return {'success': False, 'error': 'No model loaded'}
        
        try:
            # Load CSV data
            df = pd.read_csv(csv_file_path)
            logger.info(f"Processing {len(df)} articles from {csv_file_path}")
            
            if df.empty:
                return {'success': True, 'articles_processed': 0, 'message': 'No articles to process'}
            
            # Preprocess text
            processed_df = self._preprocess_for_classification(df)
            
            if processed_df.empty:
                return {'success': True, 'articles_processed': 0, 'message': 'No valid articles after preprocessing'}
            
            # Classify articles
            classified_df = self._classify_articles(processed_df)
            
            # Save results
            results_file = self._save_classification_results(classified_df)
            
            # Update real-time dashboard data
            self._update_dashboard_data(classified_df)
            
            # Generate summary
            summary = self._generate_classification_summary(classified_df)
            
            return {
                'success': True,
                'articles_processed': len(classified_df),
                'results_file': results_file,
                'summary': summary,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Classification failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _preprocess_for_classification(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess articles for classification."""
        # Combine text fields
        text_columns = ['title', 'description', 'content']
        combined_text = []
        
        for _, row in df.iterrows():
            text_parts = []
            for col in text_columns:
                if col in row and pd.notna(row[col]) and str(row[col]).strip():
                    text_parts.append(str(row[col]))
            combined_text.append(' '.join(text_parts))
        
        df['combined_text'] = combined_text
        
        # Preprocess text
        cleaned_texts = []
        for text in df['combined_text']:
            cleaned = self.preprocessor.preprocess_text(text)
            cleaned_texts.append(cleaned)
        
        df['cleaned_text'] = cleaned_texts
        
        # Filter out articles with insufficient content
        df = df[df['cleaned_text'].str.len() > 20]  # At least 20 characters
        
        return df
    
    def _classify_articles(self, df: pd.DataFrame) -> pd.DataFrame:
        """Classify articles using the loaded model."""
        documents = df['cleaned_text'].tolist()
        
        if self.method == 'sklearn':
            # Transform documents using the trained vectorizer
            doc_term_matrix = self.vectorizer.transform(documents)
            
            # Get topic distributions
            topic_distributions = self.model.transform(doc_term_matrix)
            
            # Assign topics
            for i, distribution in enumerate(topic_distributions):
                dominant_topic = np.argmax(distribution)
                confidence = distribution[dominant_topic]
                
                df.loc[df.index[i], 'topic_id'] = int(dominant_topic)
                df.loc[df.index[i], 'topic_label'] = self.topics[dominant_topic]['label']
                df.loc[df.index[i], 'topic_keywords'] = ', '.join(self.topics[dominant_topic]['keywords'][:5])
                df.loc[df.index[i], 'topic_confidence'] = float(confidence)
                df.loc[df.index[i], 'topic_distribution'] = json.dumps(distribution.tolist())
        
        # Add classification metadata
        df['classification_timestamp'] = datetime.now()
        df['model_version'] = self.model_path or 'latest'
        
        return df
    
    def _save_classification_results(self, df: pd.DataFrame) -> str:
        """Save classification results to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"classified_articles_{timestamp}.csv"
        file_path = self.results_dir / filename
        
        # Save detailed results
        df.to_csv(file_path, index=False, encoding='utf-8')
        
        # Also update the latest results file for dashboard
        latest_file = self.data_dir / 'latest_classified.csv'
        df.to_csv(latest_file, index=False, encoding='utf-8')
        
        logger.info(f"Classification results saved: {file_path}")
        return str(file_path)
    
    def _update_dashboard_data(self, df: pd.DataFrame):
        """Update real-time dashboard data."""
        # Create hourly summary for dashboard
        current_hour = datetime.now().strftime('%Y-%m-%d %H:00:00')
        
        # Topic distribution for current hour
        topic_counts = df['topic_id'].value_counts().to_dict()
        topic_summary = []
        
        for topic_id, count in topic_counts.items():
            topic_info = self.topics.get(topic_id, {})
            topic_summary.append({
                'hour': current_hour,
                'topic_id': topic_id,
                'topic_label': topic_info.get('label', f'Topic_{topic_id}'),
                'topic_keywords': ', '.join(topic_info.get('keywords', [])[:5]),
                'article_count': count,
                'avg_confidence': df[df['topic_id'] == topic_id]['topic_confidence'].mean()
            })
        
        # Save hourly summary
        summary_df = pd.DataFrame(topic_summary)
        hourly_summary_file = self.data_dir / 'hourly_topic_summary.csv'
        
        # Append to existing file or create new
        if hourly_summary_file.exists():
            existing_df = pd.read_csv(hourly_summary_file)
            combined_df = pd.concat([existing_df, summary_df], ignore_index=True)
            # Keep only last 24 hours
            combined_df['hour'] = pd.to_datetime(combined_df['hour'])
            cutoff_time = datetime.now() - pd.Timedelta(hours=24)
            combined_df = combined_df[combined_df['hour'] >= cutoff_time]
        else:
            combined_df = summary_df
        
        combined_df.to_csv(hourly_summary_file, index=False)
        
        # Create Power BI real-time data
        self._create_powerbi_realtime_data(df)
    
    def _create_powerbi_realtime_data(self, df: pd.DataFrame):
        """Create Power BI compatible real-time data."""
        # Prepare data for Power BI
        powerbi_data = df.copy()
        
        # Add time-based columns
        powerbi_data['hour'] = pd.to_datetime(powerbi_data['classification_timestamp']).dt.hour
        powerbi_data['date'] = pd.to_datetime(powerbi_data['classification_timestamp']).dt.date
        powerbi_data['datetime'] = pd.to_datetime(powerbi_data['classification_timestamp'])
        
        # Select relevant columns for dashboard
        dashboard_columns = [
            'title', 'source_name', 'publishedAt', 'topic_id', 'topic_label', 
            'topic_keywords', 'topic_confidence', 'hour', 'date', 'datetime'
        ]
        
        available_columns = [col for col in dashboard_columns if col in powerbi_data.columns]
        dashboard_df = powerbi_data[available_columns]
        
        # Save for Power BI
        powerbi_file = self.data_dir / 'powerbi_realtime.csv'
        dashboard_df.to_csv(powerbi_file, index=False)
        
        logger.info(f"Power BI real-time data updated: {powerbi_file}")
    
    def _generate_classification_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate summary of classification results."""
        summary = {
            'total_articles': len(df),
            'topics_found': df['topic_id'].nunique(),
            'avg_confidence': df['topic_confidence'].mean(),
            'topic_distribution': df['topic_id'].value_counts().to_dict(),
            'top_topics': []
        }
        
        # Get top 3 topics
        top_topics = df['topic_id'].value_counts().head(3)
        for topic_id, count in top_topics.items():
            topic_info = self.topics.get(topic_id, {})
            summary['top_topics'].append({
                'topic_id': topic_id,
                'topic_label': topic_info.get('label', f'Topic_{topic_id}'),
                'article_count': count,
                'percentage': round((count / len(df)) * 100, 1)
            })
        
        return summary
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if self.model is None:
            return {'loaded': False}
        
        return {
            'loaded': True,
            'model_path': self.model_path,
            'num_topics': len(self.topics),
            'method': getattr(self, 'method', 'unknown'),
            'topics': [
                {
                    'id': topic_id,
                    'label': topic_info['label'],
                    'keywords': topic_info['keywords'][:5]
                }
                for topic_id, topic_info in self.topics.items()
            ]
        }

class RealTimeProcessor:
    """Real-time processor that monitors for new data and triggers classification."""
    
    def __init__(self):
        self.lda_service = LDAModelService()
        self.data_dir = Path(config.get('output.data_dir', 'data'))
        self.signal_file = self.data_dir / 'new_data_signal.txt'
    
    def start_monitoring(self):
        """Start monitoring for new data signals."""
        logger.info("Starting real-time LDA processing monitor...")
        
        while True:
            try:
                if self.signal_file.exists():
                    # Read signal file
                    with open(self.signal_file, 'r') as f:
                        lines = f.readlines()
                    
                    if lines:
                        csv_file_path = lines[0].strip()
                        timestamp = lines[1].strip() if len(lines) > 1 else datetime.now().isoformat()
                        
                        logger.info(f"Processing new data: {csv_file_path}")
                        
                        # Classify the data
                        result = self.lda_service.classify_csv_data(csv_file_path)
                        
                        if result['success']:
                            logger.info(f"✅ Classification completed: {result['articles_processed']} articles")
                        else:
                            logger.error(f"❌ Classification failed: {result.get('error')}")
                        
                        # Remove signal file
                        self.signal_file.unlink()
                
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                logger.info("Real-time processor stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in real-time processor: {e}")
                time.sleep(60)  # Wait longer on error

def main():
    """Main function for LDA service."""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'monitor':
            # Start real-time monitoring
            processor = RealTimeProcessor()
            processor.start_monitoring()
        elif sys.argv[1] == 'classify':
            # Classify a specific file
            if len(sys.argv) > 2:
                service = LDAModelService()
                result = service.classify_csv_data(sys.argv[2])
                print(json.dumps(result, indent=2))
            else:
                print("Usage: python lda_service.py classify <csv_file_path>")
        else:
            print("Usage: python lda_service.py [monitor|classify <file>]")
    else:
        # Default: start monitoring
        processor = RealTimeProcessor()
        processor.start_monitoring()

if __name__ == "__main__":
    main()
