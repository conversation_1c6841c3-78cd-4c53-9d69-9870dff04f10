"""
Tests for topic modeling functionality.
"""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import tempfile
import pickle

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Mock the config to avoid dependency issues in tests
with patch('src.topic_modeling.config') as mock_config:
    mock_config.topic_modeling = {
        'num_topics': 3,
        'max_features': 100,
        'min_df': 1,
        'max_df': 0.9,
        'ngram_range': [1, 2],
        'random_state': 42,
        'max_iter': 10,
        'learning_method': 'batch'
    }
    mock_config.get.return_value = 'test_models'
    from src.topic_modeling import TopicModeler

class TestTopicModeler:
    """Test topic modeling functionality."""
    
    @pytest.fixture
    def sample_documents(self):
        """Create sample documents for testing."""
        return [
            "artificial intelligence machine learning technology",
            "politics government election voting democracy",
            "economy finance market stock business",
            "artificial intelligence neural networks deep learning",
            "political campaign election results voting",
            "economic growth market trends financial analysis"
        ]
    
    @pytest.fixture
    def topic_modeler(self):
        """Create a topic modeler instance."""
        return TopicModeler(method='sklearn')
    
    def test_create_vectorizer(self, topic_modeler):
        """Test vectorizer creation."""
        vectorizer = topic_modeler._create_vectorizer()
        
        assert vectorizer is not None
        assert vectorizer.max_features == 100
        assert vectorizer.min_df == 1
        assert vectorizer.max_df == 0.9
        assert vectorizer.ngram_range == (1, 2)
    
    def test_fit_sklearn_lda(self, topic_modeler, sample_documents):
        """Test fitting LDA model with sklearn."""
        topic_modeler.fit(sample_documents)
        
        # Check that model is trained
        assert topic_modeler.model is not None
        assert topic_modeler.vectorizer is not None
        assert topic_modeler.feature_names is not None
        assert len(topic_modeler.topics) == 3  # num_topics from config
        
        # Check topic structure
        for topic_id, topic_info in topic_modeler.topics.items():
            assert 'keywords' in topic_info
            assert 'weights' in topic_info
            assert 'label' in topic_info
            assert len(topic_info['keywords']) > 0
    
    def test_predict_topics(self, topic_modeler, sample_documents):
        """Test topic prediction."""
        # First fit the model
        topic_modeler.fit(sample_documents)
        
        # Predict topics
        predictions = topic_modeler.predict_topics(sample_documents)
        
        assert len(predictions) == len(sample_documents)
        
        for pred in predictions:
            assert 'document_index' in pred
            assert 'topic_id' in pred
            assert 'topic_label' in pred
            assert 'topic_keywords' in pred
            assert 'confidence' in pred
            assert 'topic_distribution' in pred
            
            # Check data types
            assert isinstance(pred['topic_id'], int)
            assert isinstance(pred['confidence'], float)
            assert isinstance(pred['topic_keywords'], list)
            assert 0 <= pred['confidence'] <= 1
    
    def test_generate_topic_label(self, topic_modeler):
        """Test topic label generation."""
        top_words = ['artificial', 'intelligence', 'machine']
        label = topic_modeler._generate_topic_label(top_words)
        
        assert isinstance(label, str)
        assert len(label) > 0
        assert 'Artificial_Intelligence_Machine' == label
    
    def test_get_topic_summary(self, topic_modeler, sample_documents):
        """Test topic summary generation."""
        topic_modeler.fit(sample_documents)
        summary = topic_modeler.get_topic_summary()
        
        assert isinstance(summary, pd.DataFrame)
        assert len(summary) == 3  # num_topics
        
        expected_columns = ['topic_id', 'topic_label', 'top_keywords', 'all_keywords']
        for col in expected_columns:
            assert col in summary.columns
    
    def test_save_and_load_model(self, topic_modeler, sample_documents):
        """Test model saving and loading."""
        # Fit model
        topic_modeler.fit(sample_documents)
        
        # Save model
        with tempfile.TemporaryDirectory() as temp_dir:
            topic_modeler.model_dir = Path(temp_dir)
            model_path = topic_modeler.save_model('test_model.pkl')
            
            assert Path(model_path).exists()
            
            # Create new modeler and load model
            new_modeler = TopicModeler()
            new_modeler.load_model(model_path)
            
            # Check that model is loaded correctly
            assert new_modeler.model is not None
            assert new_modeler.topics == topic_modeler.topics
            assert new_modeler.method == topic_modeler.method
    
    def test_empty_documents(self, topic_modeler):
        """Test handling of empty document list."""
        with pytest.raises(ValueError):
            topic_modeler.fit([])
    
    def test_predict_without_fitting(self, topic_modeler, sample_documents):
        """Test prediction without fitting model first."""
        with pytest.raises(ValueError):
            topic_modeler.predict_topics(sample_documents)
    
    def test_insufficient_documents(self, topic_modeler):
        """Test handling of insufficient documents."""
        # Very few documents
        few_docs = ["test document"]
        
        # Should still work but may produce poor results
        topic_modeler.fit(few_docs)
        assert topic_modeler.model is not None

class TestTopicModelingEdgeCases:
    """Test edge cases in topic modeling."""
    
    @pytest.fixture
    def topic_modeler(self):
        return TopicModeler(method='sklearn')
    
    def test_identical_documents(self, topic_modeler):
        """Test with identical documents."""
        identical_docs = ["same text"] * 5
        topic_modeler.fit(identical_docs)
        
        predictions = topic_modeler.predict_topics(identical_docs)
        
        # All documents should get the same topic
        topic_ids = [pred['topic_id'] for pred in predictions]
        assert len(set(topic_ids)) <= 2  # Should be mostly the same topic
    
    def test_very_short_documents(self, topic_modeler):
        """Test with very short documents."""
        short_docs = ["a", "b", "c", "d", "e"]
        
        # Should handle gracefully (may produce poor results)
        topic_modeler.fit(short_docs)
        predictions = topic_modeler.predict_topics(short_docs)
        
        assert len(predictions) == len(short_docs)
    
    def test_documents_with_no_valid_tokens(self, topic_modeler):
        """Test documents that produce no valid tokens after preprocessing."""
        invalid_docs = ["123", "!!!", "   ", "a b c"]  # Numbers, punctuation, short words
        
        # Should handle gracefully
        topic_modeler.fit(invalid_docs)
        assert topic_modeler.model is not None
    
    def test_mixed_quality_documents(self, topic_modeler):
        """Test mix of good and poor quality documents."""
        mixed_docs = [
            "artificial intelligence machine learning deep neural networks",
            "a",
            "politics government democracy election voting systems",
            "123 !!! @@@",
            "economic analysis financial markets stock trading"
        ]
        
        topic_modeler.fit(mixed_docs)
        predictions = topic_modeler.predict_topics(mixed_docs)
        
        assert len(predictions) == len(mixed_docs)
        
        # Good documents should have higher confidence
        good_doc_indices = [0, 2, 4]
        for idx in good_doc_indices:
            assert predictions[idx]['confidence'] >= 0  # Should be non-negative

class TestTopicModelingIntegration:
    """Integration tests for topic modeling."""
    
    def test_end_to_end_workflow(self):
        """Test complete topic modeling workflow."""
        # Create realistic sample data
        articles_data = {
            'title': [
                'AI Revolution in Healthcare',
                'Election Results Show Close Race',
                'Stock Market Hits New High',
                'Machine Learning Breakthrough',
                'Political Campaign Updates',
                'Economic Growth Continues'
            ],
            'cleaned_text': [
                'artificial intelligence healthcare medical diagnosis treatment',
                'election voting results democracy political campaign',
                'stock market financial growth investment trading',
                'machine learning algorithm neural network breakthrough',
                'political campaign election voting democracy',
                'economic growth market financial analysis business'
            ]
        }
        
        df = pd.DataFrame(articles_data)
        
        # Initialize modeler
        modeler = TopicModeler(method='sklearn')
        
        # Fit model
        documents = df['cleaned_text'].tolist()
        modeler.fit(documents)
        
        # Predict topics
        predictions = modeler.predict_topics(documents)
        
        # Add predictions to dataframe
        for i, pred in enumerate(predictions):
            df.loc[i, 'topic_id'] = pred['topic_id']
            df.loc[i, 'topic_label'] = pred['topic_label']
            df.loc[i, 'topic_keywords'] = ', '.join(pred['topic_keywords'])
            df.loc[i, 'topic_confidence'] = pred['confidence']
        
        # Verify results
        assert 'topic_id' in df.columns
        assert 'topic_label' in df.columns
        assert 'topic_keywords' in df.columns
        assert 'topic_confidence' in df.columns
        
        # Check that topics are assigned
        assert df['topic_id'].notna().all()
        assert df['topic_confidence'].between(0, 1).all()
        
        # Get topic summary
        summary = modeler.get_topic_summary()
        assert len(summary) > 0
