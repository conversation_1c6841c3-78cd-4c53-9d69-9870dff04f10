#!/usr/bin/env python3
"""
Main Workflow Orchestrator
Complete Pipeline: [NewsAPI Request] → [Parse JSON] → [Append to CSV] → [LDA Model] → [Power BI]
"""

import os
import sys
import time
import schedule
from datetime import datetime
from typing import Dict, List
import logging
from dotenv import load_dotenv

# Import our pipeline components
from newsapi_collector import NewsAPICollector
from csv_pipeline import CSVPipeline
from lda_classifier import LDAClassifier
from powerbi_exporter import PowerBIExporter

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/workflow.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TopicModelingWorkflow:
    """Main workflow orchestrator for the complete pipeline."""
    
    def __init__(self):
        """Initialize the workflow with all components."""
        
        # Load environment variables
        load_dotenv('config/.env')
        
        # Get API key
        self.api_key = os.getenv('NEWS_API_KEY')
        if not self.api_key:
            logger.error("❌ NEWS_API_KEY not found in environment variables")
            sys.exit(1)
        
        # Initialize components
        logger.info("🚀 Initializing workflow components...")
        
        self.news_collector = NewsAPICollector(self.api_key)
        self.csv_pipeline = CSVPipeline()
        self.lda_classifier = LDAClassifier()
        self.powerbi_exporter = PowerBIExporter()
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
        
        logger.info("✅ Workflow initialized successfully")
    
    def run_single_cycle(self, keywords: List[str] = None, hours_back: int = 1) -> Dict:
        """
        Run a single cycle of the complete workflow.
        
        Args:
            keywords: Keywords for news collection
            hours_back: Hours to look back for news
            
        Returns:
            Dictionary with cycle results
        """
        
        cycle_start = datetime.now()
        logger.info(f"🔄 Starting workflow cycle at {cycle_start.strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            'start_time': cycle_start,
            'articles_collected': 0,
            'articles_classified': 0,
            'powerbi_exported': False,
            'errors': []
        }
        
        try:
            # Step 1: Collect articles from NewsAPI
            logger.info("📰 Step 1: Collecting articles from NewsAPI...")
            
            if keywords is None:
                keywords = ["technology", "artificial intelligence", "politics", "business", "economy"]
            
            articles = self.news_collector.fetch_articles(
                keywords=keywords,
                hours_back=hours_back,
                max_articles=100
            )
            
            if not articles:
                logger.warning("⚠️ No articles collected")
                results['errors'].append("No articles collected from NewsAPI")
                return results
            
            results['articles_collected'] = len(articles)
            logger.info(f"✅ Collected {len(articles)} articles")
            
            # Step 2: Append to CSV
            logger.info("📄 Step 2: Appending articles to CSV...")
            
            appended_count = self.csv_pipeline.append_raw_articles(articles)
            
            if appended_count == 0:
                logger.warning("⚠️ No new articles to process (all duplicates)")
                # Still continue to process any existing unprocessed articles
            
            # Step 3: Get unprocessed articles and classify with LDA
            logger.info("🧠 Step 3: Classifying articles with LDA model...")
            
            unprocessed_df = self.csv_pipeline.get_unprocessed_articles()
            
            if unprocessed_df.empty:
                logger.info("ℹ️ No unprocessed articles found")
            else:
                classified_articles = self.lda_classifier.classify_articles_batch(
                    unprocessed_df,
                    confidence_threshold=0.3
                )
                
                if classified_articles:
                    # Append classified articles to processed CSV
                    processed_count = self.csv_pipeline.append_processed_articles(classified_articles)
                    results['articles_classified'] = processed_count
                    logger.info(f"✅ Classified and saved {processed_count} articles")
                else:
                    logger.warning("⚠️ No articles met classification confidence threshold")
            
            # Step 4: Export to Power BI
            logger.info("📊 Step 4: Exporting data for Power BI...")
            
            export_status = self.powerbi_exporter.export_all(hours_for_recent=24)
            
            successful_exports = sum(export_status.values())
            total_exports = len(export_status)
            
            if successful_exports > 0:
                results['powerbi_exported'] = True
                logger.info(f"✅ Power BI export complete: {successful_exports}/{total_exports} files")
            else:
                logger.error("❌ Power BI export failed")
                results['errors'].append("Power BI export failed")
            
            # Cycle summary
            cycle_end = datetime.now()
            duration = (cycle_end - cycle_start).total_seconds()
            
            logger.info(f"🎯 Cycle complete in {duration:.1f} seconds")
            logger.info(f"📊 Summary: {results['articles_collected']} collected, {results['articles_classified']} classified")
            
            results['end_time'] = cycle_end
            results['duration_seconds'] = duration
            
        except Exception as e:
            logger.error(f"❌ Workflow cycle failed: {e}")
            results['errors'].append(str(e))
        
        return results
    
    def run_hourly_schedule(self):
        """Run the workflow on an hourly schedule."""
        
        logger.info("⏰ Starting hourly scheduled workflow")
        logger.info("🔄 Will run every hour at :05 minutes")
        
        # Schedule the job
        schedule.every().hour.at(":05").do(self.run_single_cycle)
        
        # Keep running
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
            except KeyboardInterrupt:
                logger.info("⏹️ Stopping scheduled workflow")
                break
            except Exception as e:
                logger.error(f"❌ Scheduler error: {e}")
                time.sleep(300)  # Wait 5 minutes before retrying
    
    def get_pipeline_status(self) -> Dict:
        """
        Get current pipeline status.
        
        Returns:
            Dictionary with pipeline status
        """
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'components': {},
            'data_stats': {}
        }
        
        # Check component status
        status['components']['news_collector'] = bool(self.api_key)
        status['components']['lda_classifier'] = self.lda_classifier.is_loaded
        status['components']['csv_pipeline'] = os.path.exists('data')
        status['components']['powerbi_exporter'] = os.path.exists('data/powerbi')
        
        # Get data statistics
        try:
            stats = self.csv_pipeline.get_stats()
            status['data_stats'] = stats
        except Exception as e:
            logger.error(f"❌ Failed to get data stats: {e}")
            status['data_stats'] = {'error': str(e)}
        
        return status

def main():
    """Main entry point."""
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python main_workflow.py run          # Run single cycle")
        print("  python main_workflow.py schedule     # Run hourly schedule")
        print("  python main_workflow.py status       # Show pipeline status")
        return
    
    command = sys.argv[1].lower()
    
    try:
        # Initialize workflow
        workflow = TopicModelingWorkflow()
        
        if command == "run":
            # Run single cycle
            logger.info("🚀 Running single workflow cycle...")
            results = workflow.run_single_cycle()
            
            # Show results
            print(f"\n📊 Workflow Results:")
            print(f"  Articles collected: {results['articles_collected']}")
            print(f"  Articles classified: {results['articles_classified']}")
            print(f"  Power BI exported: {'✅' if results['powerbi_exported'] else '❌'}")
            
            if results['errors']:
                print(f"  Errors: {len(results['errors'])}")
                for error in results['errors']:
                    print(f"    - {error}")
        
        elif command == "schedule":
            # Run hourly schedule
            workflow.run_hourly_schedule()
        
        elif command == "status":
            # Show pipeline status
            status = workflow.get_pipeline_status()
            
            print(f"\n📊 Pipeline Status ({status['timestamp']}):")
            print(f"  Components:")
            for component, is_ready in status['components'].items():
                icon = "✅" if is_ready else "❌"
                print(f"    {icon} {component}")
            
            print(f"  Data Statistics:")
            for stat, value in status['data_stats'].items():
                print(f"    {stat}: {value}")
        
        else:
            print(f"❌ Unknown command: {command}")
            print("Use 'run', 'schedule', or 'status'")
    
    except Exception as e:
        logger.error(f"❌ Main workflow failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
