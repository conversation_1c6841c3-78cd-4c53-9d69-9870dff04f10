-- =====================================================
-- Essential Power BI DirectQuery Views for MySQL
-- Optimized for real-time topic modeling dashboards
-- =====================================================

USE TopicModelingDB;

-- =====================================================
-- CORE DASHBOARD VIEWS
-- =====================================================

-- 1. Topic Summary (Main overview)
CREATE OR REPLACE VIEW vw_topic_summary AS
SELECT 
    t.topic_id,
    t.topic_label,
    COUNT(tr.result_id) as total_articles,
    AVG(tr.confidence) as avg_confidence,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    MAX(tr.classification_timestamp) as last_updated,
    
    -- Time-based metrics
    SUM(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 ELSE 0 END) as articles_last_hour,
    SUM(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as articles_last_24h,
    
    -- Confidence categories
    SUM(CASE WHEN tr.confidence >= 0.7 THEN 1 ELSE 0 END) as high_confidence_articles,
    SUM(CASE WHEN tr.confidence < 0.7 THEN 1 ELSE 0 END) as low_confidence_articles
    
FROM topics t
LEFT JOIN topic_results tr ON t.topic_id = tr.topic_id
LEFT JOIN raw_articles ra ON tr.article_id = ra.article_id
GROUP BY t.topic_id, t.topic_label;

-- 2. Key Performance Indicators
CREATE OR REPLACE VIEW vw_kpi_metrics AS
SELECT 
    COUNT(DISTINCT ra.article_id) as total_articles,
    COUNT(DISTINCT t.topic_id) as active_topics,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    AVG(tr.confidence) as overall_avg_confidence,
    MAX(ra.published_at) as latest_article_time,
    MAX(tr.classification_timestamp) as latest_classification_time,
    
    -- Pipeline health
    COUNT(CASE WHEN ra.processing_status = 'pending' THEN 1 END) as pending_articles,
    COUNT(CASE WHEN ra.processing_status = 'processed' THEN 1 END) as processed_articles,
    COUNT(CASE WHEN ra.processing_status = 'failed' THEN 1 END) as failed_articles
    
FROM raw_articles ra
LEFT JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
LEFT JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
LEFT JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 3. Hourly Trends (Real-time analysis)
CREATE OR REPLACE VIEW vw_hourly_trends AS
SELECT 
    DATE_FORMAT(tr.classification_timestamp, '%Y-%m-%d %H:00:00') as hour_bucket,
    t.topic_id,
    t.topic_label,
    COUNT(tr.result_id) as article_count,
    AVG(tr.confidence) as avg_confidence,
    COUNT(DISTINCT ra.source_name) as unique_sources
    
FROM topic_results tr
INNER JOIN topics t ON tr.topic_id = t.topic_id
INNER JOIN raw_articles ra ON tr.article_id = ra.article_id
WHERE tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY 
    DATE_FORMAT(tr.classification_timestamp, '%Y-%m-%d %H:00:00'),
    t.topic_id, 
    t.topic_label
ORDER BY hour_bucket DESC;

-- 4. Latest Articles (Real-time feed)
CREATE OR REPLACE VIEW vw_realtime_articles AS
SELECT 
    ra.article_id,
    ra.title,
    ra.source_name,
    ra.published_at,
    t.topic_label,
    tr.confidence,
    tr.classification_timestamp,
    
    -- Freshness indicator
    CASE 
        WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'Very Fresh'
        WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 6 HOUR) THEN 'Fresh'
        ELSE 'Recent'
    END as freshness
    
FROM raw_articles ra
INNER JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
INNER JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
INNER JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY ra.published_at DESC
LIMIT 100;

-- 5. Source Analysis
CREATE OR REPLACE VIEW vw_source_analysis AS
SELECT 
    ra.source_name,
    COUNT(DISTINCT ra.article_id) as total_articles,
    AVG(tr.confidence) as avg_confidence,
    COUNT(DISTINCT t.topic_id) as topic_diversity,
    SUM(CASE WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as articles_last_24h,
    MAX(ra.published_at) as latest_article
    
FROM raw_articles ra
INNER JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
INNER JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
INNER JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY ra.source_name
ORDER BY total_articles DESC;

-- 6. Pipeline Health Monitor
CREATE OR REPLACE VIEW vw_pipeline_health AS
SELECT 
    -- Processing status counts
    COUNT(CASE WHEN ra.processing_status = 'pending' THEN 1 END) as pending_articles,
    COUNT(CASE WHEN ra.processing_status = 'processed' THEN 1 END) as processed_articles,
    COUNT(CASE WHEN ra.processing_status = 'failed' THEN 1 END) as failed_articles,
    
    -- Recent activity
    COUNT(CASE WHEN ra.fetched_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as fetched_last_hour,
    COUNT(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as classified_last_hour,
    
    -- Data freshness
    MAX(ra.fetched_at) as last_fetch_time,
    MAX(tr.classification_timestamp) as last_classification_time,
    
    -- Performance metrics
    AVG(pa.preprocessing_duration_ms) as avg_preprocessing_time_ms,
    AVG(tr.processing_duration_ms) as avg_classification_time_ms
    
FROM raw_articles ra
LEFT JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
LEFT JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
WHERE ra.fetched_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- =====================================================
-- VERIFICATION
-- =====================================================

SHOW FULL TABLES WHERE Table_type = 'VIEW';
SELECT 'Power BI views created successfully' as status;
