-- =====================================================
-- Power BI DirectQuery Optimized Views
-- High-performance views designed for real-time dashboards
-- =====================================================

-- =====================================================
-- 1. OVERVIEW DASHBOARD VIEWS
-- =====================================================

-- Real-time topic summary for overview cards
CREATE OR ALTER VIEW vw_topic_summary AS
SELECT 
    t.topic_id,
    t.topic_label,
    t.topic_keywords,
    COUNT(at.article_id) as total_articles,
    AVG(at.confidence) as avg_confidence,
    STDEV(at.confidence) as confidence_std,
    MIN(at.confidence) as min_confidence,
    MAX(at.confidence) as max_confidence,
    MIN(a.published_at) as first_article_date,
    MAX(a.published_at) as latest_article_date,
    COUNT(DISTINCT a.source_name) as unique_sources,
    CAST(COUNT(at.article_id) * 100.0 / SUM(COUNT(at.article_id)) OVER() AS DECIMAL(5,2)) as percentage,
    MAX(at.classified_at) as last_updated
FROM topics t
LEFT JOIN article_topics at ON t.topic_id = at.topic_id
LEFT JOIN articles a ON at.article_id = a.article_id
GROUP BY t.topic_id, t.topic_label, t.topic_keywords;

-- Key performance indicators
CREATE OR ALTER VIEW vw_kpi_metrics AS
SELECT 
    COUNT(DISTINCT a.article_id) as total_articles,
    COUNT(DISTINCT CASE WHEN at.confidence > 0.7 THEN a.article_id END) as high_confidence_articles,
    COUNT(DISTINCT t.topic_id) as active_topics,
    COUNT(DISTINCT a.source_name) as unique_sources,
    AVG(at.confidence) as overall_avg_confidence,
    CAST(COUNT(DISTINCT CASE WHEN at.confidence > 0.3 THEN a.article_id END) * 100.0 / COUNT(DISTINCT a.article_id) AS DECIMAL(5,2)) as classification_rate,
    MAX(a.published_at) as latest_article_time,
    MAX(at.classified_at) as latest_classification_time
FROM articles a
LEFT JOIN article_topics at ON a.article_id = at.article_id
LEFT JOIN topics t ON at.topic_id = t.topic_id
WHERE a.published_at >= DATEADD(DAY, -30, GETDATE()); -- Last 30 days

-- =====================================================
-- 2. TREND ANALYSIS VIEWS
-- =====================================================

-- Daily trends with rolling averages
CREATE OR ALTER VIEW vw_daily_trends_enhanced AS
SELECT 
    ttd.*,
    AVG(ttd.article_count) OVER (
        PARTITION BY ttd.topic_id 
        ORDER BY ttd.date_key 
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as rolling_7day_avg,
    LAG(ttd.article_count, 1) OVER (
        PARTITION BY ttd.topic_id 
        ORDER BY ttd.date_key
    ) as prev_day_count,
    CASE 
        WHEN LAG(ttd.article_count, 1) OVER (PARTITION BY ttd.topic_id ORDER BY ttd.date_key) > 0
        THEN CAST((ttd.article_count - LAG(ttd.article_count, 1) OVER (PARTITION BY ttd.topic_id ORDER BY ttd.date_key)) * 100.0 / 
                  LAG(ttd.article_count, 1) OVER (PARTITION BY ttd.topic_id ORDER BY ttd.date_key) AS DECIMAL(5,2))
        ELSE 0
    END as day_over_day_change_pct,
    DATEPART(WEEKDAY, ttd.date_key) as day_of_week_num,
    DATENAME(WEEKDAY, ttd.date_key) as day_of_week_name
FROM topic_trends_daily ttd
WHERE ttd.date_key >= DATEADD(DAY, -90, GETDATE()); -- Last 90 days for performance

-- Hourly trends with time intelligence
CREATE OR ALTER VIEW vw_hourly_trends_enhanced AS
SELECT 
    tth.*,
    AVG(tth.article_count) OVER (
        PARTITION BY tth.topic_id, tth.hour_of_day
        ORDER BY tth.hour_key
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as rolling_7hour_avg,
    CASE 
        WHEN tth.hour_of_day BETWEEN 6 AND 18 THEN 'Business Hours'
        WHEN tth.hour_of_day BETWEEN 19 AND 23 THEN 'Evening'
        ELSE 'Night/Early Morning'
    END as time_period,
    CASE 
        WHEN tth.day_of_week IN ('Saturday', 'Sunday') THEN 'Weekend'
        ELSE 'Weekday'
    END as day_type
FROM topic_trends_hourly tth
WHERE tth.hour_key >= DATEADD(DAY, -7, GETDATE()); -- Last 7 days for performance

-- =====================================================
-- 3. ARTICLE ANALYSIS VIEWS
-- =====================================================

-- Recent articles with enriched metadata
CREATE OR ALTER VIEW vw_recent_articles AS
SELECT 
    a.article_id,
    a.title,
    a.description,
    a.source_name,
    a.author,
    a.published_at,
    a.url,
    t.topic_id,
    t.topic_label,
    at.confidence as topic_confidence,
    CASE 
        WHEN at.confidence >= 0.8 THEN 'Very High'
        WHEN at.confidence >= 0.6 THEN 'High'
        WHEN at.confidence >= 0.4 THEN 'Medium'
        WHEN at.confidence >= 0.2 THEN 'Low'
        ELSE 'Very Low'
    END as confidence_category,
    LEN(a.title) as title_length,
    CASE WHEN a.description IS NOT NULL THEN 1 ELSE 0 END as has_description,
    DATEDIFF(HOUR, a.published_at, GETDATE()) as hours_since_published,
    at.classified_at,
    ROW_NUMBER() OVER (ORDER BY a.published_at DESC) as recency_rank
FROM articles a
INNER JOIN article_topics at ON a.article_id = at.article_id
INNER JOIN topics t ON at.topic_id = t.topic_id
WHERE a.published_at >= DATEADD(DAY, -3, GETDATE()) -- Last 3 days
AND at.confidence >= 0.3; -- Only classified articles

-- =====================================================
-- 4. SOURCE ANALYSIS VIEWS
-- =====================================================

-- Source performance metrics
CREATE OR ALTER VIEW vw_source_performance AS
SELECT 
    sa.source_name,
    sa.topic_id,
    sa.topic_label,
    sa.article_count,
    sa.avg_confidence,
    sa.total_articles_from_source,
    sa.topics_covered_by_source,
    sa.percentage_within_source,
    sa.last_article_date,
    DATEDIFF(HOUR, sa.last_article_date, GETDATE()) as hours_since_last_article,
    CASE 
        WHEN sa.avg_confidence >= 0.7 THEN 'High Quality'
        WHEN sa.avg_confidence >= 0.5 THEN 'Medium Quality'
        ELSE 'Low Quality'
    END as source_quality_tier,
    CASE 
        WHEN sa.topics_covered_by_source >= 5 THEN 'High Diversity'
        WHEN sa.topics_covered_by_source >= 3 THEN 'Medium Diversity'
        ELSE 'Low Diversity'
    END as topic_diversity_tier
FROM source_analysis sa
WHERE sa.updated_at >= DATEADD(DAY, -1, GETDATE()); -- Updated in last day

-- Top sources summary
CREATE OR ALTER VIEW vw_top_sources AS
SELECT 
    source_name,
    SUM(article_count) as total_articles,
    AVG(avg_confidence) as overall_avg_confidence,
    MAX(topics_covered_by_source) as topics_covered,
    MAX(last_article_date) as latest_article,
    COUNT(DISTINCT topic_id) as topics_with_articles,
    RANK() OVER (ORDER BY SUM(article_count) DESC) as volume_rank,
    RANK() OVER (ORDER BY AVG(avg_confidence) DESC) as quality_rank
FROM source_analysis
GROUP BY source_name;

-- =====================================================
-- 5. MODEL PERFORMANCE VIEWS
-- =====================================================

-- Confidence distribution analysis
CREATE OR ALTER VIEW vw_confidence_distribution AS
SELECT 
    ca.confidence_bin,
    ca.topic_id,
    ca.topic_label,
    ca.article_count,
    ca.avg_confidence,
    ca.confidence_std,
    ca.bin_min,
    ca.bin_max,
    CAST(ca.article_count * 100.0 / SUM(ca.article_count) OVER (PARTITION BY ca.topic_id) AS DECIMAL(5,2)) as percentage_within_topic,
    CAST(ca.article_count * 100.0 / SUM(ca.article_count) OVER () AS DECIMAL(5,2)) as percentage_overall
FROM confidence_analysis ca;

-- Model performance metrics
CREATE OR ALTER VIEW vw_model_performance AS
SELECT 
    CAST(GETDATE() AS DATE) as metric_date,
    COUNT(DISTINCT a.article_id) as total_articles_today,
    COUNT(DISTINCT CASE WHEN at.confidence >= 0.3 THEN a.article_id END) as classified_articles_today,
    CAST(COUNT(DISTINCT CASE WHEN at.confidence >= 0.3 THEN a.article_id END) * 100.0 / 
         COUNT(DISTINCT a.article_id) AS DECIMAL(5,2)) as classification_rate_today,
    AVG(at.confidence) as avg_confidence_today,
    COUNT(DISTINCT CASE WHEN at.confidence >= 0.7 THEN a.article_id END) as high_confidence_today,
    COUNT(DISTINCT CASE WHEN at.confidence >= 0.5 AND at.confidence < 0.7 THEN a.article_id END) as medium_confidence_today,
    COUNT(DISTINCT CASE WHEN at.confidence < 0.5 AND at.confidence >= 0.3 THEN a.article_id END) as low_confidence_today,
    STDEV(at.confidence) as confidence_std_today
FROM articles a
LEFT JOIN article_topics at ON a.article_id = at.article_id
WHERE CAST(a.published_at AS DATE) = CAST(GETDATE() AS DATE);

-- =====================================================
-- 6. REAL-TIME MONITORING VIEWS
-- =====================================================

-- Pipeline health dashboard
CREATE OR ALTER VIEW vw_pipeline_health AS
SELECT 
    pr.run_id,
    pr.start_time,
    pr.end_time,
    pr.status,
    pr.articles_collected,
    pr.articles_processed,
    pr.articles_classified,
    pr.duration_seconds,
    pr.error_message,
    CASE 
        WHEN pr.status = 'SUCCESS' AND pr.duration_seconds <= 300 THEN 'Healthy'
        WHEN pr.status = 'SUCCESS' AND pr.duration_seconds > 300 THEN 'Slow'
        WHEN pr.status = 'FAILED' THEN 'Failed'
        WHEN pr.status = 'RUNNING' AND DATEDIFF(MINUTE, pr.start_time, GETDATE()) > 30 THEN 'Stuck'
        ELSE 'Running'
    END as health_status,
    ROW_NUMBER() OVER (ORDER BY pr.start_time DESC) as run_sequence
FROM pipeline_runs pr
WHERE pr.start_time >= DATEADD(DAY, -7, GETDATE()); -- Last 7 days

-- Data freshness indicators
CREATE OR ALTER VIEW vw_data_freshness AS
SELECT 
    'Articles' as data_type,
    MAX(published_at) as latest_timestamp,
    COUNT(*) as total_records,
    DATEDIFF(MINUTE, MAX(published_at), GETDATE()) as minutes_since_latest
FROM articles
WHERE published_at >= DATEADD(DAY, -1, GETDATE())

UNION ALL

SELECT 
    'Classifications' as data_type,
    MAX(classified_at) as latest_timestamp,
    COUNT(*) as total_records,
    DATEDIFF(MINUTE, MAX(classified_at), GETDATE()) as minutes_since_latest
FROM article_topics
WHERE classified_at >= DATEADD(DAY, -1, GETDATE())

UNION ALL

SELECT 
    'Daily Trends' as data_type,
    MAX(updated_at) as latest_timestamp,
    COUNT(*) as total_records,
    DATEDIFF(MINUTE, MAX(updated_at), GETDATE()) as minutes_since_latest
FROM topic_trends_daily
WHERE updated_at >= DATEADD(DAY, -1, GETDATE());

-- =====================================================
-- 7. PERFORMANCE OPTIMIZATION HINTS
-- =====================================================

-- Create additional indexes for view performance
CREATE INDEX IX_articles_published_date ON articles(CAST(published_at AS DATE));
CREATE INDEX IX_article_topics_confidence_range ON article_topics(confidence) WHERE confidence >= 0.3;
CREATE INDEX IX_daily_trends_recent ON topic_trends_daily(date_key, topic_id) WHERE date_key >= DATEADD(DAY, -90, GETDATE());
CREATE INDEX IX_hourly_trends_recent ON topic_trends_hourly(hour_key, topic_id) WHERE hour_key >= DATEADD(DAY, -7, GETDATE());
