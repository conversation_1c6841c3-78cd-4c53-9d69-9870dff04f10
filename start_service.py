#!/usr/bin/env python3
"""
Real-Time Topic Modeling Service
Main entry point for the hourly news collection and classification system.
"""

import sys
from pathlib import Path

# Add modules to path
sys.path.insert(0, str(Path(__file__).parent / 'core'))
sys.path.insert(0, str(Path(__file__).parent / 'backend'))

from realtime_service import TopicModelingBackend

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Real-Time Topic Modeling Service")
    parser.add_argument('action', choices=['start', 'stop', 'status', 'train'], 
                       help='Service action')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    
    args = parser.parse_args()
    
    if args.action == 'train':
        # Train initial model
        from models.model_trainer import TopicModeler
        from processors.text_processor import TextPreprocessor
        from config_manager import config
        from utils import load_articles, save_dataframe

        print("🧠 Training initial LDA model...")

        # Create sample data for training if none exists
        data_dir = Path(config.get('output.data_dir', 'data'))
        sample_file = data_dir / 'sample_articles.csv'

        if not sample_file.exists():
            print("📰 No training data found. Please run data collection first.")
            print("💡 Use: python core/collectors/hourly_news.py")
            return

        # Load and train
        df = load_articles(str(sample_file))
        if df.empty:
            print("❌ No articles found for training")
            return

        # Preprocess
        preprocessor = TextPreprocessor()
        processed_df = preprocessor.preprocess_dataframe(df)

        # Train model
        modeler = TopicModeler(method='sklearn')
        documents = processed_df['cleaned_text'].tolist()
        modeler.fit(documents)

        # Save model
        model_path = modeler.save_model()
        print(f"✅ Model trained and saved: {model_path}")

        # Create latest model link
        latest_path = Path('models/latest_model.pkl')
        if latest_path.exists():
            latest_path.unlink()
        latest_path.symlink_to(Path(model_path).resolve())
        print(f"✅ Latest model link created: {latest_path}")
    else:
        # Handle service commands
        backend = TopicModelingBackend()
        
        if args.action == 'start':
            backend.start_backend_service()
            if not args.daemon:
                try:
                    input("Press Enter to stop service...")
                except KeyboardInterrupt:
                    pass
                backend.stop_backend_service()
        elif args.action == 'stop':
            backend.stop_backend_service()
        elif args.action == 'status':
            status = backend.get_service_status()
            print(status)

if __name__ == "__main__":
    main()
