# =====================================================
# Environment Variables for Topic Modeling Pipeline
# Copy this file to .env and fill in your actual values
# =====================================================

# =====================================================
# NEWS API CONFIGURATION
# =====================================================
# Get your free API key from: https://newsapi.org/register
NEWS_API_KEY=your_newsapi_key_here

# =====================================================
# MYSQL DATABASE CONFIGURATION
# =====================================================
# MySQL connection details (alternative to config/database.json)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=TopicModelingDB
MYSQL_USERNAME=your_mysql_username
MYSQL_PASSWORD=your_mysql_password

# =====================================================
# PIPELINE CONFIGURATION
# =====================================================
# Data collection settings
FETCH_INTERVAL_MINUTES=60
MAX_ARTICLES_PER_FETCH=100

# Model settings
MODEL_PATH=models/lda_model.pkl
CONFIDENCE_THRESHOLD=0.3

# =====================================================
# LOGGING CONFIGURATION
# =====================================================
LOG_LEVEL=INFO
LOG_FILE=logs/realtime_service.log

# =====================================================
# POWER BI INTEGRATION
# =====================================================
POWERBI_EXPORT_ENABLED=true
POWERBI_DATA_DIR=data/dashboard
