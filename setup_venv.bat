@echo off
REM =====================================================
REM Virtual Environment Setup for Topic Modeling Pipeline
REM Run this script to create and configure your virtual environment
REM =====================================================

echo.
echo ========================================
echo  Topic Modeling Pipeline Setup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo ✅ Python found:
python --version

REM Create virtual environment
echo.
echo 📦 Creating virtual environment...
if exist topic_modeling_env (
    echo ⚠️  Virtual environment already exists
    set /p choice="Do you want to recreate it? (y/n): "
    if /i "%choice%"=="y" (
        echo 🗑️  Removing existing environment...
        rmdir /s /q topic_modeling_env
    ) else (
        echo 📂 Using existing environment
        goto activate
    )
)

python -m venv topic_modeling_env
if errorlevel 1 (
    echo ❌ Failed to create virtual environment
    pause
    exit /b 1
)
echo ✅ Virtual environment created

:activate
REM Activate virtual environment
echo.
echo 🔄 Activating virtual environment...
call topic_modeling_env\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

REM Upgrade pip
echo.
echo ⬆️  Upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo.
echo 📥 Installing dependencies from requirements.txt...
echo This may take a few minutes...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install some dependencies
    echo Check the error messages above
    pause
    exit /b 1
)

REM Test installation
echo.
echo 🧪 Testing installation...
python -c "import pandas, numpy, sklearn, pymysql, sqlalchemy; print('✅ Core packages imported successfully')"
if errorlevel 1 (
    echo ❌ Package import test failed
    pause
    exit /b 1
)

REM Test database connection (if config exists)
echo.
echo 🔗 Testing database connection...
if exist config\database.json (
    cd database
    python test_connection.py
    cd ..
) else (
    echo ⚠️  Database config not found (config\database.json)
    echo Please configure your MySQL connection before running the pipeline
)

echo.
echo ========================================
echo  🎉 Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Configure MySQL: Edit config\database.json
echo 2. Set NewsAPI key: set NEWS_API_KEY=your_key
echo 3. Add LDA model: Place .pkl file in models\ folder
echo 4. Test pipeline: python automated_pipeline.py --single-run
echo.
echo To activate environment manually:
echo   topic_modeling_env\Scripts\activate
echo.
echo To deactivate:
echo   deactivate
echo.
pause
