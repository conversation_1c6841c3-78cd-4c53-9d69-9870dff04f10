"""
Tests for configuration management.
"""

import pytest
import tempfile
import yaml
from pathlib import Path
import sys

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from src.config import Config

class TestConfig:
    """Test configuration loading and validation."""
    
    def test_config_loading(self):
        """Test basic configuration loading."""
        # Create a temporary config file
        config_data = {
            'news_api': {
                'api_key': 'test_key',
                'base_url': 'https://test.com',
                'query_keywords': ['test']
            },
            'preprocessing': {
                'min_word_length': 3,
                'remove_stopwords': True
            },
            'topic_modeling': {
                'num_topics': 5,
                'random_state': 42
            },
            'output': {
                'data_dir': 'test_data'
            },
            'logging': {
                'level': 'INFO'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_config_path = f.name
        
        try:
            config = Config(temp_config_path)
            
            # Test basic access
            assert config.get('news_api.api_key') == 'test_key'
            assert config.get('preprocessing.min_word_length') == 3
            assert config.get('topic_modeling.num_topics') == 5
            
            # Test section access
            news_api_config = config.news_api
            assert news_api_config['api_key'] == 'test_key'
            assert news_api_config['query_keywords'] == ['test']
            
        finally:
            Path(temp_config_path).unlink()
    
    def test_config_defaults(self):
        """Test default values for missing keys."""
        config_data = {
            'news_api': {
                'api_key': 'test_key'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_config_path = f.name
        
        try:
            config = Config(temp_config_path)
            
            # Test default values
            assert config.get('nonexistent.key', 'default') == 'default'
            assert config.get('news_api.missing_key', 'default') == 'default'
            
        finally:
            Path(temp_config_path).unlink()
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Test missing required sections
        incomplete_config = {
            'news_api': {
                'api_key': 'test_key'
            }
            # Missing other required sections
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(incomplete_config, f)
            temp_config_path = f.name
        
        try:
            with pytest.raises(ValueError):
                Config(temp_config_path)
                
        finally:
            Path(temp_config_path).unlink()
    
    def test_missing_config_file(self):
        """Test handling of missing configuration file."""
        with pytest.raises(FileNotFoundError):
            Config('nonexistent_config.yaml')
