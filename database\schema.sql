-- =====================================================
-- Essential MySQL Schema for Topic Modeling Pipeline
-- Optimized for your exact workflow: NewsAPI → raw_articles → preprocessed_articles → topic_results
-- =====================================================

CREATE DATABASE IF NOT EXISTS TopicModelingDB;
USE TopicModelingDB;

-- =====================================================
-- CORE WORKFLOW TABLES
-- =====================================================

-- 1. Raw articles (stores NewsAPI JSON responses)
CREATE TABLE raw_articles (
    article_id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    content LONGTEXT,
    source_name VARCHAR(100) NOT NULL,
    author VARCHAR(200),
    url VARCHAR(1000) UNIQUE,
    published_at DATETIME NOT NULL,
    fetched_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    content_hash VARCHAR(64) UNIQUE,
    raw_json LONGTEXT,
    processing_status VARCHAR(20) DEFAULT 'pending',
    
    INDEX IX_published_timestamp (published_at DESC, processing_status),
    INDEX IX_fetched_at (fetched_at DESC),
    INDEX IX_content_hash (content_hash)
);

-- 2. Preprocessed articles (stores cleaned text)
CREATE TABLE preprocessed_articles (
    preprocessed_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id VARCHAR(50) NOT NULL,
    cleaned_text LONGTEXT NOT NULL,
    tokens LONGTEXT,
    lemmatized_text LONGTEXT,
    word_count INT,
    preprocessing_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    preprocessing_duration_ms INT,
    
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    UNIQUE KEY UQ_article_preprocessing (article_id),
    INDEX IX_preprocessing_timestamp (preprocessing_timestamp DESC)
);

-- 3. Topics master table
CREATE TABLE topics (
    topic_id INT PRIMARY KEY,
    topic_label VARCHAR(100) NOT NULL,
    topic_keywords LONGTEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 4. Topic results (stores LDA model output)
CREATE TABLE topic_results (
    result_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    article_id VARCHAR(50) NOT NULL,
    preprocessed_id BIGINT NOT NULL,
    topic_id INT NOT NULL,
    confidence DECIMAL(8,6) NOT NULL,
    topic_distribution LONGTEXT,
    topic_keywords LONGTEXT,
    classification_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    model_version VARCHAR(20) DEFAULT '1.0',
    processing_duration_ms INT,
    
    FOREIGN KEY (article_id) REFERENCES raw_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (preprocessed_id) REFERENCES preprocessed_articles(preprocessed_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    UNIQUE KEY UQ_preprocessed_classification (preprocessed_id),
    INDEX IX_classification_timestamp (classification_timestamp DESC, topic_id, confidence DESC)
);

-- =====================================================
-- SAMPLE TOPICS DATA
-- =====================================================

INSERT INTO topics (topic_id, topic_label, topic_keywords) VALUES
(0, 'Technology', '["technology", "tech", "innovation", "digital", "software", "ai"]'),
(1, 'Politics', '["politics", "government", "election", "policy", "political"]'),
(2, 'Business', '["business", "economy", "market", "financial", "company"]'),
(3, 'Health', '["health", "medical", "healthcare", "medicine", "hospital"]'),
(4, 'Science', '["science", "research", "study", "scientific", "discovery"]'),
(5, 'Sports', '["sports", "game", "team", "player", "championship"]'),
(6, 'Entertainment', '["entertainment", "movie", "music", "celebrity", "film"]'),
(7, 'Environment', '["environment", "climate", "green", "sustainability"]');

-- =====================================================
-- VERIFICATION
-- =====================================================

SHOW TABLES;
SELECT COUNT(*) as topic_count FROM topics;
SELECT 'Schema created successfully' as status;
