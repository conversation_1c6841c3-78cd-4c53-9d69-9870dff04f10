-- =====================================================
-- Microsoft SQL Server Schema for Real-Time Topic Modeling
-- Optimized for Power BI DirectQuery Performance
-- =====================================================

-- Create database (run separately if needed)
-- CREATE DATABASE TopicModelingDB;
-- USE TopicModelingDB;

-- =====================================================
-- 1. CORE TABLES
-- =====================================================

-- Articles master table
CREATE TABLE articles (
    article_id NVARCHAR(50) PRIMARY KEY,
    title NVARCHAR(500) NOT NULL,
    description NVARCHAR(MAX),
    content NVARCHAR(MAX),
    full_text NVARCHAR(MAX),
    source_name NVARCHAR(100) NOT NULL,
    source_id NVARCHAR(50),
    author NVARCHAR(200),
    url NVARCHAR(1000) UNIQUE,
    url_to_image NVARCHAR(1000),
    published_at DATETIME2 NOT NULL,
    collected_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    content_hash NVARCHAR(64) UNIQUE,
    language NVARCHAR(10) DEFAULT 'en',
    country NVARCHAR(10),
    category NVARCHAR(50),
    
    -- Indexing for performance
    INDEX IX_articles_published_at (published_at DESC),
    INDEX IX_articles_source_name (source_name),
    INDEX IX_articles_collected_at (collected_at DESC),
    INDEX IX_articles_content_hash (content_hash)
);

-- Topics master table
CREATE TABLE topics (
    topic_id INT PRIMARY KEY,
    topic_label NVARCHAR(100) NOT NULL,
    topic_keywords NVARCHAR(MAX), -- JSON array of keywords
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    INDEX IX_topics_label (topic_label)
);

-- Article topic classifications
CREATE TABLE article_topics (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    article_id NVARCHAR(50) NOT NULL,
    topic_id INT NOT NULL,
    confidence DECIMAL(5,4) NOT NULL, -- 0.0000 to 1.0000
    all_probabilities NVARCHAR(MAX), -- JSON array of all topic probabilities
    classified_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    model_version NVARCHAR(20) DEFAULT '1.0',
    
    -- Foreign keys
    FOREIGN KEY (article_id) REFERENCES articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint - one classification per article
    UNIQUE (article_id),
    
    -- Indexing for performance
    INDEX IX_article_topics_topic_id (topic_id),
    INDEX IX_article_topics_confidence (confidence DESC),
    INDEX IX_article_topics_classified_at (classified_at DESC)
);

-- =====================================================
-- 2. AGGREGATED TABLES FOR PERFORMANCE
-- =====================================================

-- Daily topic trends (pre-aggregated for performance)
CREATE TABLE topic_trends_daily (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    date_key DATE NOT NULL,
    topic_id INT NOT NULL,
    topic_label NVARCHAR(100) NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(5,4) NOT NULL,
    confidence_std DECIMAL(5,4) DEFAULT 0,
    min_confidence DECIMAL(5,4) NOT NULL,
    max_confidence DECIMAL(5,4) NOT NULL,
    percentage_of_day DECIMAL(5,2) DEFAULT 0,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Foreign key
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint
    UNIQUE (date_key, topic_id),
    
    -- Indexing for DirectQuery performance
    INDEX IX_daily_trends_date_topic (date_key DESC, topic_id),
    INDEX IX_daily_trends_article_count (article_count DESC),
    INDEX IX_daily_trends_updated_at (updated_at DESC)
);

-- Hourly topic trends (pre-aggregated for performance)
CREATE TABLE topic_trends_hourly (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    hour_key DATETIME2 NOT NULL, -- Truncated to hour
    topic_id INT NOT NULL,
    topic_label NVARCHAR(100) NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(5,4) NOT NULL,
    confidence_std DECIMAL(5,4) DEFAULT 0,
    hour_of_day INT NOT NULL, -- 0-23
    day_of_week NVARCHAR(10) NOT NULL, -- Monday, Tuesday, etc.
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Foreign key
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint
    UNIQUE (hour_key, topic_id),
    
    -- Indexing for DirectQuery performance
    INDEX IX_hourly_trends_hour_topic (hour_key DESC, topic_id),
    INDEX IX_hourly_trends_hour_of_day (hour_of_day),
    INDEX IX_hourly_trends_day_of_week (day_of_week),
    INDEX IX_hourly_trends_updated_at (updated_at DESC)
);

-- Source analysis (pre-aggregated)
CREATE TABLE source_analysis (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    source_name NVARCHAR(100) NOT NULL,
    topic_id INT NOT NULL,
    topic_label NVARCHAR(100) NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(5,4) NOT NULL,
    total_articles_from_source INT NOT NULL DEFAULT 0,
    topics_covered_by_source INT NOT NULL DEFAULT 0,
    percentage_within_source DECIMAL(5,2) DEFAULT 0,
    last_article_date DATETIME2,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Foreign key
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint
    UNIQUE (source_name, topic_id),
    
    -- Indexing
    INDEX IX_source_analysis_source (source_name),
    INDEX IX_source_analysis_topic (topic_id),
    INDEX IX_source_analysis_article_count (article_count DESC),
    INDEX IX_source_analysis_updated_at (updated_at DESC)
);

-- Confidence analysis (pre-aggregated)
CREATE TABLE confidence_analysis (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    confidence_bin NVARCHAR(20) NOT NULL, -- '0.0-0.3', '0.3-0.5', etc.
    topic_id INT NOT NULL,
    topic_label NVARCHAR(100) NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(5,4) NOT NULL,
    confidence_std DECIMAL(5,4) DEFAULT 0,
    bin_min DECIMAL(5,4) NOT NULL,
    bin_max DECIMAL(5,4) NOT NULL,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Foreign key
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id),
    
    -- Unique constraint
    UNIQUE (confidence_bin, topic_id),
    
    -- Indexing
    INDEX IX_confidence_analysis_bin (confidence_bin),
    INDEX IX_confidence_analysis_topic (topic_id),
    INDEX IX_confidence_analysis_updated_at (updated_at DESC)
);

-- =====================================================
-- 3. PIPELINE MONITORING TABLES
-- =====================================================

-- Pipeline execution log
CREATE TABLE pipeline_runs (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    run_id NVARCHAR(50) UNIQUE NOT NULL,
    start_time DATETIME2 NOT NULL,
    end_time DATETIME2,
    status NVARCHAR(20) NOT NULL, -- 'RUNNING', 'SUCCESS', 'FAILED'
    articles_collected INT DEFAULT 0,
    articles_processed INT DEFAULT 0,
    articles_classified INT DEFAULT 0,
    error_message NVARCHAR(MAX),
    duration_seconds INT,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    INDEX IX_pipeline_runs_start_time (start_time DESC),
    INDEX IX_pipeline_runs_status (status),
    INDEX IX_pipeline_runs_run_id (run_id)
);

-- Data quality metrics
CREATE TABLE data_quality_metrics (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    metric_date DATE NOT NULL,
    total_articles INT NOT NULL DEFAULT 0,
    classified_articles INT NOT NULL DEFAULT 0,
    classification_rate DECIMAL(5,4) NOT NULL,
    avg_confidence DECIMAL(5,4) NOT NULL,
    high_confidence_articles INT NOT NULL DEFAULT 0, -- confidence > 0.7
    duplicate_articles INT NOT NULL DEFAULT 0,
    failed_classifications INT NOT NULL DEFAULT 0,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    UNIQUE (metric_date),
    INDEX IX_quality_metrics_date (metric_date DESC)
);

-- =====================================================
-- 4. STORED PROCEDURES FOR UPSERTS
-- =====================================================

-- Upsert daily trends
CREATE OR ALTER PROCEDURE usp_UpsertDailyTrends
    @date_key DATE,
    @topic_id INT,
    @topic_label NVARCHAR(100),
    @article_count INT,
    @avg_confidence DECIMAL(5,4),
    @confidence_std DECIMAL(5,4),
    @min_confidence DECIMAL(5,4),
    @max_confidence DECIMAL(5,4),
    @percentage_of_day DECIMAL(5,2)
AS
BEGIN
    MERGE topic_trends_daily AS target
    USING (SELECT @date_key as date_key, @topic_id as topic_id) AS source
    ON target.date_key = source.date_key AND target.topic_id = source.topic_id
    WHEN MATCHED THEN
        UPDATE SET 
            topic_label = @topic_label,
            article_count = @article_count,
            avg_confidence = @avg_confidence,
            confidence_std = @confidence_std,
            min_confidence = @min_confidence,
            max_confidence = @max_confidence,
            percentage_of_day = @percentage_of_day,
            updated_at = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (date_key, topic_id, topic_label, article_count, avg_confidence, 
                confidence_std, min_confidence, max_confidence, percentage_of_day)
        VALUES (@date_key, @topic_id, @topic_label, @article_count, @avg_confidence,
                @confidence_std, @min_confidence, @max_confidence, @percentage_of_day);
END;

-- Upsert hourly trends
CREATE OR ALTER PROCEDURE usp_UpsertHourlyTrends
    @hour_key DATETIME2,
    @topic_id INT,
    @topic_label NVARCHAR(100),
    @article_count INT,
    @avg_confidence DECIMAL(5,4),
    @confidence_std DECIMAL(5,4),
    @hour_of_day INT,
    @day_of_week NVARCHAR(10)
AS
BEGIN
    MERGE topic_trends_hourly AS target
    USING (SELECT @hour_key as hour_key, @topic_id as topic_id) AS source
    ON target.hour_key = source.hour_key AND target.topic_id = source.topic_id
    WHEN MATCHED THEN
        UPDATE SET 
            topic_label = @topic_label,
            article_count = @article_count,
            avg_confidence = @avg_confidence,
            confidence_std = @confidence_std,
            hour_of_day = @hour_of_day,
            day_of_week = @day_of_week,
            updated_at = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (hour_key, topic_id, topic_label, article_count, avg_confidence,
                confidence_std, hour_of_day, day_of_week)
        VALUES (@hour_key, @topic_id, @topic_label, @article_count, @avg_confidence,
                @confidence_std, @hour_of_day, @day_of_week);
END;

-- =====================================================
-- 5. INITIAL DATA SETUP
-- =====================================================

-- Insert default topics (will be updated by pipeline)
INSERT INTO topics (topic_id, topic_label, topic_keywords) VALUES
(0, 'Technology', '["technology", "ai", "artificial", "intelligence", "software"]'),
(1, 'Politics', '["politics", "election", "government", "policy", "democracy"]'),
(2, 'Business', '["business", "economy", "market", "finance", "company"]'),
(3, 'Health', '["health", "medical", "healthcare", "medicine", "treatment"]'),
(4, 'Sports', '["sports", "football", "basketball", "soccer", "game"]'),
(5, 'Entertainment', '["entertainment", "movie", "music", "celebrity", "film"]'),
(6, 'Science', '["science", "research", "study", "discovery", "scientific"]'),
(7, 'Environment', '["environment", "climate", "energy", "green", "sustainability"]');

-- Create indexes for better performance
CREATE INDEX IX_articles_published_source ON articles(published_at DESC, source_name);
CREATE INDEX IX_article_topics_composite ON article_topics(topic_id, confidence DESC, classified_at DESC);

-- =====================================================
-- 6. CLEANUP AND MAINTENANCE PROCEDURES
-- =====================================================

-- Procedure to clean old data (optional - for data retention)
CREATE OR ALTER PROCEDURE usp_CleanOldData
    @days_to_keep INT = 90
AS
BEGIN
    DECLARE @cutoff_date DATETIME2 = DATEADD(DAY, -@days_to_keep, GETDATE());

    -- Clean old articles and related data
    DELETE FROM article_topics WHERE article_id IN (
        SELECT article_id FROM articles WHERE published_at < @cutoff_date
    );

    DELETE FROM articles WHERE published_at < @cutoff_date;

    -- Clean old pipeline runs
    DELETE FROM pipeline_runs WHERE start_time < @cutoff_date;

    -- Clean old daily trends (keep more history for trends)
    DELETE FROM topic_trends_daily WHERE date_key < DATEADD(DAY, -365, GETDATE());

    -- Clean old hourly trends (keep less history due to volume)
    DELETE FROM topic_trends_hourly WHERE hour_key < DATEADD(DAY, -30, GETDATE());
END;
