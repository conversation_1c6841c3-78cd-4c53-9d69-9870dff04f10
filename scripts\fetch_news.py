#!/usr/bin/env python3
"""
Complete Data Ingestion Module
Fetches news articles from NewsAPI with deduplication and persistent storage
"""

import requests
import json
import pandas as pd
import sqlite3
import os
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TimestampBasedNewsIngestion:
    """
    Timestamp-based news ingestion with SQL Server integration.
    Fetches only new articles based on last fetch timestamp.
    """

    def __init__(self,
                 api_key: str,
                 sql_connection_manager=None,
                 fetch_interval_minutes: int = 15):
        """
        Initialize timestamp-based news ingestion.

        Args:
            api_key: NewsAPI key
            sql_connection_manager: SQL Server connection manager
            fetch_interval_minutes: How often to fetch news (default: 15 minutes)
        """
        self.api_key = api_key
        self.sql_manager = sql_connection_manager
        self.fetch_interval_minutes = fetch_interval_minutes

        # NewsAPI configuration
        self.base_url = "https://newsapi.org/v2/everything"
        self.sources_url = "https://newsapi.org/v2/sources"

        # Timestamp tracking
        self.last_fetch_timestamp = None

    def get_last_fetch_timestamp(self) -> Optional[datetime]:
        """
        Get the timestamp of the last successful fetch from SQL Server.

        Returns:
            Last fetch timestamp or None if no previous fetches
        """
        if not self.sql_manager:
            logger.warning("No SQL connection manager provided")
            return datetime.now() - timedelta(minutes=self.fetch_interval_minutes)

        try:
            with self.sql_manager.get_connection() as conn:
                query = """
                SELECT MAX(fetched_at) as last_fetch
                FROM raw_articles
                WHERE processing_status IN ('processed', 'pending')
                """
                result = conn.execute(query).fetchone()

                if result and result.last_fetch:
                    self.last_fetch_timestamp = result.last_fetch
                    logger.info(f"📅 Last fetch timestamp: {self.last_fetch_timestamp}")
                    return self.last_fetch_timestamp
                else:
                    # First time running - fetch from last interval
                    default_timestamp = datetime.now() - timedelta(minutes=self.fetch_interval_minutes)
                    logger.info(f"📅 No previous fetches found, using default: {default_timestamp}")
                    return default_timestamp

        except Exception as e:
            logger.error(f"❌ Failed to get last fetch timestamp: {e}")
            # Fallback to interval-based timestamp
            return datetime.now() - timedelta(minutes=self.fetch_interval_minutes)

    def fetch_latest_articles(self,
                            query: str = None,
                            sources: List[str] = None,
                            language: str = 'en',
                            page_size: int = 100) -> List[Dict]:
        """
        Fetch only the latest articles since last fetch timestamp.

        Args:
            query: Search query keywords
            sources: List of source IDs
            language: Language code
            page_size: Articles per page (max 100)

        Returns:
            List of new articles with raw JSON
        """

        # Get timestamp for filtering
        from_timestamp = self.get_last_fetch_timestamp()
        to_timestamp = datetime.now()

        logger.info(f"🔍 Fetching articles from {from_timestamp} to {to_timestamp}")

        # Default query if none provided
        if not query and not sources:
            query = "technology OR politics OR business OR economy OR health"

        # Build API parameters for timestamp-based filtering
        params = {
            'apiKey': self.api_key,
            'language': language,
            'sortBy': 'publishedAt',
            'from': from_timestamp.isoformat(),
            'to': to_timestamp.isoformat(),
            'pageSize': min(page_size, 100),
            'page': 1
        }

        # Add optional parameters
        if query:
            params['q'] = query
        if sources:
            params['sources'] = ','.join(sources)

        all_articles = []
        current_page = 1
        max_pages = 5  # Limit to prevent excessive API calls

        try:
            while current_page <= max_pages:
                params['page'] = current_page

                logger.info(f"📡 Fetching page {current_page} from NewsAPI...")

                response = requests.get(self.base_url, params=params, timeout=30)

                if response.status_code == 200:
                    data = response.json()
                    articles = data.get('articles', [])

                    if not articles:
                        logger.info(f"📄 No more articles on page {current_page}")
                        break

                    # Process and clean articles
                    for article in articles:
                        cleaned_article = self._clean_article_data(article)
                        if cleaned_article:
                            all_articles.append(cleaned_article)

                    logger.info(f"📄 Fetched {len(articles)} articles from page {current_page}")

                    # Check if we have more pages
                    total_results = data.get('totalResults', 0)
                    if len(all_articles) >= total_results or len(articles) < page_size:
                        break

                    current_page += 1

                elif response.status_code == 429:
                    logger.warning("⚠️ Rate limit reached, stopping fetch")
                    break

                else:
                    logger.error(f"❌ API request failed: {response.status_code} - {response.text}")
                    break

        except Exception as e:
            logger.error(f"❌ Failed to fetch articles: {e}")

        logger.info(f"✅ Total articles fetched: {len(all_articles)}")
        return all_articles

    def _clean_article_data(self, article: Dict) -> Optional[Dict]:
        """
        Clean and structure article data with raw JSON preservation.

        Args:
            article: Raw article data from NewsAPI

        Returns:
            Cleaned article dictionary with raw JSON
        """

        # Skip articles without essential content
        if not article.get('title') or not article.get('content'):
            return None

        # Skip articles that are too short
        content = article.get('content', '')
        if len(content.strip()) < 100:
            return None

        # Extract source information
        source = article.get('source', {})
        source_name = source.get('name', 'Unknown') if isinstance(source, dict) else str(source)
        source_id = source.get('id', '') if isinstance(source, dict) else ''

        # Generate unique article ID
        article_id = self._generate_article_id(article)

        # Generate content hash for deduplication
        content_hash = self._generate_content_hash(article)

        cleaned = {
            'article_id': article_id,
            'title': article.get('title', '').strip(),
            'description': article.get('description', '').strip(),
            'content': content.strip(),
            'source_name': source_name,
            'source_id': source_id,
            'author': article.get('author', ''),
            'url': article.get('url', ''),
            'url_to_image': article.get('urlToImage', ''),
            'published_at': article.get('publishedAt', ''),
            'fetched_at': datetime.now().isoformat(),
            'content_hash': content_hash,
            'language': 'en',  # Default to English
            'raw_json': json.dumps(article),  # Store original JSON
            'processing_status': 'pending'
        }

        return cleaned

    def _generate_article_id(self, article: Dict) -> str:
        """Generate unique article ID."""

        # Use URL + published_at for ID generation
        url = article.get('url', '')
        published_at = article.get('publishedAt', '')

        id_string = f"{url}_{published_at}"
        return hashlib.md5(id_string.encode()).hexdigest()[:16]

    def _generate_content_hash(self, article: Dict) -> str:
        """Generate content hash for deduplication."""

        # Combine title and content for hash
        title = article.get('title', '')
        content = article.get('content', '')

        hash_string = f"{title}_{content}"
        return hashlib.sha256(hash_string.encode()).hexdigest()

    def store_raw_articles(self, articles: List[Dict]) -> int:
        """
        Store raw articles in SQL Server with deduplication.

        Args:
            articles: List of cleaned article dictionaries

        Returns:
            Number of new articles stored
        """

        if not self.sql_manager or not articles:
            logger.warning("No SQL connection or no articles to store")
            return 0

        new_articles_count = 0

        try:
            with self.sql_manager.get_connection() as conn:
                for article in articles:
                    try:
                        # Check if article already exists
                        check_query = """
                        SELECT COUNT(*) as count
                        FROM raw_articles
                        WHERE content_hash = ? OR article_id = ?
                        """

                        result = conn.execute(check_query,
                                            (article['content_hash'], article['article_id'])).fetchone()

                        if result.count > 0:
                            logger.debug(f"📄 Article already exists: {article['title'][:50]}...")
                            continue

                        # Insert new article
                        insert_query = """
                        INSERT INTO raw_articles (
                            article_id, title, description, content, source_name, source_id,
                            author, url, url_to_image, published_at, fetched_at, content_hash,
                            language, raw_json, processing_status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """

                        conn.execute(insert_query, (
                            article['article_id'],
                            article['title'],
                            article['description'],
                            article['content'],
                            article['source_name'],
                            article['source_id'],
                            article['author'],
                            article['url'],
                            article['url_to_image'],
                            article['published_at'],
                            article['fetched_at'],
                            article['content_hash'],
                            article['language'],
                            article['raw_json'],
                            article['processing_status']
                        ))

                        new_articles_count += 1
                        logger.debug(f"✅ Stored: {article['title'][:50]}...")

                    except Exception as e:
                        logger.error(f"❌ Failed to store article {article.get('title', 'Unknown')}: {e}")
                        continue

                # Commit all changes
                conn.commit()

        except Exception as e:
            logger.error(f"❌ Failed to store articles in SQL Server: {e}")

        logger.info(f"✅ Stored {new_articles_count} new articles in raw_articles table")
        return new_articles_count
        
        # Initialize storage
        self._initialize_storage()
        
        logger.info(f"📰 News ingestion initialized (Database: {use_database})")
    
    def _initialize_storage(self):
        """Initialize CSV file or database."""
        
        if self.use_database:
            self._initialize_database()
        else:
            self._initialize_csv()
    
    def _initialize_csv(self):
        """Initialize CSV file with headers."""
        
        if not self.raw_articles_file.exists():
            headers = [
                'article_id', 'title', 'description', 'content', 'full_text',
                'source_name', 'source_id', 'author', 'url', 'url_to_image',
                'published_at', 'fetched_at', 'content_hash', 'language',
                'country', 'category'
            ]
            
            pd.DataFrame(columns=headers).to_csv(self.raw_articles_file, index=False)
            logger.info(f"📄 Created CSV file: {self.raw_articles_file}")
    
    def _initialize_database(self):
        """Initialize SQLite database with tables."""
        
        with sqlite3.connect(self.database_file) as conn:
            cursor = conn.cursor()
            
            # Create articles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS articles (
                    article_id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    content TEXT,
                    full_text TEXT,
                    source_name TEXT,
                    source_id TEXT,
                    author TEXT,
                    url TEXT UNIQUE,
                    url_to_image TEXT,
                    published_at TIMESTAMP,
                    fetched_at TIMESTAMP,
                    content_hash TEXT UNIQUE,
                    language TEXT,
                    country TEXT,
                    category TEXT
                )
            ''')
            
            # Create indexes for performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_published_at ON articles(published_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_hash ON articles(content_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_url ON articles(url)')
            
            conn.commit()
            logger.info(f"🗄️ Database initialized: {self.database_file}")
    
    def _generate_article_id(self, article: Dict) -> str:
        """Generate unique article ID."""
        
        # Use URL + published_at for ID generation
        url = article.get('url', '')
        published_at = article.get('publishedAt', '')
        
        id_string = f"{url}_{published_at}"
        return hashlib.md5(id_string.encode()).hexdigest()[:16]
    
    def _generate_content_hash(self, article: Dict) -> str:
        """Generate content hash for deduplication."""
        
        title = article.get('title', '').strip()
        content = article.get('content', '').strip()
        
        content_string = f"{title}_{content}"
        return hashlib.md5(content_string.encode()).hexdigest()
    
    def _clean_article_data(self, article: Dict) -> Dict:
        """Clean and structure article data."""
        
        # Extract and clean fields
        title = article.get('title', '').strip()
        description = article.get('description', '').strip()
        content = article.get('content', '').strip()
        
        # Remove "[+X chars]" from content
        if content and '[+' in content and 'chars]' in content:
            content = content.split('[+')[0].strip()
        
        # Combine full text
        full_text_parts = [title, description, content]
        full_text = '. '.join([part for part in full_text_parts if part]).strip()
        
        # Clean source information
        source = article.get('source', {})
        source_name = source.get('name', 'Unknown') if source else 'Unknown'
        source_id = source.get('id', '') if source else ''
        
        # Structure cleaned article
        cleaned = {
            'article_id': self._generate_article_id(article),
            'title': title,
            'description': description,
            'content': content,
            'full_text': full_text,
            'source_name': source_name,
            'source_id': source_id,
            'author': article.get('author', 'Unknown'),
            'url': article.get('url', ''),
            'url_to_image': article.get('urlToImage', ''),
            'published_at': article.get('publishedAt', ''),
            'fetched_at': datetime.now().isoformat(),
            'content_hash': self._generate_content_hash(article),
            'language': 'en',  # Default to English
            'country': '',
            'category': ''
        }
        
        return cleaned
    
    def fetch_articles(self,
                      query: str = None,
                      sources: List[str] = None,
                      domains: List[str] = None,
                      from_date: datetime = None,
                      to_date: datetime = None,
                      language: str = 'en',
                      sort_by: str = 'publishedAt',
                      page_size: int = 100,
                      max_pages: int = 5) -> List[Dict]:
        """
        Fetch articles from NewsAPI.
        
        Args:
            query: Search query keywords
            sources: List of source IDs
            domains: List of domains to search
            from_date: Start date for articles
            to_date: End date for articles
            language: Language code
            sort_by: Sort order (publishedAt, relevancy, popularity)
            page_size: Articles per page (max 100)
            max_pages: Maximum pages to fetch
            
        Returns:
            List of cleaned article dictionaries
        """
        
        # Default query if none provided
        if not query and not sources and not domains:
            query = "technology OR politics OR business OR economy"
        
        # Default date range (last 24 hours)
        if not from_date:
            from_date = datetime.now() - timedelta(hours=24)
        if not to_date:
            to_date = datetime.now()
        
        # Build API parameters
        params = {
            'apiKey': self.api_key,
            'language': language,
            'sortBy': sort_by,
            'from': from_date.isoformat(),
            'to': to_date.isoformat(),
            'pageSize': min(page_size, 100),
            'page': 1
        }
        
        # Add optional parameters
        if query:
            params['q'] = query
        if sources:
            params['sources'] = ','.join(sources)
        if domains:
            params['domains'] = ','.join(domains)
        
        logger.info(f"🔍 Fetching articles: query='{query}', pages={max_pages}")
        
        all_articles = []
        
        for page in range(1, max_pages + 1):
            params['page'] = page
            
            try:
                response = requests.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if data['status'] != 'ok':
                    logger.error(f"❌ API Error: {data.get('message', 'Unknown error')}")
                    break
                
                articles = data.get('articles', [])
                
                if not articles:
                    logger.info(f"ℹ️ No more articles on page {page}")
                    break
                
                # Clean articles
                cleaned_articles = []
                for article in articles:
                    cleaned = self._clean_article_data(article)
                    if cleaned['title'] and len(cleaned['full_text']) > 50:
                        cleaned_articles.append(cleaned)
                
                all_articles.extend(cleaned_articles)
                logger.info(f"📄 Page {page}: {len(cleaned_articles)} valid articles")
                
                # Check if we've reached the end
                total_results = data.get('totalResults', 0)
                if len(all_articles) >= total_results:
                    break
                
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ Request failed on page {page}: {e}")
                break
            except Exception as e:
                logger.error(f"❌ Unexpected error on page {page}: {e}")
                break
        
        logger.info(f"✅ Fetched {len(all_articles)} total articles")
        return all_articles
    
    def _check_duplicates_csv(self, articles: List[Dict]) -> List[Dict]:
        """Check for duplicates in CSV storage."""
        
        if not self.raw_articles_file.exists():
            return articles
        
        try:
            existing_df = pd.read_csv(self.raw_articles_file)
            
            if existing_df.empty:
                return articles
            
            existing_hashes = set(existing_df['content_hash'].tolist())
            existing_urls = set(existing_df['url'].tolist())
            
            new_articles = []
            for article in articles:
                if (article['content_hash'] not in existing_hashes and 
                    article['url'] not in existing_urls):
                    new_articles.append(article)
            
            logger.info(f"🔍 Deduplication: {len(new_articles)}/{len(articles)} are new")
            return new_articles
            
        except Exception as e:
            logger.error(f"❌ Error checking duplicates: {e}")
            return articles
    
    def _check_duplicates_db(self, articles: List[Dict]) -> List[Dict]:
        """Check for duplicates in database storage."""
        
        try:
            with sqlite3.connect(self.database_file) as conn:
                cursor = conn.cursor()
                
                new_articles = []
                for article in articles:
                    # Check by content hash and URL
                    cursor.execute('''
                        SELECT COUNT(*) FROM articles 
                        WHERE content_hash = ? OR url = ?
                    ''', (article['content_hash'], article['url']))
                    
                    count = cursor.fetchone()[0]
                    if count == 0:
                        new_articles.append(article)
                
                logger.info(f"🔍 Deduplication: {len(new_articles)}/{len(articles)} are new")
                return new_articles
                
        except Exception as e:
            logger.error(f"❌ Error checking duplicates: {e}")
            return articles
    
    def save_articles(self, articles: List[Dict]) -> int:
        """
        Save articles to storage with deduplication.
        
        Args:
            articles: List of article dictionaries
            
        Returns:
            Number of articles saved
        """
        
        if not articles:
            logger.warning("⚠️ No articles to save")
            return 0
        
        # Check for duplicates
        if self.use_database:
            new_articles = self._check_duplicates_db(articles)
        else:
            new_articles = self._check_duplicates_csv(articles)
        
        if not new_articles:
            logger.info("ℹ️ No new articles to save (all duplicates)")
            return 0
        
        # Save new articles
        if self.use_database:
            return self._save_to_database(new_articles)
        else:
            return self._save_to_csv(new_articles)
    
    def _save_to_csv(self, articles: List[Dict]) -> int:
        """Save articles to CSV file."""
        
        try:
            df = pd.DataFrame(articles)
            df.to_csv(self.raw_articles_file, mode='a', header=False, index=False)
            
            logger.info(f"✅ Saved {len(articles)} articles to CSV")
            return len(articles)
            
        except Exception as e:
            logger.error(f"❌ Failed to save to CSV: {e}")
            return 0
    
    def _save_to_database(self, articles: List[Dict]) -> int:
        """Save articles to database."""
        
        try:
            with sqlite3.connect(self.database_file) as conn:
                df = pd.DataFrame(articles)
                df.to_sql('articles', conn, if_exists='append', index=False)
                
                logger.info(f"✅ Saved {len(articles)} articles to database")
                return len(articles)
                
        except Exception as e:
            logger.error(f"❌ Failed to save to database: {e}")
            return 0
    
    def get_article_count(self) -> int:
        """Get total number of articles in storage."""
        
        if self.use_database:
            try:
                with sqlite3.connect(self.database_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT COUNT(*) FROM articles')
                    return cursor.fetchone()[0]
            except:
                return 0
        else:
            try:
                if self.raw_articles_file.exists():
                    df = pd.read_csv(self.raw_articles_file)
                    return len(df)
                return 0
            except:
                return 0
    
    def get_latest_articles(self, limit: int = 10) -> pd.DataFrame:
        """Get latest articles from storage."""
        
        if self.use_database:
            try:
                with sqlite3.connect(self.database_file) as conn:
                    query = '''
                        SELECT * FROM articles 
                        ORDER BY published_at DESC 
                        LIMIT ?
                    '''
                    return pd.read_sql_query(query, conn, params=[limit])
            except Exception as e:
                logger.error(f"❌ Error getting latest articles: {e}")
                return pd.DataFrame()
        else:
            try:
                if self.raw_articles_file.exists():
                    df = pd.read_csv(self.raw_articles_file)
                    df['published_at'] = pd.to_datetime(df['published_at'])
                    return df.sort_values('published_at', ascending=False).head(limit)
                return pd.DataFrame()
            except Exception as e:
                logger.error(f"❌ Error getting latest articles: {e}")
                return pd.DataFrame()

def main():
    """Test the news ingestion module."""
    
    # Load API key from environment
    api_key = os.getenv('NEWS_API_KEY')
    if not api_key:
        logger.error("❌ NEWS_API_KEY environment variable not set")
        return
    
    # Create ingestion instance
    ingestion = NewsDataIngestion(api_key, use_database=False)
    
    # Fetch articles
    articles = ingestion.fetch_articles(
        query="artificial intelligence OR machine learning",
        max_pages=2
    )
    
    # Save articles
    saved_count = ingestion.save_articles(articles)
    
    # Show statistics
    total_count = ingestion.get_article_count()
    logger.info(f"📊 Total articles in storage: {total_count}")
    
    # Show latest articles
    latest = ingestion.get_latest_articles(5)
    if not latest.empty:
        logger.info("📰 Latest articles:")
        for _, article in latest.iterrows():
            logger.info(f"  - {article['title'][:50]}...")

if __name__ == "__main__":
    main()
