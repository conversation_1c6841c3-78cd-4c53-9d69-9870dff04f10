#!/usr/bin/env python3
"""
Complete Data Ingestion Module
Fetches news articles from NewsAPI with deduplication and persistent storage
"""

import requests
import json
import pandas as pd
import sqlite3
import os
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsDataIngestion:
    """Comprehensive news data ingestion with deduplication and persistence."""
    
    def __init__(self, 
                 api_key: str,
                 data_dir: str = "data",
                 use_database: bool = False):
        """
        Initialize news data ingestion.
        
        Args:
            api_key: NewsAPI key
            data_dir: Directory for data storage
            use_database: Whether to use SQLite database instead of CSV
        """
        self.api_key = api_key
        self.data_dir = Path(data_dir)
        self.use_database = use_database
        
        # Create data directory
        self.data_dir.mkdir(exist_ok=True)
        
        # File paths
        self.raw_articles_file = self.data_dir / "raw_articles.csv"
        self.database_file = self.data_dir / "news.db"
        
        # NewsAPI configuration
        self.base_url = "https://newsapi.org/v2/everything"
        self.sources_url = "https://newsapi.org/v2/sources"
        
        # Initialize storage
        self._initialize_storage()
        
        logger.info(f"📰 News ingestion initialized (Database: {use_database})")
    
    def _initialize_storage(self):
        """Initialize CSV file or database."""
        
        if self.use_database:
            self._initialize_database()
        else:
            self._initialize_csv()
    
    def _initialize_csv(self):
        """Initialize CSV file with headers."""
        
        if not self.raw_articles_file.exists():
            headers = [
                'article_id', 'title', 'description', 'content', 'full_text',
                'source_name', 'source_id', 'author', 'url', 'url_to_image',
                'published_at', 'fetched_at', 'content_hash', 'language',
                'country', 'category'
            ]
            
            pd.DataFrame(columns=headers).to_csv(self.raw_articles_file, index=False)
            logger.info(f"📄 Created CSV file: {self.raw_articles_file}")
    
    def _initialize_database(self):
        """Initialize SQLite database with tables."""
        
        with sqlite3.connect(self.database_file) as conn:
            cursor = conn.cursor()
            
            # Create articles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS articles (
                    article_id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    content TEXT,
                    full_text TEXT,
                    source_name TEXT,
                    source_id TEXT,
                    author TEXT,
                    url TEXT UNIQUE,
                    url_to_image TEXT,
                    published_at TIMESTAMP,
                    fetched_at TIMESTAMP,
                    content_hash TEXT UNIQUE,
                    language TEXT,
                    country TEXT,
                    category TEXT
                )
            ''')
            
            # Create indexes for performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_published_at ON articles(published_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_hash ON articles(content_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_url ON articles(url)')
            
            conn.commit()
            logger.info(f"🗄️ Database initialized: {self.database_file}")
    
    def _generate_article_id(self, article: Dict) -> str:
        """Generate unique article ID."""
        
        # Use URL + published_at for ID generation
        url = article.get('url', '')
        published_at = article.get('publishedAt', '')
        
        id_string = f"{url}_{published_at}"
        return hashlib.md5(id_string.encode()).hexdigest()[:16]
    
    def _generate_content_hash(self, article: Dict) -> str:
        """Generate content hash for deduplication."""
        
        title = article.get('title', '').strip()
        content = article.get('content', '').strip()
        
        content_string = f"{title}_{content}"
        return hashlib.md5(content_string.encode()).hexdigest()
    
    def _clean_article_data(self, article: Dict) -> Dict:
        """Clean and structure article data."""
        
        # Extract and clean fields
        title = article.get('title', '').strip()
        description = article.get('description', '').strip()
        content = article.get('content', '').strip()
        
        # Remove "[+X chars]" from content
        if content and '[+' in content and 'chars]' in content:
            content = content.split('[+')[0].strip()
        
        # Combine full text
        full_text_parts = [title, description, content]
        full_text = '. '.join([part for part in full_text_parts if part]).strip()
        
        # Clean source information
        source = article.get('source', {})
        source_name = source.get('name', 'Unknown') if source else 'Unknown'
        source_id = source.get('id', '') if source else ''
        
        # Structure cleaned article
        cleaned = {
            'article_id': self._generate_article_id(article),
            'title': title,
            'description': description,
            'content': content,
            'full_text': full_text,
            'source_name': source_name,
            'source_id': source_id,
            'author': article.get('author', 'Unknown'),
            'url': article.get('url', ''),
            'url_to_image': article.get('urlToImage', ''),
            'published_at': article.get('publishedAt', ''),
            'fetched_at': datetime.now().isoformat(),
            'content_hash': self._generate_content_hash(article),
            'language': 'en',  # Default to English
            'country': '',
            'category': ''
        }
        
        return cleaned
    
    def fetch_articles(self,
                      query: str = None,
                      sources: List[str] = None,
                      domains: List[str] = None,
                      from_date: datetime = None,
                      to_date: datetime = None,
                      language: str = 'en',
                      sort_by: str = 'publishedAt',
                      page_size: int = 100,
                      max_pages: int = 5) -> List[Dict]:
        """
        Fetch articles from NewsAPI.
        
        Args:
            query: Search query keywords
            sources: List of source IDs
            domains: List of domains to search
            from_date: Start date for articles
            to_date: End date for articles
            language: Language code
            sort_by: Sort order (publishedAt, relevancy, popularity)
            page_size: Articles per page (max 100)
            max_pages: Maximum pages to fetch
            
        Returns:
            List of cleaned article dictionaries
        """
        
        # Default query if none provided
        if not query and not sources and not domains:
            query = "technology OR politics OR business OR economy"
        
        # Default date range (last 24 hours)
        if not from_date:
            from_date = datetime.now() - timedelta(hours=24)
        if not to_date:
            to_date = datetime.now()
        
        # Build API parameters
        params = {
            'apiKey': self.api_key,
            'language': language,
            'sortBy': sort_by,
            'from': from_date.isoformat(),
            'to': to_date.isoformat(),
            'pageSize': min(page_size, 100),
            'page': 1
        }
        
        # Add optional parameters
        if query:
            params['q'] = query
        if sources:
            params['sources'] = ','.join(sources)
        if domains:
            params['domains'] = ','.join(domains)
        
        logger.info(f"🔍 Fetching articles: query='{query}', pages={max_pages}")
        
        all_articles = []
        
        for page in range(1, max_pages + 1):
            params['page'] = page
            
            try:
                response = requests.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if data['status'] != 'ok':
                    logger.error(f"❌ API Error: {data.get('message', 'Unknown error')}")
                    break
                
                articles = data.get('articles', [])
                
                if not articles:
                    logger.info(f"ℹ️ No more articles on page {page}")
                    break
                
                # Clean articles
                cleaned_articles = []
                for article in articles:
                    cleaned = self._clean_article_data(article)
                    if cleaned['title'] and len(cleaned['full_text']) > 50:
                        cleaned_articles.append(cleaned)
                
                all_articles.extend(cleaned_articles)
                logger.info(f"📄 Page {page}: {len(cleaned_articles)} valid articles")
                
                # Check if we've reached the end
                total_results = data.get('totalResults', 0)
                if len(all_articles) >= total_results:
                    break
                
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ Request failed on page {page}: {e}")
                break
            except Exception as e:
                logger.error(f"❌ Unexpected error on page {page}: {e}")
                break
        
        logger.info(f"✅ Fetched {len(all_articles)} total articles")
        return all_articles
    
    def _check_duplicates_csv(self, articles: List[Dict]) -> List[Dict]:
        """Check for duplicates in CSV storage."""
        
        if not self.raw_articles_file.exists():
            return articles
        
        try:
            existing_df = pd.read_csv(self.raw_articles_file)
            
            if existing_df.empty:
                return articles
            
            existing_hashes = set(existing_df['content_hash'].tolist())
            existing_urls = set(existing_df['url'].tolist())
            
            new_articles = []
            for article in articles:
                if (article['content_hash'] not in existing_hashes and 
                    article['url'] not in existing_urls):
                    new_articles.append(article)
            
            logger.info(f"🔍 Deduplication: {len(new_articles)}/{len(articles)} are new")
            return new_articles
            
        except Exception as e:
            logger.error(f"❌ Error checking duplicates: {e}")
            return articles
    
    def _check_duplicates_db(self, articles: List[Dict]) -> List[Dict]:
        """Check for duplicates in database storage."""
        
        try:
            with sqlite3.connect(self.database_file) as conn:
                cursor = conn.cursor()
                
                new_articles = []
                for article in articles:
                    # Check by content hash and URL
                    cursor.execute('''
                        SELECT COUNT(*) FROM articles 
                        WHERE content_hash = ? OR url = ?
                    ''', (article['content_hash'], article['url']))
                    
                    count = cursor.fetchone()[0]
                    if count == 0:
                        new_articles.append(article)
                
                logger.info(f"🔍 Deduplication: {len(new_articles)}/{len(articles)} are new")
                return new_articles
                
        except Exception as e:
            logger.error(f"❌ Error checking duplicates: {e}")
            return articles
    
    def save_articles(self, articles: List[Dict]) -> int:
        """
        Save articles to storage with deduplication.
        
        Args:
            articles: List of article dictionaries
            
        Returns:
            Number of articles saved
        """
        
        if not articles:
            logger.warning("⚠️ No articles to save")
            return 0
        
        # Check for duplicates
        if self.use_database:
            new_articles = self._check_duplicates_db(articles)
        else:
            new_articles = self._check_duplicates_csv(articles)
        
        if not new_articles:
            logger.info("ℹ️ No new articles to save (all duplicates)")
            return 0
        
        # Save new articles
        if self.use_database:
            return self._save_to_database(new_articles)
        else:
            return self._save_to_csv(new_articles)
    
    def _save_to_csv(self, articles: List[Dict]) -> int:
        """Save articles to CSV file."""
        
        try:
            df = pd.DataFrame(articles)
            df.to_csv(self.raw_articles_file, mode='a', header=False, index=False)
            
            logger.info(f"✅ Saved {len(articles)} articles to CSV")
            return len(articles)
            
        except Exception as e:
            logger.error(f"❌ Failed to save to CSV: {e}")
            return 0
    
    def _save_to_database(self, articles: List[Dict]) -> int:
        """Save articles to database."""
        
        try:
            with sqlite3.connect(self.database_file) as conn:
                df = pd.DataFrame(articles)
                df.to_sql('articles', conn, if_exists='append', index=False)
                
                logger.info(f"✅ Saved {len(articles)} articles to database")
                return len(articles)
                
        except Exception as e:
            logger.error(f"❌ Failed to save to database: {e}")
            return 0
    
    def get_article_count(self) -> int:
        """Get total number of articles in storage."""
        
        if self.use_database:
            try:
                with sqlite3.connect(self.database_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT COUNT(*) FROM articles')
                    return cursor.fetchone()[0]
            except:
                return 0
        else:
            try:
                if self.raw_articles_file.exists():
                    df = pd.read_csv(self.raw_articles_file)
                    return len(df)
                return 0
            except:
                return 0
    
    def get_latest_articles(self, limit: int = 10) -> pd.DataFrame:
        """Get latest articles from storage."""
        
        if self.use_database:
            try:
                with sqlite3.connect(self.database_file) as conn:
                    query = '''
                        SELECT * FROM articles 
                        ORDER BY published_at DESC 
                        LIMIT ?
                    '''
                    return pd.read_sql_query(query, conn, params=[limit])
            except Exception as e:
                logger.error(f"❌ Error getting latest articles: {e}")
                return pd.DataFrame()
        else:
            try:
                if self.raw_articles_file.exists():
                    df = pd.read_csv(self.raw_articles_file)
                    df['published_at'] = pd.to_datetime(df['published_at'])
                    return df.sort_values('published_at', ascending=False).head(limit)
                return pd.DataFrame()
            except Exception as e:
                logger.error(f"❌ Error getting latest articles: {e}")
                return pd.DataFrame()

def main():
    """Test the news ingestion module."""
    
    # Load API key from environment
    api_key = os.getenv('NEWS_API_KEY')
    if not api_key:
        logger.error("❌ NEWS_API_KEY environment variable not set")
        return
    
    # Create ingestion instance
    ingestion = NewsDataIngestion(api_key, use_database=False)
    
    # Fetch articles
    articles = ingestion.fetch_articles(
        query="artificial intelligence OR machine learning",
        max_pages=2
    )
    
    # Save articles
    saved_count = ingestion.save_articles(articles)
    
    # Show statistics
    total_count = ingestion.get_article_count()
    logger.info(f"📊 Total articles in storage: {total_count}")
    
    # Show latest articles
    latest = ingestion.get_latest_articles(5)
    if not latest.empty:
        logger.info("📰 Latest articles:")
        for _, article in latest.iterrows():
            logger.info(f"  - {article['title'][:50]}...")

if __name__ == "__main__":
    main()
