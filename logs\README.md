# 📝 Logs Directory

## 📁 Purpose
This directory stores all log files from the topic modeling pipeline for monitoring and debugging.

## 📋 Log Files

### **Main Pipeline Logs:**
- `realtime_service.log` - Main pipeline execution log
- `automated_pipeline.log` - Automated pipeline runs
- `manual_runs.log` - Manual pipeline executions

### **Component-Specific Logs:**
- `news_ingestion.log` - NewsAPI data collection
- `preprocessing.log` - Text preprocessing operations
- `topic_classification.log` - LDA model predictions
- `database.log` - MySQL operations and queries

### **Error Logs:**
- `errors.log` - All error messages and stack traces
- `failed_articles.log` - Articles that failed processing
- `connection_errors.log` - Database connection issues

## 🔍 Log Levels

### **DEBUG** - Detailed execution information
```
2024-01-15 10:30:15 DEBUG [fetch_news.py:125] Processing article: "AI breakthrough..."
```

### **INFO** - General pipeline status
```
2024-01-15 10:30:15 INFO [automated_pipeline.py:95] ✅ MySQL connection established
```

### **WARNING** - Non-critical issues
```
2024-01-15 10:30:15 WARNING [preprocess.py:234] Article too short, skipping: article_123
```

### **ERROR** - Critical failures
```
2024-01-15 10:30:15 ERROR [topic_model.py:156] ❌ Model file not found: models/lda_model.pkl
```

## 📊 Log Rotation

### **Automatic Rotation:**
- **Max Size**: 10MB per log file
- **Backup Count**: 5 files kept
- **Compression**: Old logs compressed (.gz)

### **File Naming:**
```
realtime_service.log      # Current log
realtime_service.log.1    # Previous log
realtime_service.log.2.gz # Compressed backup
```

## 🔧 Log Configuration

### **Current Settings (from config/pipeline.yaml):**
```yaml
logging:
  level: "INFO"
  log_file: "logs/realtime_service.log"
  max_log_size: "10MB"
  backup_count: 5
```

### **Change Log Level:**
```yaml
# For debugging issues
logging:
  level: "DEBUG"

# For production (less verbose)
logging:
  level: "WARNING"
```

## 🔍 Monitoring Commands

### **View Real-time Logs:**
```bash
# Follow main pipeline log
tail -f logs/realtime_service.log

# Follow all logs
tail -f logs/*.log

# Search for errors
grep "ERROR" logs/*.log

# Search for specific article
grep "article_123" logs/*.log
```

### **Log Analysis:**
```bash
# Count errors in last hour
grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')" logs/errors.log | wc -l

# Find most common errors
grep "ERROR" logs/*.log | cut -d']' -f2 | sort | uniq -c | sort -nr

# Check pipeline performance
grep "Pipeline cycle completed" logs/realtime_service.log | tail -10
```

## 📈 Log Monitoring

### **Key Metrics to Watch:**
- **Error Rate**: <5% of total operations
- **Processing Time**: <30 seconds per cycle
- **Database Connections**: No connection timeouts
- **Model Performance**: Average confidence >0.5

### **Alert Conditions:**
- Multiple consecutive errors
- Database connection failures
- Model loading failures
- Disk space warnings

## 🧹 Log Maintenance

### **Manual Cleanup:**
```bash
# Remove logs older than 30 days
find logs/ -name "*.log*" -mtime +30 -delete

# Compress large current logs
gzip logs/realtime_service.log.1

# Clear all logs (use with caution)
rm -f logs/*.log
```

### **Log Backup:**
```bash
# Backup important logs
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/

# Archive monthly logs
mkdir -p archives/$(date +%Y-%m)
mv logs/*.log.*.gz archives/$(date +%Y-%m)/
```

## 🚨 Troubleshooting

### **Common Log Issues:**

1. **"Permission denied writing to log file"**
   - Check directory permissions: `chmod 755 logs/`
   - Ensure user has write access

2. **"Log file too large"**
   - Check log rotation settings
   - Manually rotate: `logrotate -f logrotate.conf`

3. **"No log output"**
   - Check logging level in config
   - Verify log file path exists

4. **"Logs filling disk space"**
   - Reduce log level to WARNING
   - Increase rotation frequency
   - Clean old archives

## 📊 Log Analysis Tools

### **Useful Commands:**
```bash
# Pipeline success rate
grep -c "Pipeline cycle completed" logs/realtime_service.log

# Average processing time
grep "Processing time:" logs/realtime_service.log | awk '{sum+=$NF; count++} END {print sum/count}'

# Error summary
grep "ERROR" logs/*.log | cut -d':' -f3 | sort | uniq -c | sort -nr
```

**Monitor these logs to ensure your pipeline runs smoothly!** 📊
