# 🔍 Real-Time Topic Modeling and Trend Analysis

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

An end-to-end automated pipeline that collects real-time news data from multiple sources, applies advanced topic modeling using Latent Dirichlet Allocation (LDA), and creates interactive visualizations in Power BI for trend analysis and insights.

## 🌟 Key Features

### 📰 **Multi-Source Data Collection**
- **NewsAPI Integration**: Fetch articles with configurable keywords and filters
- **RSS Feed Support**: Fallback to RSS feeds when API limits are reached
- **Automated Scheduling**: Daily, weekly, or custom scheduling options
- **Data Validation**: Automatic deduplication and quality checks

### 🧠 **Advanced Text Processing**
- **Smart Preprocessing**: HTML cleaning, tokenization, and normalization
- **Multi-language Support**: Primary focus on English with extensibility
- **Stopword Filtering**: Customizable stopword lists for domain-specific analysis
- **Lemmatization**: Advanced word normalization using spaCy and NLTK

### 🎯 **Intelligent Topic Modeling**
- **LDA Implementation**: Both scikit-learn and Gensim backends supported
- **Automatic Topic Discovery**: Configurable number of topics with quality metrics
- **Keyword Extraction**: Top keywords and phrases for each topic
- **Confidence Scoring**: Topic assignment confidence for quality assessment

### 📊 **Power BI Ready Exports**
- **Structured Data**: Clean, normalized datasets for immediate import
- **Multiple Views**: Article-level, topic summary, and time series data
- **Interactive Dashboards**: Pre-built templates and visualization guides
- **Real-time Updates**: Automated refresh capabilities

### 🔧 **Production Ready**
- **Comprehensive Testing**: Unit tests, integration tests, and end-to-end validation
- **Monitoring & Logging**: Detailed logging with configurable levels
- **Error Handling**: Graceful degradation and retry mechanisms
- **Scalable Architecture**: Modular design for easy extension and customization

## 🚀 Quick Start

### 1. Installation
```bash
# Clone the repository
git clone <repository-url>
cd topic_modelling

# Automated setup (recommended)
python setup.py

# Manual setup alternative
pip install -r requirements.txt
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"
python -m spacy download en_core_web_sm
```

### 2. Configuration
```bash
# Set up environment variables
cp .env.example .env
# Edit .env with your NewsAPI key from https://newsapi.org/

# Customize settings (optional)
# Edit config.yaml for advanced configuration
```

### 3. Run Pipeline
```bash
# Test with data ingestion only
python run_pipeline.py --step ingestion

# Run complete pipeline
python run_pipeline.py

# Run specific components
python run_pipeline.py --step preprocessing
python run_pipeline.py --step modeling
python run_pipeline.py --step export
```

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Memory**: 4 GB RAM
- **Storage**: 5 GB free space
- **OS**: Windows 10, macOS 10.14+, or Linux (Ubuntu 18.04+)

### Recommended for Production
- **Python**: 3.9 or 3.10
- **Memory**: 8 GB RAM or more
- **Storage**: 20 GB free space (SSD preferred)
- **CPU**: Multi-core processor

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │   Processing    │    │     Output      │
│                 │    │                 │    │                 │
│ • NewsAPI       │───▶│ • Text Cleaning │───▶│ • CSV Files     │
│ • RSS Feeds     │    │ • Tokenization  │    │ • Power BI Data │
│ • Custom APIs   │    │ • Topic Modeling│    │ • Visualizations│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scheduling    │    │    Monitoring   │    │   Integration   │
│                 │    │                 │    │                 │
│ • Cron Jobs     │    │ • Logging       │    │ • Power BI      │
│ • Task Scheduler│    │ • Health Checks │    │ • Dashboards    │
│ • Cloud Triggers│    │ • Alerting      │    │ • APIs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Sample Output

The pipeline generates structured data perfect for analysis:

### Topic Distribution Example
| Topic ID | Topic Label | Keywords | Article Count | Confidence |
|----------|-------------|----------|---------------|------------|
| 0 | Technology_AI_Innovation | artificial, intelligence, technology | 45 | 0.87 |
| 1 | Politics_Election_Democracy | election, voting, political | 38 | 0.82 |
| 2 | Economy_Market_Financial | market, economic, financial | 42 | 0.79 |

### Time Series Trends
| Date | Topic | Article Count | Avg Confidence |
|------|-------|---------------|----------------|
| 2024-01-01 | Technology_AI | 12 | 0.85 |
| 2024-01-01 | Politics_Election | 8 | 0.78 |
| 2024-01-02 | Technology_AI | 15 | 0.88 |
