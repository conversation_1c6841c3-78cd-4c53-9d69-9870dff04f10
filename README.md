# 🔍 Real-Time Topic Modeling and Trend Analysis

## 🧠 **Project Overview**

**Real-Time Topic Modeling and Trend Analysis on News Articles using Pre-trained LDA and Power BI**

A comprehensive data pipeline that fetches news articles using NewsAPI, applies your trained LDA model for topic classification, and visualizes evolving topic trends through interactive Power BI dashboards.

## 🏗️ **System Architecture**

```
+---------------------+         +---------------------+         +------------------------+         +------------------+
| 1. Data Ingestion   |         | 2. Data Processing  |         | 3. Topic Modeling      |         | 4. Visualization |
+---------------------+         +---------------------+         +------------------------+         +------------------+
| - NewsAPI / GNews   |  --->   | - Clean text        |  --->   | - Your .pkl LDA Model  |  --->   | - Power BI       |
| - Hourly scheduler  |         | - Remove stopwords  |         | - Topic classification |         | - Live dashboard |
| - JSON to CSV/DB    |         | - Lemmatization     |         | - Topic keywords       |         | - Trend analysis |
+---------------------+         +---------------------+         +------------------------+         +------------------+
         |                                |                              |                                  ↑
         |                                ↓                              ↓                                  |
     Persistent                     Processed                         Topic-labeled                   CSV export
     Storage: CSV / DB             Article Texts                     Article Dataset                  for Power BI
```

## 📁 **Project Structure**

```
topic_modelling/
├── 📁 models/                       # 🧠 Place your .pkl file here
│   └── lda_model.pkl               # ← YOUR TRAINED LDA MODEL
├── 📁 data/                         # Data storage
│   ├── raw_articles.csv            # Collected news articles
│   ├── processed_articles.csv      # Articles with topic classifications
│   ├── news.db                     # SQLite database (optional)
│   └── powerbi/                    # Power BI export files
├── 📁 scripts/                      # Pipeline components
│   ├── fetch_news.py               # Data ingestion from NewsAPI
│   ├── preprocess.py               # Advanced text preprocessing
│   ├── topic_model.py              # LDA model integration
│   ├── export_to_csv.py            # Power BI export system
│   └── run_pipeline.py             # Complete pipeline orchestrator
├── 📁 scheduler/                    # Automation configs
│   ├── windows_task.xml            # Windows Task Scheduler
│   └── crontab.txt                 # Linux/Mac cron jobs
├── 📁 powerbi/                      # Power BI templates
│   └── dashboard.pbix              # Pre-built dashboard
├── 📁 config/                       # Configuration
│   ├── .env                        # API keys and settings
│   └── pipeline.yaml              # Pipeline configuration
├── 📁 logs/                         # Logging and monitoring
│   ├── pipeline.log                # Main pipeline logs
│   ├── monitor.log                 # Monitoring logs
│   └── pipeline_status.json        # Current status
├── monitor_pipeline.py             # Health monitoring system
└── requirements.txt                # Python dependencies
```

## 🧠 **LDA Model Setup**

### **Step 1: Place Your .pkl File**

Put your trained LDA model file in the `models/` directory:

```bash
models/
└── lda_model.pkl    # ← Your trained model here
```

### **Step 2: Model File Requirements**

Your `.pkl` file should contain a dictionary with these keys:

```python
{
    'model': trained_lda_model,           # Scikit-learn LDA model
    'vectorizer': count_vectorizer,       # Fitted CountVectorizer
    'topics': {                           # Topic information
        0: {
            'label': 'Technology_AI',
            'keywords': ['artificial', 'intelligence', 'technology', ...]
        },
        1: {
            'label': 'Politics_Election',
            'keywords': ['election', 'political', 'voting', ...]
        },
        # ... more topics
    },
    'method': 'sklearn'                   # Model type
}
```

### **Step 3: Alternative Model Names**

The system will look for models in this order:
1. `models/lda_model.pkl` (default)
2. `models/latest_model.pkl`
3. `models/topic_model_*.pkl` (most recent)

## 🔄 **How the Workflow Works**

### **Step 1: [NewsAPI Request] → [Parse JSON]**
- `newsapi_collector.py` fetches articles from NewsAPI
- Parses JSON response and cleans article data
- Filters by keywords and time range

### **Step 2: [Parse JSON] → [Append to CSV]**
- `csv_pipeline.py` appends new articles to `data/raw_articles.csv`
- Removes duplicates based on article URL
- Maintains simple, readable CSV format

### **Step 3: [CSV] → [LDA Model]**
- `lda_classifier.py` loads your `.pkl` model
- Classifies unprocessed articles from CSV
- Saves results to `data/processed_articles.csv`

### **Step 4: [Classified Data] → [Power BI]**
- `powerbi_exporter.py` creates Power BI ready files
- Generates hourly trends, topic summaries, recent articles
- Exports to `data/powerbi/` directory

### **Complete Workflow**
- `main_workflow.py` orchestrates all steps
- Runs hourly on schedule or on-demand
- Handles errors and logging

## 🚀 Quick Start

### **Prerequisites**
- ✅ Python 3.8+
- ✅ Trained LDA model in `.pkl` format
- ✅ NewsAPI key (free from [newsapi.org](https://newsapi.org/))

### **1. Place Your Model**
```bash
# Put your trained LDA model here:
models/lda_model.pkl
```

### **2. Setup**
```bash
# Install dependencies
pip install -r requirements.txt

# Configure API key
cp config/.env.example config/.env
# Edit config/.env with your NewsAPI key

# Validate your model
python validate_model.py
```

### **3. Run Complete Pipeline**
```bash
# Run complete pipeline once
python scripts/run_pipeline.py run

# Run specific steps
python scripts/run_pipeline.py step --step ingestion
python scripts/run_pipeline.py step --step modeling
python scripts/run_pipeline.py step --step export

# Start automated hourly schedule
python scripts/run_pipeline.py schedule

# Check pipeline status
python scripts/run_pipeline.py status
```

### **4. Monitor Pipeline Health**
```bash
# Single health check
python monitor_pipeline.py check

# Continuous monitoring
python monitor_pipeline.py monitor --interval 300
```

### **5. Power BI Dashboard**
```bash
# Import these comprehensive datasets into Power BI:
data/powerbi/topic_trends.csv        # Topic trends over time
data/powerbi/hourly_trends.csv       # Hourly topic analysis
data/powerbi/article_details.csv     # Recent article details
data/powerbi/topic_summary.csv       # Topic statistics
data/powerbi/source_analysis.csv     # News source analysis
data/powerbi/confidence_analysis.csv # Model confidence metrics
```

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Memory**: 4 GB RAM
- **Storage**: 5 GB free space
- **OS**: Windows 10, macOS 10.14+, or Linux (Ubuntu 18.04+)

### Recommended for Production
- **Python**: 3.9 or 3.10
- **Memory**: 8 GB RAM or more
- **Storage**: 20 GB free space (SSD preferred)
- **CPU**: Multi-core processor

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │   Processing    │    │     Output      │
│                 │    │                 │    │                 │
│ • NewsAPI       │───▶│ • Text Cleaning │───▶│ • CSV Files     │
│ • RSS Feeds     │    │ • Tokenization  │    │ • Power BI Data │
│ • Custom APIs   │    │ • Topic Modeling│    │ • Visualizations│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scheduling    │    │    Monitoring   │    │   Integration   │
│                 │    │                 │    │                 │
│ • Cron Jobs     │    │ • Logging       │    │ • Power BI      │
│ • Task Scheduler│    │ • Health Checks │    │ • Dashboards    │
│ • Cloud Triggers│    │ • Alerting      │    │ • APIs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Sample Output

The pipeline generates structured data perfect for analysis:

### Topic Distribution Example
| Topic ID | Topic Label | Keywords | Article Count | Confidence |
|----------|-------------|----------|---------------|------------|
| 0 | Technology_AI_Innovation | artificial, intelligence, technology | 45 | 0.87 |
| 1 | Politics_Election_Democracy | election, voting, political | 38 | 0.82 |
| 2 | Economy_Market_Financial | market, economic, financial | 42 | 0.79 |

### Time Series Trends
| Date | Topic | Article Count | Avg Confidence |
|------|-------|---------------|----------------|
| 2024-01-01 | Technology_AI | 12 | 0.85 |
| 2024-01-01 | Politics_Election | 8 | 0.78 |
| 2024-01-02 | Technology_AI | 15 | 0.88 |
