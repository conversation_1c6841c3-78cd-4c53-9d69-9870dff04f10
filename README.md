# 🔍 Real-Time Topic Modeling with Pre-trained LDA

**Hourly News Collection → CSV Processing → Pre-trained LDA (.pkl) → Live Power BI Dashboard**

## 🎯 **Project Overview**

This system uses a **pre-trained LDA model** in `.pkl` format to classify news articles collected hourly and update a real-time Power BI dashboard with topic trends.

## 📁 **Project Structure**

```
topic_modelling/
├── 📁 models/                       # 🧠 Place your .pkl file here
│   └── lda_model.pkl               # ← YOUR TRAINED LDA MODEL GOES HERE
├── 📁 core/                         # Core processing components
│   ├── collectors/                  # Hourly news collection
│   ├── models/                      # LDA classification service
│   ├── processors/                  # Text preprocessing
│   └── exporters/                   # Power BI data export
├── 📁 data/                         # Data storage
│   ├── hourly/                     # Hourly collected CSV files
│   ├── processed/                  # Classified articles
│   └── dashboard/                  # Power BI ready exports
├── 📁 config/                       # Configuration
│   ├── pipeline.yaml              # Main settings
│   └── .env                        # API keys
└── start_service.py                # Main launcher
```

## 🧠 **LDA Model Setup**

### **Step 1: Place Your .pkl File**

Put your trained LDA model file in the `models/` directory:

```bash
models/
└── lda_model.pkl    # ← Your trained model here
```

### **Step 2: Model File Requirements**

Your `.pkl` file should contain a dictionary with these keys:

```python
{
    'model': trained_lda_model,           # Scikit-learn LDA model
    'vectorizer': count_vectorizer,       # Fitted CountVectorizer
    'topics': {                           # Topic information
        0: {
            'label': 'Technology_AI',
            'keywords': ['artificial', 'intelligence', 'technology', ...]
        },
        1: {
            'label': 'Politics_Election',
            'keywords': ['election', 'political', 'voting', ...]
        },
        # ... more topics
    },
    'method': 'sklearn'                   # Model type
}
```

### **Step 3: Alternative Model Names**

The system will look for models in this order:
1. `models/lda_model.pkl` (default)
2. `models/latest_model.pkl`
3. `models/topic_model_*.pkl` (most recent)

## 🚀 Quick Start

### **Prerequisites**
- ✅ Python 3.8+
- ✅ Trained LDA model in `.pkl` format
- ✅ NewsAPI key (free from [newsapi.org](https://newsapi.org/))

### **1. Place Your Model**
```bash
# Put your trained LDA model here:
models/lda_model.pkl
```

### **2. Setup**
```bash
# Install dependencies
pip install -r requirements.txt

# Configure API key
cp config/.env.example config/.env
# Edit config/.env with your NewsAPI key

# Validate your model
python validate_model.py
```

### **3. Start Real-Time Service**
```bash
# Start hourly collection and classification
python start_service.py start

# Check service status
python start_service.py status
```

### **4. Power BI Dashboard**
```bash
# Import these files into Power BI:
data/dashboard/hourly_trends.csv      # Topic trends over time
data/dashboard/realtime_articles.csv  # Latest classified articles
data/dashboard/topic_summary.csv      # Topic statistics
```

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Memory**: 4 GB RAM
- **Storage**: 5 GB free space
- **OS**: Windows 10, macOS 10.14+, or Linux (Ubuntu 18.04+)

### Recommended for Production
- **Python**: 3.9 or 3.10
- **Memory**: 8 GB RAM or more
- **Storage**: 20 GB free space (SSD preferred)
- **CPU**: Multi-core processor

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │   Processing    │    │     Output      │
│                 │    │                 │    │                 │
│ • NewsAPI       │───▶│ • Text Cleaning │───▶│ • CSV Files     │
│ • RSS Feeds     │    │ • Tokenization  │    │ • Power BI Data │
│ • Custom APIs   │    │ • Topic Modeling│    │ • Visualizations│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scheduling    │    │    Monitoring   │    │   Integration   │
│                 │    │                 │    │                 │
│ • Cron Jobs     │    │ • Logging       │    │ • Power BI      │
│ • Task Scheduler│    │ • Health Checks │    │ • Dashboards    │
│ • Cloud Triggers│    │ • Alerting      │    │ • APIs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Sample Output

The pipeline generates structured data perfect for analysis:

### Topic Distribution Example
| Topic ID | Topic Label | Keywords | Article Count | Confidence |
|----------|-------------|----------|---------------|------------|
| 0 | Technology_AI_Innovation | artificial, intelligence, technology | 45 | 0.87 |
| 1 | Politics_Election_Democracy | election, voting, political | 38 | 0.82 |
| 2 | Economy_Market_Financial | market, economic, financial | 42 | 0.79 |

### Time Series Trends
| Date | Topic | Article Count | Avg Confidence |
|------|-------|---------------|----------------|
| 2024-01-01 | Technology_AI | 12 | 0.85 |
| 2024-01-01 | Politics_Election | 8 | 0.78 |
| 2024-01-02 | Technology_AI | 15 | 0.88 |
