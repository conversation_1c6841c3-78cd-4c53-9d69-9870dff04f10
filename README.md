# 🔍 Real-Time Topic Modeling with Pre-trained LDA

## 🎯 **Simple Workflow**

```
[NewsAPI Request] → [Parse JSON] → [Append to CSV] → [LDA Model] → [Power BI]
```

**Automated hourly pipeline that uses your existing .pkl LDA model to classify news and update Power BI dashboards**

## 📁 **Project Structure**

```
topic_modelling/
├── 📁 models/                       # 🧠 Place your .pkl file here
│   └── lda_model.pkl               # ← YOUR TRAINED LDA MODEL
├── 📁 data/                         # CSV data storage
│   ├── raw_articles.csv            # Collected news articles
│   ├── processed_articles.csv      # Articles with topic classifications
│   └── powerbi/                    # Power BI export files
├── 📁 config/                       # Configuration
│   └── .env                        # NewsAPI key
├── newsapi_collector.py            # Step 1: NewsAPI → JSON
├── csv_pipeline.py                 # Step 2: JSON → CSV
├── lda_classifier.py               # Step 3: CSV → LDA Model
├── powerbi_exporter.py             # Step 4: Classified Data → Power BI
└── main_workflow.py                # Complete workflow orchestrator
```

## 🧠 **LDA Model Setup**

### **Step 1: Place Your .pkl File**

Put your trained LDA model file in the `models/` directory:

```bash
models/
└── lda_model.pkl    # ← Your trained model here
```

### **Step 2: Model File Requirements**

Your `.pkl` file should contain a dictionary with these keys:

```python
{
    'model': trained_lda_model,           # Scikit-learn LDA model
    'vectorizer': count_vectorizer,       # Fitted CountVectorizer
    'topics': {                           # Topic information
        0: {
            'label': 'Technology_AI',
            'keywords': ['artificial', 'intelligence', 'technology', ...]
        },
        1: {
            'label': 'Politics_Election',
            'keywords': ['election', 'political', 'voting', ...]
        },
        # ... more topics
    },
    'method': 'sklearn'                   # Model type
}
```

### **Step 3: Alternative Model Names**

The system will look for models in this order:
1. `models/lda_model.pkl` (default)
2. `models/latest_model.pkl`
3. `models/topic_model_*.pkl` (most recent)

## 🔄 **How the Workflow Works**

### **Step 1: [NewsAPI Request] → [Parse JSON]**
- `newsapi_collector.py` fetches articles from NewsAPI
- Parses JSON response and cleans article data
- Filters by keywords and time range

### **Step 2: [Parse JSON] → [Append to CSV]**
- `csv_pipeline.py` appends new articles to `data/raw_articles.csv`
- Removes duplicates based on article URL
- Maintains simple, readable CSV format

### **Step 3: [CSV] → [LDA Model]**
- `lda_classifier.py` loads your `.pkl` model
- Classifies unprocessed articles from CSV
- Saves results to `data/processed_articles.csv`

### **Step 4: [Classified Data] → [Power BI]**
- `powerbi_exporter.py` creates Power BI ready files
- Generates hourly trends, topic summaries, recent articles
- Exports to `data/powerbi/` directory

### **Complete Workflow**
- `main_workflow.py` orchestrates all steps
- Runs hourly on schedule or on-demand
- Handles errors and logging

## 🚀 Quick Start

### **Prerequisites**
- ✅ Python 3.8+
- ✅ Trained LDA model in `.pkl` format
- ✅ NewsAPI key (free from [newsapi.org](https://newsapi.org/))

### **1. Place Your Model**
```bash
# Put your trained LDA model here:
models/lda_model.pkl
```

### **2. Setup**
```bash
# Install dependencies
pip install -r requirements.txt

# Configure API key
cp config/.env.example config/.env
# Edit config/.env with your NewsAPI key

# Validate your model
python validate_model.py
```

### **3. Run Workflow**
```bash
# Run single cycle (test)
python main_workflow.py run

# Start hourly automated schedule
python main_workflow.py schedule

# Check pipeline status
python main_workflow.py status
```

### **4. Power BI Dashboard**
```bash
# Import these CSV files into Power BI:
data/powerbi/hourly_trends.csv       # Hourly topic trends
data/powerbi/recent_articles.csv     # Latest classified articles
data/powerbi/topic_summary.csv       # Topic statistics
data/powerbi/daily_trends.csv        # Daily topic trends
```

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Memory**: 4 GB RAM
- **Storage**: 5 GB free space
- **OS**: Windows 10, macOS 10.14+, or Linux (Ubuntu 18.04+)

### Recommended for Production
- **Python**: 3.9 or 3.10
- **Memory**: 8 GB RAM or more
- **Storage**: 20 GB free space (SSD preferred)
- **CPU**: Multi-core processor

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │   Processing    │    │     Output      │
│                 │    │                 │    │                 │
│ • NewsAPI       │───▶│ • Text Cleaning │───▶│ • CSV Files     │
│ • RSS Feeds     │    │ • Tokenization  │    │ • Power BI Data │
│ • Custom APIs   │    │ • Topic Modeling│    │ • Visualizations│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scheduling    │    │    Monitoring   │    │   Integration   │
│                 │    │                 │    │                 │
│ • Cron Jobs     │    │ • Logging       │    │ • Power BI      │
│ • Task Scheduler│    │ • Health Checks │    │ • Dashboards    │
│ • Cloud Triggers│    │ • Alerting      │    │ • APIs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Sample Output

The pipeline generates structured data perfect for analysis:

### Topic Distribution Example
| Topic ID | Topic Label | Keywords | Article Count | Confidence |
|----------|-------------|----------|---------------|------------|
| 0 | Technology_AI_Innovation | artificial, intelligence, technology | 45 | 0.87 |
| 1 | Politics_Election_Democracy | election, voting, political | 38 | 0.82 |
| 2 | Economy_Market_Financial | market, economic, financial | 42 | 0.79 |

### Time Series Trends
| Date | Topic | Article Count | Avg Confidence |
|------|-------|---------------|----------------|
| 2024-01-01 | Technology_AI | 12 | 0.85 |
| 2024-01-01 | Politics_Election | 8 | 0.78 |
| 2024-01-02 | Technology_AI | 15 | 0.88 |
