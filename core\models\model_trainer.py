"""
Topic modeling engine using Latent Dirichlet Allocation (LDA).
Extracts topics from preprocessed news articles and assigns topic labels.
"""

import pandas as pd
import numpy as np
import pickle
import joblib
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime

# Scikit-learn imports
from sklearn.feature_extraction.text import CountVectorizer, TfidfVectorizer
from sklearn.decomposition import LatentDirichletAllocation
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import perplexity

# Gensim imports (alternative LDA implementation)
try:
    import gensim
    from gensim import corpora
    from gensim.models import LdaModel
    from gensim.models.coherencemodel import CoherenceModel
    GENSIM_AVAILABLE = True
except ImportError:
    GENSIM_AVAILABLE = False

from loguru import logger
from .config import config
from .utils import load_articles, save_dataframe, validate_dataframe, get_latest_file

class TopicModeler:
    """LDA-based topic modeling for news articles."""
    
    def __init__(self, method: str = 'sklearn'):
        """
        Initialize topic modeler.
        
        Args:
            method: Method to use ('sklearn' or 'gensim')
        """
        self.method = method
        self.config = config.topic_modeling
        self.model = None
        self.vectorizer = None
        self.feature_names = None
        self.topics = {}
        
        # Model directory
        self.model_dir = Path(config.get('output.model_dir', 'models'))
        self.model_dir.mkdir(exist_ok=True)
    
    def _create_vectorizer(self) -> CountVectorizer:
        """Create and configure the text vectorizer."""
        vectorizer_params = {
            'max_features': self.config.get('max_features', 1000),
            'min_df': self.config.get('min_df', 2),
            'max_df': self.config.get('max_df', 0.8),
            'ngram_range': tuple(self.config.get('ngram_range', [1, 2])),
            'stop_words': 'english',
            'lowercase': True,
            'token_pattern': r'\b[a-zA-Z]{3,}\b'  # Only words with 3+ letters
        }
        
        return CountVectorizer(**vectorizer_params)
    
    def _fit_sklearn_lda(self, documents: List[str]) -> None:
        """
        Fit LDA model using scikit-learn.
        
        Args:
            documents: List of preprocessed documents
        """
        logger.info("Fitting LDA model using scikit-learn...")
        
        # Create and fit vectorizer
        self.vectorizer = self._create_vectorizer()
        doc_term_matrix = self.vectorizer.fit_transform(documents)
        self.feature_names = self.vectorizer.get_feature_names_out()
        
        logger.info(f"Created document-term matrix: {doc_term_matrix.shape}")
        logger.info(f"Vocabulary size: {len(self.feature_names)}")
        
        # Configure LDA
        lda_params = {
            'n_components': self.config.get('num_topics', 8),
            'random_state': self.config.get('random_state', 42),
            'max_iter': self.config.get('max_iter', 100),
            'learning_method': self.config.get('learning_method', 'batch'),
            'n_jobs': -1
        }
        
        # Fit LDA model
        self.model = LatentDirichletAllocation(**lda_params)
        self.model.fit(doc_term_matrix)
        
        # Calculate perplexity
        perplexity_score = self.model.perplexity(doc_term_matrix)
        logger.info(f"Model perplexity: {perplexity_score:.2f}")
        
        # Extract topics
        self._extract_sklearn_topics()
    
    def _fit_gensim_lda(self, documents: List[str]) -> None:
        """
        Fit LDA model using Gensim.
        
        Args:
            documents: List of preprocessed documents
        """
        if not GENSIM_AVAILABLE:
            raise ImportError("Gensim not available. Install with: pip install gensim")
        
        logger.info("Fitting LDA model using Gensim...")
        
        # Tokenize documents
        tokenized_docs = [doc.split() for doc in documents]
        
        # Create dictionary and corpus
        dictionary = corpora.Dictionary(tokenized_docs)
        
        # Filter extremes
        dictionary.filter_extremes(
            no_below=self.config.get('min_df', 2),
            no_above=self.config.get('max_df', 0.8)
        )
        
        # Create corpus
        corpus = [dictionary.doc2bow(doc) for doc in tokenized_docs]
        
        logger.info(f"Dictionary size: {len(dictionary)}")
        logger.info(f"Corpus size: {len(corpus)}")
        
        # Fit LDA model
        self.model = LdaModel(
            corpus=corpus,
            id2word=dictionary,
            num_topics=self.config.get('num_topics', 8),
            random_state=self.config.get('random_state', 42),
            passes=self.config.get('max_iter', 100),
            alpha='auto',
            per_word_topics=True
        )
        
        # Calculate coherence
        coherence_model = CoherenceModel(
            model=self.model,
            texts=tokenized_docs,
            dictionary=dictionary,
            coherence='c_v'
        )
        coherence_score = coherence_model.get_coherence()
        logger.info(f"Model coherence: {coherence_score:.3f}")
        
        # Store additional objects
        self.dictionary = dictionary
        self.corpus = corpus
        
        # Extract topics
        self._extract_gensim_topics()
    
    def _extract_sklearn_topics(self) -> None:
        """Extract topic keywords from sklearn LDA model."""
        n_top_words = 10
        
        for topic_idx, topic in enumerate(self.model.components_):
            top_words_idx = topic.argsort()[-n_top_words:][::-1]
            top_words = [self.feature_names[i] for i in top_words_idx]
            top_weights = [topic[i] for i in top_words_idx]
            
            self.topics[topic_idx] = {
                'keywords': top_words,
                'weights': top_weights,
                'label': self._generate_topic_label(top_words[:3])
            }
        
        logger.info(f"Extracted {len(self.topics)} topics")
        for topic_id, topic_info in self.topics.items():
            logger.info(f"Topic {topic_id}: {topic_info['label']} - {', '.join(topic_info['keywords'][:5])}")
    
    def _extract_gensim_topics(self) -> None:
        """Extract topic keywords from Gensim LDA model."""
        for topic_idx in range(self.model.num_topics):
            topic_words = self.model.show_topic(topic_idx, topn=10)
            keywords = [word for word, _ in topic_words]
            weights = [weight for _, weight in topic_words]
            
            self.topics[topic_idx] = {
                'keywords': keywords,
                'weights': weights,
                'label': self._generate_topic_label(keywords[:3])
            }
        
        logger.info(f"Extracted {len(self.topics)} topics")
        for topic_id, topic_info in self.topics.items():
            logger.info(f"Topic {topic_id}: {topic_info['label']} - {', '.join(topic_info['keywords'][:5])}")
    
    def _generate_topic_label(self, top_words: List[str]) -> str:
        """
        Generate a human-readable label for a topic.
        
        Args:
            top_words: Top words for the topic
            
        Returns:
            Topic label
        """
        # Simple approach: join top 3 words
        return "_".join(top_words[:3]).title()
    
    def fit(self, documents: List[str]) -> None:
        """
        Fit the topic model on documents.
        
        Args:
            documents: List of preprocessed documents
        """
        if not documents:
            raise ValueError("No documents provided for training")
        
        logger.info(f"Training topic model on {len(documents)} documents using {self.method}")
        
        if self.method == 'gensim':
            self._fit_gensim_lda(documents)
        else:
            self._fit_sklearn_lda(documents)
    
    def predict_topics(self, documents: List[str]) -> List[Dict[str, Any]]:
        """
        Predict topics for new documents.
        
        Args:
            documents: List of preprocessed documents
            
        Returns:
            List of topic predictions
        """
        if self.model is None:
            raise ValueError("Model not fitted. Call fit() first.")
        
        predictions = []
        
        if self.method == 'gensim':
            predictions = self._predict_gensim(documents)
        else:
            predictions = self._predict_sklearn(documents)
        
        return predictions
    
    def _predict_sklearn(self, documents: List[str]) -> List[Dict[str, Any]]:
        """Predict topics using sklearn model."""
        # Transform documents
        doc_term_matrix = self.vectorizer.transform(documents)
        
        # Get topic distributions
        topic_distributions = self.model.transform(doc_term_matrix)
        
        predictions = []
        for i, distribution in enumerate(topic_distributions):
            # Get dominant topic
            dominant_topic = np.argmax(distribution)
            confidence = distribution[dominant_topic]
            
            prediction = {
                'document_index': i,
                'topic_id': int(dominant_topic),
                'topic_label': self.topics[dominant_topic]['label'],
                'topic_keywords': self.topics[dominant_topic]['keywords'][:5],
                'confidence': float(confidence),
                'topic_distribution': distribution.tolist()
            }
            predictions.append(prediction)
        
        return predictions
    
    def _predict_gensim(self, documents: List[str]) -> List[Dict[str, Any]]:
        """Predict topics using Gensim model."""
        predictions = []
        
        for i, doc in enumerate(documents):
            # Tokenize and convert to bow
            tokens = doc.split()
            bow = self.dictionary.doc2bow(tokens)
            
            # Get topic distribution
            topic_distribution = self.model.get_document_topics(bow, minimum_probability=0)
            topic_probs = dict(topic_distribution)
            
            # Get dominant topic
            if topic_probs:
                dominant_topic = max(topic_probs.keys(), key=lambda k: topic_probs[k])
                confidence = topic_probs[dominant_topic]
            else:
                dominant_topic = 0
                confidence = 0.0
            
            prediction = {
                'document_index': i,
                'topic_id': int(dominant_topic),
                'topic_label': self.topics[dominant_topic]['label'],
                'topic_keywords': self.topics[dominant_topic]['keywords'][:5],
                'confidence': float(confidence),
                'topic_distribution': [topic_probs.get(j, 0.0) for j in range(self.model.num_topics)]
            }
            predictions.append(prediction)
        
        return predictions
    
    def save_model(self, filename: str = None) -> str:
        """
        Save the trained model to disk.
        
        Args:
            filename: Optional filename
            
        Returns:
            Path to saved model
        """
        if self.model is None:
            raise ValueError("No model to save")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if filename is None:
            filename = f"topic_model_{self.method}_{timestamp}.pkl"
        
        model_path = self.model_dir / filename
        
        # Save model and related objects
        model_data = {
            'model': self.model,
            'vectorizer': self.vectorizer,
            'topics': self.topics,
            'method': self.method,
            'config': self.config,
            'feature_names': self.feature_names
        }
        
        if self.method == 'gensim':
            model_data['dictionary'] = self.dictionary
            model_data['corpus'] = self.corpus
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        logger.info(f"Model saved to: {model_path}")
        return str(model_path)
    
    def load_model(self, model_path: str) -> None:
        """
        Load a trained model from disk.
        
        Args:
            model_path: Path to saved model
        """
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        self.model = model_data['model']
        self.vectorizer = model_data.get('vectorizer')
        self.topics = model_data['topics']
        self.method = model_data['method']
        self.feature_names = model_data.get('feature_names')
        
        if self.method == 'gensim':
            self.dictionary = model_data.get('dictionary')
            self.corpus = model_data.get('corpus')
        
        logger.info(f"Model loaded from: {model_path}")
    
    def get_topic_summary(self) -> pd.DataFrame:
        """
        Get a summary of all topics.
        
        Returns:
            DataFrame with topic information
        """
        if not self.topics:
            return pd.DataFrame()
        
        summary_data = []
        for topic_id, topic_info in self.topics.items():
            summary_data.append({
                'topic_id': topic_id,
                'topic_label': topic_info['label'],
                'top_keywords': ', '.join(topic_info['keywords'][:5]),
                'all_keywords': ', '.join(topic_info['keywords'])
            })
        
        return pd.DataFrame(summary_data)

def main():
    """Main function for running topic modeling."""
    logger.info("Starting topic modeling...")
    
    # Load preprocessed articles
    data_dir = config.get('output.data_dir', 'data')
    processed_file = config.get('output.processed_articles_file', 'processed_articles.csv')
    input_path = Path(data_dir) / processed_file
    
    if not input_path.exists():
        # Try to find the latest processed articles file
        latest_file = get_latest_file(data_dir, "processed_articles*.csv")
        if latest_file:
            input_path = Path(latest_file)
        else:
            logger.error("No processed articles file found. Please run preprocessing first.")
            return
    
    # Load articles
    df = load_articles(str(input_path))
    if df.empty or 'cleaned_text' not in df.columns:
        logger.error("No preprocessed articles found")
        return
    
    # Filter articles with sufficient content
    df = df[df['cleaned_text'].str.len() > 50]  # At least 50 characters
    
    if len(df) < 10:
        logger.error("Insufficient articles for topic modeling")
        return
    
    logger.info(f"Training topic model on {len(df)} articles")
    
    # Initialize and train model
    modeler = TopicModeler(method='sklearn')  # Use sklearn by default
    documents = df['cleaned_text'].tolist()
    modeler.fit(documents)
    
    # Predict topics for all documents
    predictions = modeler.predict_topics(documents)
    
    # Add predictions to dataframe
    for i, pred in enumerate(predictions):
        df.loc[df.index[i], 'topic_id'] = pred['topic_id']
        df.loc[df.index[i], 'topic_label'] = pred['topic_label']
        df.loc[df.index[i], 'topic_keywords'] = ', '.join(pred['topic_keywords'])
        df.loc[df.index[i], 'topic_confidence'] = pred['confidence']
    
    # Save results
    output_file = config.get('output.topic_trends_file', 'topic_trends.csv')
    output_path = Path(data_dir) / output_file
    save_dataframe(df, str(output_path))
    
    # Save model
    model_path = modeler.save_model()
    
    # Save topic summary
    topic_summary = modeler.get_topic_summary()
    summary_path = Path(data_dir) / 'topic_summary.csv'
    save_dataframe(topic_summary, str(summary_path), add_timestamp=False)
    
    logger.info(f"Topic modeling completed. Results saved to {output_path}")
    logger.info(f"Model saved to {model_path}")
    logger.info(f"Topic summary saved to {summary_path}")

if __name__ == "__main__":
    main()
