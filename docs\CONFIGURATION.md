# Configuration Guide

This document explains how to configure the Topic Modeling Pipeline for your specific needs.

## Configuration Files

### 1. `config.yaml` - Main Configuration

The main configuration file controls all aspects of the pipeline:

#### News API Configuration
```yaml
news_api:
  api_key: "YOUR_NEWS_API_KEY_HERE"  # Get from https://newsapi.org/
  base_url: "https://newsapi.org/v2/everything"
  query_keywords: ["India", "election", "economy"]  # Topics to search for
  language: "en"
  sort_by: "publishedAt"  # publishedAt, relevancy, popularity
  page_size: 100
  max_pages: 5
```

#### Text Preprocessing
```yaml
preprocessing:
  min_word_length: 3        # Minimum word length to keep
  max_word_length: 20       # Maximum word length to keep
  remove_stopwords: true    # Remove common words
  lemmatize: true          # Convert words to base form
  remove_numbers: true     # Remove numeric tokens
  remove_punctuation: true # Remove punctuation
  custom_stopwords: ["said", "says", "according"]  # Additional words to remove
```

#### Topic Modeling
```yaml
topic_modeling:
  num_topics: 8            # Number of topics to extract
  max_features: 1000       # Maximum vocabulary size
  min_df: 2               # Minimum document frequency
  max_df: 0.8             # Maximum document frequency
  ngram_range: [1, 2]     # Use unigrams and bigrams
  random_state: 42        # For reproducible results
  max_iter: 100           # Maximum training iterations
```

### 2. `.env` - Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# News API Key (required)
NEWS_API_KEY=your_actual_api_key_here

# Optional: Alternative news sources
GNEWS_API_KEY=your_gnews_api_key_here

# Logging level override
LOG_LEVEL=INFO
```

## Getting API Keys

### NewsAPI
1. Visit [https://newsapi.org/](https://newsapi.org/)
2. Sign up for a free account
3. Copy your API key
4. Add it to your `.env` file

**Free tier limits:**
- 1,000 requests per day
- 100 articles per request
- Development use only

### Alternative Sources

If you exceed NewsAPI limits, the pipeline can use RSS feeds:

```yaml
rss_feeds:
  - "https://feeds.bbci.co.uk/news/rss.xml"
  - "https://rss.cnn.com/rss/edition.rss"
  - "https://feeds.reuters.com/reuters/topNews"
```

## Customizing Search Keywords

Modify the `query_keywords` in `config.yaml` to focus on your topics of interest:

```yaml
news_api:
  query_keywords: 
    - "artificial intelligence"
    - "machine learning"
    - "technology"
    - "startup"
```

## Topic Modeling Parameters

### Number of Topics
- **Too few topics**: Overly broad, mixed themes
- **Too many topics**: Overly specific, fragmented themes
- **Recommended**: Start with 5-10 topics, adjust based on results

### Vocabulary Size (`max_features`)
- **Smaller vocabulary**: Faster processing, may miss nuances
- **Larger vocabulary**: More detailed analysis, slower processing
- **Recommended**: 1000-5000 for news articles

### Document Frequency Filters
- `min_df`: Remove words that appear in very few documents
- `max_df`: Remove words that appear in too many documents
- **Recommended**: `min_df=2`, `max_df=0.8`

## Output Configuration

```yaml
output:
  data_dir: "data"                          # Data directory
  raw_articles_file: "raw_articles.csv"    # Raw articles filename
  processed_articles_file: "processed_articles.csv"
  topic_trends_file: "topic_trends.csv"    # Final results
  powerbi_export_file: "powerbi_data.csv"  # Power BI export
  model_dir: "models"                       # Saved models directory
```

## Logging Configuration

```yaml
logging:
  level: "INFO"              # DEBUG, INFO, WARNING, ERROR
  log_file: "logs/pipeline.log"
  max_log_size: "10MB"       # Log rotation size
  backup_count: 5            # Number of backup log files
```

## Performance Tuning

### For Large Datasets
```yaml
topic_modeling:
  max_features: 5000         # Increase vocabulary
  learning_method: "online"  # Use online learning
  batch_size: 128           # Smaller batches for memory efficiency
```

### For Fast Processing
```yaml
preprocessing:
  lemmatize: false          # Skip lemmatization
topic_modeling:
  max_features: 500         # Reduce vocabulary
  max_iter: 50             # Fewer iterations
```

## Validation

After configuration changes, validate your setup:

```bash
# Test configuration loading
python -c "from src.config import config; print('Config loaded successfully')"

# Test API connection
python -c "from src.data_ingestion import NewsAPICollector; collector = NewsAPICollector(); print('API configured')"

# Run a quick test
python run_pipeline.py --step ingestion
```

## Common Issues

### API Key Not Working
- Verify the key is correct in `.env`
- Check if you've exceeded daily limits
- Ensure the key has proper permissions

### Too Few Articles
- Broaden your search keywords
- Increase `max_pages` in config
- Check date range (NewsAPI free tier has limitations)

### Poor Topic Quality
- Adjust `num_topics` parameter
- Modify preprocessing settings
- Add domain-specific stopwords
- Increase `min_df` to remove rare words

### Memory Issues
- Reduce `max_features`
- Use smaller `page_size`
- Process data in batches

## Environment-Specific Configurations

### Development
```yaml
news_api:
  page_size: 20
  max_pages: 2
topic_modeling:
  num_topics: 5
  max_iter: 50
```

### Production
```yaml
news_api:
  page_size: 100
  max_pages: 10
topic_modeling:
  num_topics: 10
  max_iter: 200
logging:
  level: "WARNING"
```

## Next Steps

1. Configure your API keys
2. Customize search keywords for your domain
3. Run a test with: `python run_pipeline.py`
4. Adjust topic modeling parameters based on results
5. Set up scheduling (see SCHEDULING.md)
