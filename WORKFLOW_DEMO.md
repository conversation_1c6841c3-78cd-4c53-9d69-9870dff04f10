# 🚀 **Your Exact Workflow Implementation**

## 📋 **Complete Implementation Summary**

Your web application workflow has been **fully implemented** with the exact specifications you requested:

### **🔄 Your Workflow:**
```
NewsAPI (timestamp-based) → raw_articles → preprocessed_articles → topic_results → Power BI DirectQuery
```

## ⚙️ **1. Configuration Setup**

### **Step 1: Set Your NewsAPI Key**
```bash
# Option A: Environment Variable (Recommended)
set NEWS_API_KEY=your_actual_newsapi_key_here

# Option B: Create .env file
cp .env.example .env
# Edit .env and add: NEWS_API_KEY=your_actual_newsapi_key_here
```

### **Step 2: Configure Fetch Interval**
Edit the fetch interval in your pipeline (currently set to **15 minutes**):
```python
# In automated_pipeline.py, line 47
fetch_interval_minutes: int = 15  # ← Change this value
```

### **Step 3: Configure Power BI Refresh Interval**
Set your Power BI refresh interval (currently set to **5 minutes**):
```python
# In automated_pipeline.py, line 48
powerbi_refresh_minutes: int = 5  # ← Change this value
```

## 🗄️ **2. Database Tables (Exactly as Requested)**

### **Table 1: `raw_articles`** (Stores original NewsAPI JSON)
```sql
- article_id (unique identifier)
- title, description, content
- source_name, author, url
- published_at, fetched_at
- content_hash (for deduplication)
- raw_json (original API response)
- processing_status (pending/processed/failed)
```

### **Table 2: `preprocessed_articles`** (Stores cleaned text)
```sql
- preprocessed_id (auto-increment)
- article_id (foreign key to raw_articles)
- cleaned_text, tokens, lemmatized_text
- word_count, sentence_count
- preprocessing_timestamp, preprocessing_duration_ms
```

### **Table 3: `topic_results`** (Stores LDA model output)
```sql
- result_id (auto-increment)
- article_id, preprocessed_id (foreign keys)
- topic_id, confidence
- topic_distribution (JSON array of all probabilities)
- topic_keywords (JSON array of keywords)
- classification_timestamp, processing_duration_ms
```

## 🔄 **3. Automated Pipeline Flow**

### **Every X Minutes (Configurable):**

1. **📡 Timestamp-Based News Fetching**
   - Gets last fetch timestamp from SQL Server
   - Fetches only NEW articles since last timestamp
   - Stores raw JSON in `raw_articles` table
   - Uses content hash for deduplication

2. **🧹 Text Preprocessing**
   - Reads articles with `processing_status = 'pending'`
   - Applies comprehensive NLP preprocessing
   - Stores results in `preprocessed_articles` table
   - Updates `processing_status` to 'processed'

3. **🧠 LDA Topic Classification**
   - Reads unclassified articles from `preprocessed_articles`
   - Applies your trained LDA model
   - Extracts topic distributions and confidence scores
   - Stores results in `topic_results` table

4. **📊 Power BI DirectQuery**
   - Automatically refreshes every Y minutes
   - Reads live data directly from SQL Server
   - No manual CSV exports needed

## 🚀 **4. Running Your Pipeline**

### **Setup Database:**
```bash
# Create database and tables
sqlcmd -S localhost -Q "CREATE DATABASE TopicModelingDB"
sqlcmd -S localhost -d TopicModelingDB -i database/schema.sql
sqlcmd -S localhost -d TopicModelingDB -i database/powerbi_views.sql
```

### **Single Run (Test):**
```bash
python automated_pipeline.py --single-run
```

### **Continuous Operation:**
```bash
# Run with 15-minute intervals
python automated_pipeline.py --fetch-interval 15

# Run with custom intervals
python automated_pipeline.py --fetch-interval 10 --powerbi-refresh 3
```

### **Limited Cycles (Testing):**
```bash
# Run only 5 cycles then stop
python automated_pipeline.py --max-cycles 5
```

## 📊 **5. Power BI Integration**

### **DirectQuery Setup:**
1. Open Power BI Desktop
2. Get Data → SQL Server Database
3. **Server:** localhost (or your SQL Server)
4. **Database:** TopicModelingDB
5. **Mode:** DirectQuery ← **IMPORTANT!**
6. Select these optimized views:
   - `vw_topic_summary` (topic overview)
   - `vw_kpi_metrics` (key metrics)
   - `vw_hourly_trends` (time series)
   - `vw_realtime_articles` (latest articles)

### **Refresh Settings:**
- **DirectQuery:** Automatic refresh every 5 minutes (configurable)
- **No manual refresh needed** - data updates automatically

## 🔍 **6. Monitoring & Status**

### **Check Pipeline Status:**
```bash
python automated_pipeline.py --status
```

### **View Logs:**
```bash
tail -f logs/automated_pipeline.log
```

### **Check Database Status:**
```sql
-- Check article counts
SELECT 
    COUNT(*) as raw_articles,
    COUNT(CASE WHEN processing_status = 'processed' THEN 1 END) as processed,
    COUNT(CASE WHEN processing_status = 'pending' THEN 1 END) as pending
FROM raw_articles;

-- Check preprocessing status
SELECT COUNT(*) as preprocessed_articles FROM preprocessed_articles;

-- Check classification status
SELECT COUNT(*) as classified_articles FROM topic_results;
```

## ⚡ **7. Key Features Implemented**

✅ **Timestamp-based filtering** - Only fetches NEW articles
✅ **Content hash deduplication** - Prevents duplicate processing
✅ **Fault tolerance** - Handles API failures gracefully
✅ **Zero manual intervention** - Fully automated pipeline
✅ **Real-time Power BI** - DirectQuery with live updates
✅ **Comprehensive logging** - Full audit trail
✅ **Performance tracking** - Processing duration metrics
✅ **Scalable architecture** - Handles high article volumes

## 🎯 **8. Customization Options**

### **Fetch Interval:**
- **15 minutes** (default) - Good balance of freshness and API limits
- **5 minutes** - More real-time, higher API usage
- **30 minutes** - Less frequent, lower API usage

### **Power BI Refresh:**
- **5 minutes** (default) - Near real-time dashboards
- **1 minute** - Maximum real-time (if performance allows)
- **15 minutes** - Less frequent updates

### **Query Keywords:**
Edit in `automated_pipeline.py` line 156:
```python
query="technology OR politics OR business OR economy OR health OR science"
```

## 🔧 **9. Troubleshooting**

### **Common Issues:**
- **"No articles found"** → Normal during off-peak hours
- **"API rate limit"** → Increase fetch interval
- **"Model not found"** → Place `lda_model.pkl` in `models/` directory
- **"SQL connection failed"** → Check `config/database.json`

### **Performance Optimization:**
- **High volume:** Increase fetch interval to 30+ minutes
- **Low latency:** Decrease Power BI refresh to 1-3 minutes
- **API limits:** Use multiple API keys or reduce page size

## 🎉 **Your Pipeline is Ready!**

Your exact workflow is now **fully implemented** and ready for production:

1. ✅ **Timestamp-based news fetching** every X minutes
2. ✅ **Raw JSON storage** in `raw_articles` table
3. ✅ **Comprehensive preprocessing** with NLP pipeline
4. ✅ **LDA topic classification** with confidence scores
5. ✅ **Real-time Power BI DirectQuery** visualization
6. ✅ **Zero manual intervention** - fully automated
7. ✅ **Fault tolerance** and error handling
8. ✅ **Performance monitoring** and logging

**Start your pipeline now:**
```bash
python automated_pipeline.py --fetch-interval 15 --powerbi-refresh 5
```
