# Scheduling Guide

This document explains how to set up automated scheduling for the Topic Modeling Pipeline.

## Overview

The pipeline can be scheduled to run automatically at regular intervals to:
- Collect fresh news articles
- Update topic models with new data
- Refresh Power BI dashboards
- Monitor trending topics over time

## Scheduling Options

### 1. Windows Task Scheduler (Windows)

#### Create a Basic Task

1. **Open Task Scheduler**
   - Press `Win + R`, type `taskschd.msc`, press Enter

2. **Create Basic Task**
   - Click "Create Basic Task" in the right panel
   - Name: "Topic Modeling Pipeline"
   - Description: "Automated news topic analysis"

3. **Set Trigger**
   - Choose frequency: Daily, Weekly, or Monthly
   - Set start time (recommended: early morning when APIs are less busy)
   - For daily: Choose time like 6:00 AM

4. **Set Action**
   - Choose "Start a program"
   - Program: `python`
   - Arguments: `run_pipeline.py`
   - Start in: `C:\Users\<USER>\topic_modelling` (your project directory)

#### Advanced Task Configuration

Create a batch file for better control:

**`run_pipeline.bat`:**
```batch
@echo off
cd /d "C:\Users\<USER>\topic_modelling"
python run_pipeline.py > logs\scheduled_run.log 2>&1
```

Then schedule the batch file instead of Python directly.

#### PowerShell Script (Alternative)

**`run_pipeline.ps1`:**
```powershell
Set-Location "C:\Users\<USER>\topic_modelling"
python run_pipeline.py
if ($LASTEXITCODE -eq 0) {
    Write-Host "Pipeline completed successfully"
} else {
    Write-Host "Pipeline failed with exit code $LASTEXITCODE"
}
```

### 2. Cron Jobs (Linux/macOS)

#### Edit Crontab
```bash
crontab -e
```

#### Daily at 6:00 AM
```bash
0 6 * * * cd /path/to/topic_modelling && python run_pipeline.py >> logs/cron.log 2>&1
```

#### Weekly on Sundays at 7:00 AM
```bash
0 7 * * 0 cd /path/to/topic_modelling && python run_pipeline.py >> logs/cron.log 2>&1
```

#### With virtual environment
```bash
0 6 * * * cd /path/to/topic_modelling && /path/to/venv/bin/python run_pipeline.py >> logs/cron.log 2>&1
```

### 3. Python Scheduler (Cross-platform)

Create a dedicated scheduler script:

**`scheduler.py`:**
```python
import schedule
import time
import subprocess
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_pipeline():
    """Run the topic modeling pipeline."""
    logger.info(f"Starting scheduled pipeline run at {datetime.now()}")
    
    try:
        result = subprocess.run(
            ["python", "run_pipeline.py"],
            capture_output=True,
            text=True,
            timeout=3600  # 1 hour timeout
        )
        
        if result.returncode == 0:
            logger.info("Pipeline completed successfully")
        else:
            logger.error(f"Pipeline failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        logger.error("Pipeline timed out after 1 hour")
    except Exception as e:
        logger.error(f"Error running pipeline: {e}")

# Schedule the pipeline
schedule.every().day.at("06:00").do(run_pipeline)
# schedule.every().week.do(run_pipeline)  # Weekly alternative
# schedule.every().hour.do(run_pipeline)  # Hourly alternative

if __name__ == "__main__":
    logger.info("Scheduler started")
    
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute
```

Run the scheduler:
```bash
python scheduler.py
```

## Scheduling Strategies

### 1. Incremental Updates (Recommended)

**Daily Schedule:**
- **6:00 AM**: Full pipeline run
- **12:00 PM**: Data ingestion only
- **6:00 PM**: Data ingestion only

```bash
# Full pipeline daily at 6 AM
0 6 * * * cd /path/to/project && python run_pipeline.py

# Ingestion only at noon and 6 PM
0 12 * * * cd /path/to/project && python run_pipeline.py --step ingestion
0 18 * * * cd /path/to/project && python run_pipeline.py --step ingestion
```

### 2. Weekly Full Refresh

**Weekly Schedule:**
- **Sunday 6:00 AM**: Full pipeline with model retraining
- **Daily 8:00 AM**: Data ingestion and export only

### 3. High-Frequency Monitoring

**Hourly Schedule:**
- **Every hour**: Data ingestion
- **Every 6 hours**: Full pipeline

## Configuration for Scheduling

Update `config.yaml` for automated runs:

```yaml
scheduling:
  enabled: true
  frequency: "daily"
  time: "06:00"
  
# Optimize for automated runs
news_api:
  page_size: 100
  max_pages: 3  # Reduce for frequent runs

logging:
  level: "INFO"
  log_file: "logs/scheduled_pipeline.log"
```

## Monitoring Scheduled Runs

### 1. Log Analysis

Create a log monitoring script:

**`monitor_logs.py`:**
```python
import re
from pathlib import Path
from datetime import datetime, timedelta

def analyze_recent_runs(log_file="logs/pipeline.log", days=7):
    """Analyze pipeline runs from the last N days."""
    
    if not Path(log_file).exists():
        print(f"Log file not found: {log_file}")
        return
    
    cutoff_date = datetime.now() - timedelta(days=days)
    successful_runs = 0
    failed_runs = 0
    
    with open(log_file, 'r') as f:
        for line in f:
            if "PIPELINE COMPLETED SUCCESSFULLY" in line:
                successful_runs += 1
            elif "Pipeline failed" in line:
                failed_runs += 1
    
    print(f"Pipeline runs in last {days} days:")
    print(f"  Successful: {successful_runs}")
    print(f"  Failed: {failed_runs}")
    print(f"  Success rate: {successful_runs/(successful_runs+failed_runs)*100:.1f}%")

if __name__ == "__main__":
    analyze_recent_runs()
```

### 2. Email Notifications

Add email notifications for failures:

**`email_notifier.py`:**
```python
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def send_failure_notification(error_message):
    """Send email notification on pipeline failure."""
    
    # Email configuration
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    sender_email = "<EMAIL>"
    sender_password = "your_app_password"
    recipient_email = "<EMAIL>"
    
    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = recipient_email
    message["Subject"] = "Topic Modeling Pipeline Failed"
    
    body = f"""
    The topic modeling pipeline has failed with the following error:
    
    {error_message}
    
    Please check the logs and restart the pipeline if necessary.
    
    Time: {datetime.now()}
    """
    
    message.attach(MIMEText(body, "plain"))
    
    # Send email
    try:
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(sender_email, sender_password)
        text = message.as_string()
        server.sendmail(sender_email, recipient_email, text)
        server.quit()
        print("Failure notification sent")
    except Exception as e:
        print(f"Failed to send notification: {e}")
```

## Best Practices

### 1. Resource Management
- **API Limits**: Respect NewsAPI daily limits (1000 requests/day for free tier)
- **Memory Usage**: Monitor memory consumption for large datasets
- **Disk Space**: Implement log rotation and old file cleanup

### 2. Error Handling
- **Retry Logic**: Implement retries for network failures
- **Graceful Degradation**: Fall back to RSS feeds if API fails
- **Alerting**: Set up notifications for persistent failures

### 3. Data Management
- **Backup**: Regularly backup models and processed data
- **Cleanup**: Remove old temporary files
- **Archiving**: Archive old articles to prevent data bloat

### 4. Performance Optimization
- **Off-peak Hours**: Schedule during low-traffic periods
- **Incremental Processing**: Only process new articles when possible
- **Parallel Processing**: Use multiple cores for large datasets

## Troubleshooting

### Common Issues

1. **Pipeline Hangs**
   - Add timeout to subprocess calls
   - Monitor memory usage
   - Check for infinite loops in processing

2. **API Rate Limiting**
   - Reduce `page_size` and `max_pages`
   - Add delays between requests
   - Use multiple API keys if available

3. **Disk Space Issues**
   - Implement log rotation
   - Clean up old model files
   - Monitor data directory size

4. **Memory Errors**
   - Process data in smaller batches
   - Reduce vocabulary size
   - Use more efficient data structures

### Debugging Scheduled Runs

1. **Test Manually First**
   ```bash
   python run_pipeline.py
   ```

2. **Check Environment Variables**
   ```bash
   python -c "import os; print(os.environ.get('NEWS_API_KEY', 'Not set'))"
   ```

3. **Verify File Permissions**
   ```bash
   ls -la run_pipeline.py
   ```

4. **Check Python Path**
   ```bash
   which python
   python --version
   ```

## Next Steps

1. Choose your scheduling method based on your operating system
2. Set up basic daily scheduling
3. Implement monitoring and alerting
4. Test the scheduled runs thoroughly
5. Optimize frequency based on your needs and API limits
