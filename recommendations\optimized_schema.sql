-- =====================================================
-- OPTIMIZED SQL SERVER SCHEMA FOR HIGH-THROUGHPUT
-- Enhanced schema design for scalable topic modeling pipeline
-- =====================================================

-- =====================================================
-- 1. PARTITIONED TABLES FOR TIME-SERIES DATA
-- =====================================================

-- Create partition function for monthly partitioning
CREATE PARTITION FUNCTION pf_monthly_partition (DATETIME2)
AS RANGE RIGHT FOR VALUES (
    '2024-01-01', '2024-02-01', '2024-03-01', '2024-04-01',
    '2024-05-01', '2024-06-01', '2024-07-01', '2024-08-01',
    '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01',
    '2025-01-01', '2025-02-01', '2025-03-01', '2025-04-01'
);

-- Create partition scheme
CREATE PARTITION SCHEME ps_monthly_partition
AS PARTITION pf_monthly_partition
ALL TO ([PRIMARY]);

-- =====================================================
-- 2. OPTIMIZED CORE TABLES WITH PARTITIONING
-- =====================================================

-- Enhanced articles table with partitioning
CREATE TABLE articles_partitioned (
    article_id BIGINT IDENTITY(1,1) NOT NULL,
    external_id NVARCHAR(100) NOT NULL,           -- API-provided ID
    title NVARCHAR(500) NOT NULL,
    description NVARCHAR(MAX),
    content NVARCHAR(MAX),
    full_text NVARCHAR(MAX),
    source_name NVARCHAR(100) NOT NULL,
    source_id NVARCHAR(50),
    author NVARCHAR(200),
    url NVARCHAR(1000),
    url_to_image NVARCHAR(1000),
    published_at DATETIME2 NOT NULL,
    collected_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    content_hash AS HASHBYTES('SHA2_256', CONCAT(title, content)) PERSISTED,
    language NVARCHAR(10) DEFAULT 'en',
    country NVARCHAR(10),
    category NVARCHAR(50),
    word_count AS LEN(content) - LEN(REPLACE(content, ' ', '')) + 1 PERSISTED,
    
    -- Partitioning key
    CONSTRAINT PK_articles_partitioned PRIMARY KEY (article_id, published_at),
    
    -- Unique constraints
    CONSTRAINT UQ_articles_external_id UNIQUE (external_id),
    CONSTRAINT UQ_articles_content_hash UNIQUE (content_hash),
    
    -- Indexes for high-performance queries
    INDEX IX_articles_published_source NONCLUSTERED (published_at DESC, source_name),
    INDEX IX_articles_collected_at NONCLUSTERED (collected_at DESC),
    INDEX IX_articles_content_hash NONCLUSTERED (content_hash),
    INDEX IX_articles_source_published NONCLUSTERED (source_name, published_at DESC)
    
) ON ps_monthly_partition(published_at);

-- Enhanced topic classifications with better indexing
CREATE TABLE article_topics_optimized (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    article_id BIGINT NOT NULL,
    topic_id INT NOT NULL,
    confidence DECIMAL(8,6) NOT NULL,              -- Higher precision
    all_probabilities NVARCHAR(MAX),               -- JSON array
    classified_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    model_version NVARCHAR(20) DEFAULT '1.0',
    processing_time_ms INT,                        -- Track performance
    
    -- Foreign key with partitioning consideration
    CONSTRAINT FK_article_topics_article 
        FOREIGN KEY (article_id) REFERENCES articles_partitioned(article_id),
    
    -- Unique constraint - one classification per article per model version
    CONSTRAINT UQ_article_topics_unique UNIQUE (article_id, model_version),
    
    -- Optimized indexes for common query patterns
    INDEX IX_article_topics_topic_confidence NONCLUSTERED (topic_id, confidence DESC),
    INDEX IX_article_topics_classified_at NONCLUSTERED (classified_at DESC),
    INDEX IX_article_topics_confidence_range NONCLUSTERED (confidence) 
        WHERE confidence >= 0.3,  -- Filtered index for classified articles
    INDEX IX_article_topics_composite NONCLUSTERED (topic_id, classified_at DESC, confidence DESC)
);

-- =====================================================
-- 3. COLUMNSTORE INDEXES FOR ANALYTICS
-- =====================================================

-- Create columnstore index for analytical queries
CREATE NONCLUSTERED COLUMNSTORE INDEX IX_articles_columnstore
ON articles_partitioned (
    article_id, source_name, published_at, collected_at, 
    language, country, category, word_count
);

CREATE NONCLUSTERED COLUMNSTORE INDEX IX_topics_columnstore
ON article_topics_optimized (
    article_id, topic_id, confidence, classified_at, model_version
);

-- =====================================================
-- 4. REAL-TIME AGGREGATION TABLES
-- =====================================================

-- Real-time hourly aggregations (updated via triggers or scheduled jobs)
CREATE TABLE topic_metrics_realtime (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    hour_bucket DATETIME2 NOT NULL,               -- Truncated to hour
    topic_id INT NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(8,6) NOT NULL,
    confidence_std DECIMAL(8,6) DEFAULT 0,
    total_word_count BIGINT DEFAULT 0,
    unique_sources INT DEFAULT 0,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Unique constraint for upserts
    CONSTRAINT UQ_topic_metrics_hour_topic UNIQUE (hour_bucket, topic_id),
    
    -- Indexes for real-time queries
    INDEX IX_topic_metrics_hour_desc NONCLUSTERED (hour_bucket DESC, topic_id),
    INDEX IX_topic_metrics_topic_hour NONCLUSTERED (topic_id, hour_bucket DESC),
    INDEX IX_topic_metrics_updated NONCLUSTERED (updated_at DESC)
);

-- Source performance metrics
CREATE TABLE source_metrics_realtime (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    hour_bucket DATETIME2 NOT NULL,
    source_name NVARCHAR(100) NOT NULL,
    article_count INT NOT NULL DEFAULT 0,
    avg_confidence DECIMAL(8,6) NOT NULL,
    topic_diversity INT DEFAULT 0,                -- Number of unique topics
    avg_processing_time_ms INT DEFAULT 0,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT UQ_source_metrics_hour_source UNIQUE (hour_bucket, source_name),
    
    INDEX IX_source_metrics_hour_desc NONCLUSTERED (hour_bucket DESC, source_name),
    INDEX IX_source_metrics_source_hour NONCLUSTERED (source_name, hour_bucket DESC)
);

-- =====================================================
-- 5. OPTIMIZED STORED PROCEDURES FOR BULK OPERATIONS
-- =====================================================

-- High-performance bulk upsert for articles
CREATE OR ALTER PROCEDURE usp_BulkUpsertArticles
    @ArticlesJson NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Parse JSON input
    WITH ArticlesData AS (
        SELECT 
            external_id,
            title,
            description,
            content,
            source_name,
            author,
            url,
            url_to_image,
            TRY_CAST(published_at AS DATETIME2) as published_at,
            language,
            country,
            category
        FROM OPENJSON(@ArticlesJson)
        WITH (
            external_id NVARCHAR(100) '$.external_id',
            title NVARCHAR(500) '$.title',
            description NVARCHAR(MAX) '$.description',
            content NVARCHAR(MAX) '$.content',
            source_name NVARCHAR(100) '$.source_name',
            author NVARCHAR(200) '$.author',
            url NVARCHAR(1000) '$.url',
            url_to_image NVARCHAR(1000) '$.url_to_image',
            published_at NVARCHAR(50) '$.published_at',
            language NVARCHAR(10) '$.language',
            country NVARCHAR(10) '$.country',
            category NVARCHAR(50) '$.category'
        )
    )
    
    -- Bulk merge operation
    MERGE articles_partitioned AS target
    USING ArticlesData AS source
    ON target.external_id = source.external_id
    WHEN MATCHED THEN
        UPDATE SET 
            title = source.title,
            description = source.description,
            content = source.content,
            updated_at = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (external_id, title, description, content, source_name, 
                author, url, url_to_image, published_at, language, country, category)
        VALUES (source.external_id, source.title, source.description, source.content,
                source.source_name, source.author, source.url, source.url_to_image,
                source.published_at, source.language, source.country, source.category);
    
    -- Return affected row count
    SELECT @@ROWCOUNT as AffectedRows;
END;

-- High-performance bulk upsert for topic classifications
CREATE OR ALTER PROCEDURE usp_BulkUpsertTopicClassifications
    @ClassificationsJson NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    WITH ClassificationData AS (
        SELECT 
            article_id,
            topic_id,
            confidence,
            all_probabilities,
            model_version,
            processing_time_ms
        FROM OPENJSON(@ClassificationsJson)
        WITH (
            article_id BIGINT '$.article_id',
            topic_id INT '$.topic_id',
            confidence DECIMAL(8,6) '$.confidence',
            all_probabilities NVARCHAR(MAX) '$.all_probabilities',
            model_version NVARCHAR(20) '$.model_version',
            processing_time_ms INT '$.processing_time_ms'
        )
    )
    
    MERGE article_topics_optimized AS target
    USING ClassificationData AS source
    ON target.article_id = source.article_id AND target.model_version = source.model_version
    WHEN MATCHED THEN
        UPDATE SET 
            topic_id = source.topic_id,
            confidence = source.confidence,
            all_probabilities = source.all_probabilities,
            processing_time_ms = source.processing_time_ms,
            classified_at = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (article_id, topic_id, confidence, all_probabilities, 
                model_version, processing_time_ms)
        VALUES (source.article_id, source.topic_id, source.confidence,
                source.all_probabilities, source.model_version, source.processing_time_ms);
    
    SELECT @@ROWCOUNT as AffectedRows;
END;

-- =====================================================
-- 6. REAL-TIME AGGREGATION MAINTENANCE
-- =====================================================

-- Procedure to update real-time metrics
CREATE OR ALTER PROCEDURE usp_UpdateRealtimeMetrics
    @HourBucket DATETIME2
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Update topic metrics for the hour
    WITH HourlyTopicStats AS (
        SELECT 
            @HourBucket as hour_bucket,
            at.topic_id,
            COUNT(*) as article_count,
            AVG(at.confidence) as avg_confidence,
            STDEV(at.confidence) as confidence_std,
            SUM(a.word_count) as total_word_count,
            COUNT(DISTINCT a.source_name) as unique_sources
        FROM article_topics_optimized at
        INNER JOIN articles_partitioned a ON at.article_id = a.article_id
        WHERE a.published_at >= @HourBucket 
        AND a.published_at < DATEADD(HOUR, 1, @HourBucket)
        AND at.confidence >= 0.3
        GROUP BY at.topic_id
    )
    
    MERGE topic_metrics_realtime AS target
    USING HourlyTopicStats AS source
    ON target.hour_bucket = source.hour_bucket AND target.topic_id = source.topic_id
    WHEN MATCHED THEN
        UPDATE SET 
            article_count = source.article_count,
            avg_confidence = source.avg_confidence,
            confidence_std = ISNULL(source.confidence_std, 0),
            total_word_count = source.total_word_count,
            unique_sources = source.unique_sources,
            updated_at = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (hour_bucket, topic_id, article_count, avg_confidence, confidence_std,
                total_word_count, unique_sources)
        VALUES (source.hour_bucket, source.topic_id, source.article_count,
                source.avg_confidence, ISNULL(source.confidence_std, 0),
                source.total_word_count, source.unique_sources);
END;

-- =====================================================
-- 7. PERFORMANCE MONITORING VIEWS
-- =====================================================

-- View for monitoring partition health
CREATE OR ALTER VIEW vw_partition_health AS
SELECT 
    p.partition_number,
    p.rows,
    p.data_compression_desc,
    rv.value as partition_boundary,
    CASE 
        WHEN p.rows > 1000000 THEN 'Consider archiving'
        WHEN p.rows < 10000 THEN 'Low volume'
        ELSE 'Normal'
    END as health_status
FROM sys.partitions p
INNER JOIN sys.partition_schemes ps ON p.partition_id = ps.data_space_id
INNER JOIN sys.partition_range_values rv ON ps.function_id = rv.function_id 
    AND p.partition_number = rv.boundary_id + 1
WHERE p.object_id = OBJECT_ID('articles_partitioned');

-- View for index usage statistics
CREATE OR ALTER VIEW vw_index_usage_stats AS
SELECT 
    OBJECT_NAME(s.object_id) as table_name,
    i.name as index_name,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    s.last_user_seek,
    s.last_user_scan,
    CASE 
        WHEN s.user_seeks + s.user_scans + s.user_lookups = 0 THEN 'Unused'
        WHEN s.user_updates > (s.user_seeks + s.user_scans + s.user_lookups) * 2 THEN 'High maintenance'
        ELSE 'Active'
    END as usage_status
FROM sys.dm_db_index_usage_stats s
INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE s.database_id = DB_ID()
AND OBJECT_NAME(s.object_id) IN ('articles_partitioned', 'article_topics_optimized');

-- =====================================================
-- 8. AUTOMATED MAINTENANCE JOBS
-- =====================================================

-- Procedure for automated partition management
CREATE OR ALTER PROCEDURE usp_ManagePartitions
AS
BEGIN
    -- Add new partitions for future months
    -- Archive old partitions
    -- Update statistics
    -- Rebuild fragmented indexes
    
    PRINT 'Partition management completed';
END;
