../../Scripts/spacy.exe,sha256=qc61sIKxTYd1NWYj60GyeaW2Ys0wT2TJVlM6alrjBJo,108415
spacy-3.8.7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spacy-3.8.7.dist-info/METADATA,sha256=iVwm1zF6mCEelIbnhiESKM8kkA2DUEqfiVD_ZR6s3wE,28383
spacy-3.8.7.dist-info/RECORD,,
spacy-3.8.7.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy-3.8.7.dist-info/WHEEL,sha256=RYNUKzg4pggqpqERKe4OLbPF4ZPP-Ng-rmq_sekLDXg,101
spacy-3.8.7.dist-info/entry_points.txt,sha256=q6IIfdWIqV5G_nzLChejIJpbzXNEg4iqHL05cTBO888,46
spacy-3.8.7.dist-info/licenses/LICENSE,sha256=KKfkHRd893LU6-vxV1WFAVCOgunFe2VMh4ZZVrafcog,1149
spacy-3.8.7.dist-info/top_level.txt,sha256=oXTJOtzJdUdzkIbih8SAC-lK9JfD0jvZ_43H5fmTCHw,6
spacy/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/__init__.py,sha256=yv1DzNAXPvMFV_4zhZMG80kCNcVmnczF3zBECeSga3E,3076
spacy/__main__.py,sha256=ipw4uuFXJo2_pifxy6Vg6Jyxzks2XD3mppBZyzGbsrE,84
spacy/__pycache__/__init__.cpython-312.pyc,,
spacy/__pycache__/__main__.cpython-312.pyc,,
spacy/__pycache__/about.cpython-312.pyc,,
spacy/__pycache__/compat.cpython-312.pyc,,
spacy/__pycache__/errors.cpython-312.pyc,,
spacy/__pycache__/git_info.cpython-312.pyc,,
spacy/__pycache__/glossary.cpython-312.pyc,,
spacy/__pycache__/language.cpython-312.pyc,,
spacy/__pycache__/lookups.cpython-312.pyc,,
spacy/__pycache__/pipe_analysis.cpython-312.pyc,,
spacy/__pycache__/registrations.cpython-312.pyc,,
spacy/__pycache__/schemas.cpython-312.pyc,,
spacy/__pycache__/scorer.cpython-312.pyc,,
spacy/__pycache__/ty.cpython-312.pyc,,
spacy/__pycache__/util.cpython-312.pyc,,
spacy/about.py,sha256=bA-bx9H6VLTf0Z0Yj-kXwykiMqifAl7HCn76BBIJWjQ,244
spacy/attrs.cp312-win_amd64.pyd,sha256=436DnYvoWuxwcQAp-CLooaBFiHrWbIcuVLBPE6Ac8dc,65024
spacy/attrs.cpp,sha256=IZMwd8SpY6cKj-G9_lC0W6dNym5cLTtIb4fr1GHMWyU,506038
spacy/attrs.pxd,sha256=3niCIPXzTpcDx7DgMWbarZc-El_0c3t9lAzSAlUwpDM,1256
spacy/attrs.pyx,sha256=ZJcFmXfWTen6vOVtKUU8r31MvU7IjpsZHGs1ZsERRBE,6002
spacy/cli/__init__.py,sha256=negnqnsucuC6Saf6toML5WYQxtoJg-p-jAt7IDUTPTw,2446
spacy/cli/__pycache__/__init__.cpython-312.pyc,,
spacy/cli/__pycache__/_util.cpython-312.pyc,,
spacy/cli/__pycache__/apply.cpython-312.pyc,,
spacy/cli/__pycache__/assemble.cpython-312.pyc,,
spacy/cli/__pycache__/benchmark_speed.cpython-312.pyc,,
spacy/cli/__pycache__/convert.cpython-312.pyc,,
spacy/cli/__pycache__/debug_config.cpython-312.pyc,,
spacy/cli/__pycache__/debug_data.cpython-312.pyc,,
spacy/cli/__pycache__/debug_diff.cpython-312.pyc,,
spacy/cli/__pycache__/debug_model.cpython-312.pyc,,
spacy/cli/__pycache__/download.cpython-312.pyc,,
spacy/cli/__pycache__/evaluate.cpython-312.pyc,,
spacy/cli/__pycache__/find_function.cpython-312.pyc,,
spacy/cli/__pycache__/find_threshold.cpython-312.pyc,,
spacy/cli/__pycache__/info.cpython-312.pyc,,
spacy/cli/__pycache__/init_config.cpython-312.pyc,,
spacy/cli/__pycache__/init_pipeline.cpython-312.pyc,,
spacy/cli/__pycache__/package.cpython-312.pyc,,
spacy/cli/__pycache__/pretrain.cpython-312.pyc,,
spacy/cli/__pycache__/profile.cpython-312.pyc,,
spacy/cli/__pycache__/train.cpython-312.pyc,,
spacy/cli/__pycache__/validate.cpython-312.pyc,,
spacy/cli/_util.py,sha256=ULumVGqysm7G49eMKS2OdzDQRbXRF0lj5B6zAG2U8H0,11172
spacy/cli/apply.py,sha256=UXDkn0G3yuNUPS8snCczv2QrhbcWy4LpkuXm3n_j1y8,5012
spacy/cli/assemble.py,sha256=xO0F6NFoe2sdrlSX-nr-nCTPm90AKvMnJjABDOYd9uk,2642
spacy/cli/benchmark_speed.py,sha256=6Rws6ULZzKI8hz1nHMw1L08Tqpzq73FXY_xNRJEnkrY,5619
spacy/cli/convert.py,sha256=T1SMkCQEkdl7JjP1r8f71m8557zv9SRgWJcxUYPidGM,9643
spacy/cli/debug_config.py,sha256=24tqcPezWSHC1NVj7iPQtJQwuli0WAhbZ4OrC9pYVDo,4671
spacy/cli/debug_data.py,sha256=zLctmLV9iy6GwKgeVoTRpkjzqMOdGGkkVjXz_wdvweM,51485
spacy/cli/debug_diff.py,sha256=qFhQsRmfQYIgy2fs1OHp0M7vh7FPEYzbtcRL2PyyVzQ,3669
spacy/cli/debug_model.py,sha256=8g5tWEQszFMEZNdo5zUtVYqR5tH0ljKPzdzVVNSjFrE,9171
spacy/cli/download.py,sha256=OfaKaa4eiMCh5hoA1cES_TpDjCV98Crp7bfs0gOwEEE,6599
spacy/cli/evaluate.py,sha256=uBu0OAt19JL4sj8wi8jkcrq7YuZs8mWy4LfaWNPjTHM,9659
spacy/cli/find_function.py,sha256=rNUSmMRf_lwBjOZR-2TctjlXjKPVtco9OboIKhYJQdA,2191
spacy/cli/find_threshold.py,sha256=q3XzL2PUL_zcMiQzEGUqonh2yoJR18PgsUb5Sj3zTos,9605
spacy/cli/info.py,sha256=BDvaPRVY8ktFyvQf-V-5deoTTNM0KIJTSbsLEceTQh0,6520
spacy/cli/init_config.py,sha256=R737sUk2xMtOWH3LjPM7Xs5SBctHpmAdvNo5g0jCJW0,10644
spacy/cli/init_pipeline.py,sha256=cw39Tc6ZOAy-Qzs5_p8DzFmucDx4ElJBLs7EkAh0HPY,5911
spacy/cli/package.py,sha256=mZEaUIxQMkxCugvTbgQo4eZkUJQOLlrT8Ln3fJNThvQ,24484
spacy/cli/pretrain.py,sha256=8v4ZWMn34LdTEba5LO4DQAe2RgKyNLl-M4S4g0W2pqw,5401
spacy/cli/profile.py,sha256=Yctdw-oTTIckYo0XdlGZpUN61yHEH--C7D7ixAh_Q6c,3559
spacy/cli/project/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/cli/project/__pycache__/__init__.cpython-312.pyc,,
spacy/cli/project/__pycache__/assets.cpython-312.pyc,,
spacy/cli/project/__pycache__/clone.cpython-312.pyc,,
spacy/cli/project/__pycache__/document.cpython-312.pyc,,
spacy/cli/project/__pycache__/dvc.cpython-312.pyc,,
spacy/cli/project/__pycache__/pull.cpython-312.pyc,,
spacy/cli/project/__pycache__/push.cpython-312.pyc,,
spacy/cli/project/__pycache__/remote_storage.cpython-312.pyc,,
spacy/cli/project/__pycache__/run.cpython-312.pyc,,
spacy/cli/project/assets.py,sha256=Gu4HGRtW7_sKF7i9LNeKGjDpiFaTOt8-ZvcGMi8ZRgw,33
spacy/cli/project/clone.py,sha256=DTDLXE1h5jo8E1KATHouzOYioKvoLSYsl4EVqWzAenQ,32
spacy/cli/project/document.py,sha256=KDgBdOaINBa3CkaQSAmM-nCGntKvf9Iuakj9yXE7rGA,35
spacy/cli/project/dvc.py,sha256=b86jsms1n0x7Me8wtxBxVci3ungIUxjqSJaNQCeZKuE,30
spacy/cli/project/pull.py,sha256=dHf6gYmC9OXDhOj9g1kmZ0R9a-9DfFcD8WXiIAsWvL0,31
spacy/cli/project/push.py,sha256=dBbYgZCOgYRNrW94AhCYNapYcFrAuZc_68Cj8w6KgQs,31
spacy/cli/project/remote_storage.py,sha256=xhBJMwSJrD2nuKMcSSGqo0-6wRF7y02axLeiP5hAgjM,41
spacy/cli/project/run.py,sha256=dFvrl7QrAsRSObOQ-KK5qpiyCpxmykJ_2fqfZrl4UVc,30
spacy/cli/templates/quickstart_training.jinja,sha256=h8OqIrzNLZOh4OMT8VabM_R7d2IIYMb1l6c4m1ORlzo,17003
spacy/cli/templates/quickstart_training_recommendations.yml,sha256=7lIrbsm8HSCbUNe4IfoodVLiah61V9166o2VZySisZA,6576
spacy/cli/train.py,sha256=djomPb1hpcV_YPgjiIx2SaBORapQ89orNTfkNnZE5sU,3490
spacy/cli/validate.py,sha256=C_JGXzGYO8UKzVIrNpE1kCD4EfikhrzPu-djJn26DAg,4710
spacy/compat.py,sha256=n4vd6xMNY3-FXmdNUXjxOtg7JYkmTkDa3clts-_Ky4g,1396
spacy/default_config.cfg,sha256=gHMQ7JsQFkP6D-MnZtrIIEONyMuiEW0UpwqDkX-ro6g,4535
spacy/default_config_pretraining.cfg,sha256=h41wUgQkX2KmUW_2Y09DnxeOb7Nc5v-ed_tPIYfiR1Q,782
spacy/displacy/__init__.py,sha256=3XqO7lX01xu2BHtBkXjRFuolVHSWqtqHY-Nq86KVrm0,10614
spacy/displacy/__pycache__/__init__.cpython-312.pyc,,
spacy/displacy/__pycache__/render.cpython-312.pyc,,
spacy/displacy/__pycache__/templates.cpython-312.pyc,,
spacy/displacy/render.py,sha256=-8eHI9N0EERDXjHMz6lvPQOgAzS398xe8N3t_njbwq0,25312
spacy/displacy/templates.py,sha256=BNuAWXxAdocc5qa_AdeRsioffQ9ZcWXlAk6eWQOrrNY,4785
spacy/errors.py,sha256=mLAFRxr1hGiMl7buYjHnns91E8fa7RUDoFZI3Bppmp0,69151
spacy/git_info.py,sha256=gA-eFBxr-1Eo1DlosojuJ9KbkhoydF4zWAzmzBG6fQQ,74
spacy/glossary.py,sha256=MnY6AbNkMsofkEvCffH2GD3hukYuXt4rU6dBoHlMSdc,13546
spacy/kb/__init__.py,sha256=iWbBgDvGS4g9us7oefSkCQ-O-Rvtarx5C6blOQinKK8,282
spacy/kb/__pycache__/__init__.cpython-312.pyc,,
spacy/kb/candidate.cp312-win_amd64.pyd,sha256=oi6nrJnW4F_GjpV5vxT7J5KfuwBsvv3BlGoIPqyncx4,72704
spacy/kb/candidate.cpp,sha256=BQMy7y7rmrc2Y8W8GiZEaObV1GffjKH90wOFSU4s8hA,668672
spacy/kb/candidate.pxd,sha256=dg5S03k8PiqS7Cgb_ajkxnGDmuUv_ti-M7cb_7ylqOY,409
spacy/kb/candidate.pyx,sha256=5XkXaydKC_qybFb1WGv4X3t7yAEHfeVl_2AcFjBWvvQ,2769
spacy/kb/kb.cp312-win_amd64.pyd,sha256=tpNr4yykEkvVkUbG0yyxbMuVsiwaeOHSyfs5Es09IJ0,79872
spacy/kb/kb.cpp,sha256=DYYZsLQ_Ntfm89-cB1TCU0rvlF1lNgeiIKZCE7xIMvY,734834
spacy/kb/kb.pxd,sha256=W7jpueuhDk53PM6-WR3VFruMXvtUpMlPz7BdUmvJF-M,279
spacy/kb/kb.pyx,sha256=gq_ZSyvM9M2vLy59zw9BEyIYiCfg9pW0PD9avf9qI0Q,4827
spacy/kb/kb_in_memory.cp312-win_amd64.pyd,sha256=uNXOACfsmtdhuKWw9QgadUboWXvUOFOcXyF3R9MGjv4,262144
spacy/kb/kb_in_memory.cpp,sha256=20QyHnV6OdYKh-HtzLlyz4GjmI8Tj5D3TWTkDUZCp_Q,1747774
spacy/kb/kb_in_memory.pxd,sha256=JzIo6JAuYmwDNPAER3XqCrt0PBzSym7iLTbTkj4wsRo,7139
spacy/kb/kb_in_memory.pyx,sha256=y0c-5hqHeJCHAapxj65IzkveRZ1Ay7RJ7JCqsgXHMOM,27851
spacy/lang/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/lang/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/__pycache__/char_classes.cpython-312.pyc,,
spacy/lang/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/__pycache__/norm_exceptions.cpython-312.pyc,,
spacy/lang/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/af/__init__.py,sha256=ZeDySzRyxvhfJTndUMYCHNFiBhQkX0p8Je83FQ3tf_E,269
spacy/lang/af/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/af/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/af/stop_words.py,sha256=H_sR5txOztzBQH4p4xVnNxx0RkWhPlPPqvpYTl9gQJk,348
spacy/lang/am/__init__.py,sha256=ViDKjqDKOLRVIp1JAit_A1DhbBDI5BmpcPpgstdGy5s,856
spacy/lang/am/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/am/__pycache__/examples.cpython-312.pyc,,
spacy/lang/am/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/am/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/am/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/am/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/am/examples.py,sha256=3bWKezTGh9ba49D7cg6VnBmYy54_iHjJjCDmXmRe4wM,810
spacy/lang/am/lex_attrs.py,sha256=j-SPYezEw3IEghMy9a4FDNV9HLe2b5_NjlGBVkeJMcc,2294
spacy/lang/am/punctuation.py,sha256=IUfG5HQn-w4Yb3BBrTxusfXK9TDPvrWQstzmg-SVIBQ,572
spacy/lang/am/stop_words.py,sha256=0JnkVNCLWSJyHCZTOE0fNY4_qMNaYN8myRoDIvJ-BVs,3235
spacy/lang/am/tokenizer_exceptions.py,sha256=cQ10ykSkQekvb2WzfSEQ5-2RplDmcAMcjlbwAYORS0w,310
spacy/lang/ar/__init__.py,sha256=wmF--ZWF_y7YfOaTZs-4BEWT22AftiiFGjnhP9IGMHc,593
spacy/lang/ar/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ar/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ar/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ar/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ar/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ar/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ar/examples.py,sha256=p0i1W-pzSKCODHF9vBKvvSYGZxX5FIqyypzAWQaMKo4,907
spacy/lang/ar/lex_attrs.py,sha256=OPmYFydoME6Fe_1bNPI1t-HpzTxI2tvBVFlI48GMYiM,1406
spacy/lang/ar/punctuation.py,sha256=L4Yh69QPl1cka78n_-eDlrvfxb1OV6Id41g587Y6_9w,486
spacy/lang/ar/stop_words.py,sha256=3Qw5pKVhtDGQ3FlW0rRxm7vjfIbf60C6L6hbuzkfUS4,3570
spacy/lang/ar/tokenizer_exceptions.py,sha256=qQfUc-uJp9guA-_4qbq2ycVKvnuVa9iS4WHxThmbn6c,1548
spacy/lang/az/__init__.py,sha256=g1eXaA-G45gkdHC22KQLRjcA2buXr5w-FjGxTMcagj8,345
spacy/lang/az/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/az/__pycache__/examples.cpython-312.pyc,,
spacy/lang/az/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/az/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/az/examples.py,sha256=I0NZ0a650i8kTuBw8p2QhOANSCiqc0nLZRnrk5SA2u0,765
spacy/lang/az/lex_attrs.py,sha256=wHCWvnDVqnHnQxGLGcN0t9InC4plY6mUf6P9gdvb84w,1784
spacy/lang/az/stop_words.py,sha256=E10S_YshQa8QXgojMsih4sAqr41eWgplm9bEahTjC-A,1111
spacy/lang/bg/__init__.py,sha256=08lpNjLTQxa_Kts6bvl0PY-LanbcDZRosniAuWf2x44,938
spacy/lang/bg/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/bg/__pycache__/examples.cpython-312.pyc,,
spacy/lang/bg/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/bg/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/bg/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/bg/examples.py,sha256=36pksYTnijoWBOIDmNLbAVcDFa4zYO9zdtomkiuck_0,649
spacy/lang/bg/lex_attrs.py,sha256=lWmr7SLaFKE-MV74ccr29z5Zmj5YPV2H7-liU4ClY_E,2134
spacy/lang/bg/stop_words.py,sha256=tMIP6vEtXPXFRyyYK9RE2swcxL-bJj6UFlrFoM85hdw,4827
spacy/lang/bg/tokenizer_exceptions.py,sha256=YlSqOp5SisWeIFSoLqFSSFiLQL4mOGvuNHZqISofJWs,9321
spacy/lang/bn/__init__.py,sha256=O9WVKw86bIByKlrGGifor20vHKWlbGj6F4Girw46Um8,1226
spacy/lang/bn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/bn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/bn/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/bn/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/bn/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/bn/examples.py,sha256=hchWoBKFkWGkML4223ZV3UKseO7HplAxynAIYKQ0nDA,314
spacy/lang/bn/punctuation.py,sha256=nwwY7kGBbHBVpgfYPaJjQA_GrL4M58LhvLuJ5hgI7Rs,1337
spacy/lang/bn/stop_words.py,sha256=HzcPcAKwdOcTpTzYJNe1UW5GMv3it59gd8NmYpzmGfc,6198
spacy/lang/bn/tokenizer_exceptions.py,sha256=QS3dc1SUA0C1OE0OgKOs7zTDjyDs1vOXi2goM18KJXY,995
spacy/lang/bo/__init__.py,sha256=7LecpEYcKVi68nsMg9yN4qRQuqsjnE_uaM_O7a-mSUY,329
spacy/lang/bo/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/bo/__pycache__/examples.cpython-312.pyc,,
spacy/lang/bo/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/bo/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/bo/examples.py,sha256=y8zsDtTUxiUTMgJNGOtFLcDp5ohLuF_-VKT6uC1NoA4,1657
spacy/lang/bo/lex_attrs.py,sha256=K1I2EE9ZTbftRJVzg_xS6I90e8vGW2KpRWLY92zKlI8,1759
spacy/lang/bo/stop_words.py,sha256=sCwl9HaZ94190_7QhlhYgt70t2f1KhZTQtbKR1Us7wI,2397
spacy/lang/ca/__init__.py,sha256=fK8hjB51wH2aOGcdv2aXGeHa6ACLAb06y-J-fxLx2jw,1397
spacy/lang/ca/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ca/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ca/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/ca/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ca/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ca/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ca/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/ca/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ca/examples.py,sha256=uF5Z4DbU1gf8MauITplga1ytbbhoGeZ3IX19kSFUOo0,613
spacy/lang/ca/lemmatizer.py,sha256=zuzsrKUYT3cQSM21dafO2qFP7ANuUOh3bGJMmyWA1Ic,2924
spacy/lang/ca/lex_attrs.py,sha256=ronURt-9a9CHjRtV4OwAkE2fm_tVJhmgB_LkO20vzrM,1013
spacy/lang/ca/punctuation.py,sha256=Rc8qcJMDngDy2-tN0XCB16kboxmgZ9nBESLmfCb2iC0,1668
spacy/lang/ca/stop_words.py,sha256=-m8za0h81UbOw4i0UG857zjVO_9nQASaxk1gOg71U5Q,1671
spacy/lang/ca/syntax_iterators.py,sha256=OwqOo4aPjKKwkvUNPlCuBNtbTvtt7nmzkRwDIjNsw2M,2044
spacy/lang/ca/tokenizer_exceptions.py,sha256=l4ATYwiO49_kbsWyBGdacUv0EESu5Jr5GP-jeswiMBs,2028
spacy/lang/char_classes.py,sha256=dYGU78hbt9dyRhfS96h2TARG9rFS3ct1DfcmRF5p_Bg,15011
spacy/lang/cs/__init__.py,sha256=xI4eD5puCmwZJXP7HIBGBnYfZsN_Co8CJyUDRVDJm68,321
spacy/lang/cs/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/cs/__pycache__/examples.cpython-312.pyc,,
spacy/lang/cs/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/cs/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/cs/examples.py,sha256=kCgV00XVc4Lp0yCvl89DwSH00xOud01HCMgI-Dzv_p4,1552
spacy/lang/cs/lex_attrs.py,sha256=a-C2DCEroB1LyKmC83SmYYbNlDbJ4Sk3kxjqEKMs1nc,1135
spacy/lang/cs/stop_words.py,sha256=Oa--mrk0zKN_MWUV2E-PWMsf483x-SGeMXpgDvzNX7U,2628
spacy/lang/da/__init__.py,sha256=8bQ6IA2RgtufOIkYIKUxAkn_bgEBg4nLep8OA57WPRg,651
spacy/lang/da/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/da/__pycache__/examples.cpython-312.pyc,,
spacy/lang/da/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/da/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/da/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/da/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/da/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/da/examples.py,sha256=FLPTyVtK9ux7ewZ75uodGgDT3Sjzjv8z7a_I6xUe5iI,585
spacy/lang/da/lex_attrs.py,sha256=zmd3iV004q5j3fk-SkcwT_j6tcNTEF2vHD7H_x146QQ,3624
spacy/lang/da/punctuation.py,sha256=1qJd2YAkgcSr9pE3wPPjagkdIrfZgRakCWSdNpBqvrg,950
spacy/lang/da/stop_words.py,sha256=xvlGGoRV5LtAP8upcVEhXZ4DIRaGdBuoMPCDf3NQqaU,1390
spacy/lang/da/syntax_iterators.py,sha256=Q2ytAhof-MQzBARMIacgyF5LTH4TjKi6pmra9A7WQP0,2263
spacy/lang/da/tokenizer_exceptions.py,sha256=19eG64XOkOHr85khyK0mUZKDai4WS7BiZg-LnatH54s,9535
spacy/lang/de/__init__.py,sha256=JDIA4031ewdT_U81Rpr75r2jBwDdrRCnXkaWGscx9kw,638
spacy/lang/de/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/de/__pycache__/examples.cpython-312.pyc,,
spacy/lang/de/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/de/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/de/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/de/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/de/examples.py,sha256=rdNiIKOByHM4RiHOzoi7oq0rx3843UTQzG9MEcM6C7M,697
spacy/lang/de/punctuation.py,sha256=nPF_VeD_Q1YouCwby5TpvdY8Cut2f_rf9djSt288dqE,1465
spacy/lang/de/stop_words.py,sha256=25baHVaTDhAAlPU20EnPJ74MafkROAzXnRwI5MczoZw,3739
spacy/lang/de/syntax_iterators.py,sha256=bU2yayFpZgP4RMGnPCB5px5PjLIZvbpByFUgEdJi2xI,1893
spacy/lang/de/tokenizer_exceptions.py,sha256=F72X3ABfxeR8RgtgBR-VDsg-2s2urn_jv3ggBhMcAkU,6122
spacy/lang/dsb/__init__.py,sha256=yhEGIzRQe3YBwHNynf5tJDCgRnt9sjZF4YcHfpDdqmI,350
spacy/lang/dsb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/dsb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/dsb/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/dsb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/dsb/examples.py,sha256=3P1lklRPW1ei6vlRSizXW0oGHK6iAHuDM0-hlxwKBZQ,568
spacy/lang/dsb/lex_attrs.py,sha256=jVBjesvzatTtye-2eDDLh2OeVD3lmfuvGfgpIt88hH8,2019
spacy/lang/dsb/stop_words.py,sha256=DJ6QDzYe6eN0Q4gSTTDG7T4gEfeR_R86_RoAk-98Bsc,133
spacy/lang/el/__init__.py,sha256=f-bCPO2ys5MlNSr-Dw1w0uUmKK1t-6Q6j1HeYQwf5ss,1383
spacy/lang/el/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/el/__pycache__/examples.cpython-312.pyc,,
spacy/lang/el/__pycache__/get_pos_from_wiktionary.cpython-312.pyc,,
spacy/lang/el/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/el/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/el/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/el/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/el/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/el/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/el/examples.py,sha256=_cQbPtUwB-TTQNS0A6BsfRuQ39mLP7fw9uOtDczKTxM,1996
spacy/lang/el/get_pos_from_wiktionary.py,sha256=zjcz_WD0sRFZ86dcdZzK6ONZpg4CJVkKZCNGyZK2PAQ,2151
spacy/lang/el/lemmatizer.py,sha256=UJpdYmXFb0SXkSyEYSJfGKLXoUFg1cJ1ZI5fBCz3900,2257
spacy/lang/el/lex_attrs.py,sha256=kGn4bzLt8eC5Mv3xJ7t-gaZHjCdj495ypfWYxi2xQV8,2419
spacy/lang/el/punctuation.py,sha256=cczk06w0aeh0f-ak1qbxf2M3tNjKk3B0cLlhAGztvwM,3513
spacy/lang/el/stop_words.py,sha256=wCpgnP35DX2qw8VAP-oJGgNSg49jEusVCC8JFpxcgEI,8187
spacy/lang/el/syntax_iterators.py,sha256=2Zz4ejZLxiV070emRe4k7wTPlAJgOxOq-CPym3DimeM,2343
spacy/lang/el/tokenizer_exceptions.py,sha256=zyjdJYDKQxggi8u223nOfhY2GjSJG2jTwcGrtq_yQHw,10471
spacy/lang/en/__init__.py,sha256=w6FMOVdVeQyBvG9bIFts6F1dpA2rRrD4cMzFMYuBO08,1287
spacy/lang/en/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/en/__pycache__/examples.cpython-312.pyc,,
spacy/lang/en/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/en/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/en/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/en/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/en/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/en/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/en/examples.py,sha256=paT-ZtCInFSLtMBzDC1O7s0K4thsQc7tmkXqQHQk-00,574
spacy/lang/en/lemmatizer.py,sha256=xQ-Tod3RI1sbS7xd7cWkEnHya9E47hNZ0BlPWGbgxZQ,1520
spacy/lang/en/lex_attrs.py,sha256=CmAQ7somf8tJGhmO5R2q6aNj_crtQhahTydZCzTEQaQ,1816
spacy/lang/en/punctuation.py,sha256=NE246fA9_BmJyArRIROb48Z02x03wk2m1gmuHYbqlz0,602
spacy/lang/en/stop_words.py,sha256=t3w3m4xfRGBB4BFpppgGwtmjl523U0LV87w4S-QQvSw,2221
spacy/lang/en/syntax_iterators.py,sha256=2aLVMI3JPy2rrQWMrTGTAO5FYbkdGKxLitSGC7fBs_U,1620
spacy/lang/en/tokenizer_exceptions.py,sha256=j6yHhowmuER9TqFQT9v0aln_hOh15r3F_CTKexVafcg,14778
spacy/lang/es/__init__.py,sha256=3SO9ZO8n8OuoQ-ks8lfBLNteOVfTGV4Sb7bnXjCncyQ,1342
spacy/lang/es/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/es/__pycache__/examples.cpython-312.pyc,,
spacy/lang/es/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/es/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/es/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/es/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/es/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/es/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/es/examples.py,sha256=EpY8in34BTVcMC8l1GR9tqPSRfVQlR3ZiSzSEOruo_4,800
spacy/lang/es/lemmatizer.py,sha256=xXPVLSrv1K_RB-aFaC9uFNczEdmAoz5RkKd3z58JVlM,16450
spacy/lang/es/lex_attrs.py,sha256=iQFiuhiY9HkCpbyNNGwacvYiQD5XjevyWYZhrP-ajrk,1898
spacy/lang/es/punctuation.py,sha256=wRvDM5rgapPp77ux2xl3t2rNwtFQBzeWfoSpco0yDa4,1261
spacy/lang/es/stop_words.py,sha256=jizTlvy5N-nQmLAVwYUZC1QqXv7Xe9t-nU1yMjW2mgs,3468
spacy/lang/es/syntax_iterators.py,sha256=MZr7a5aG-11HUDteJX_y61lr40MKS4-RY2338a3Sv0s,2790
spacy/lang/es/tokenizer_exceptions.py,sha256=5XGveG4W9qS0tIeAvOBmAdX4gOha2arcy9JGRPW4-3M,1539
spacy/lang/et/__init__.py,sha256=PkMJnctug4sf9Cc5gAO1B9rMmjTBakKrJprwvixTvew,265
spacy/lang/et/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/et/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/et/stop_words.py,sha256=p3f6Aa8PDXTPqiKxfSsfEE_9LBsmqOTayGmDDJucPHw,287
spacy/lang/eu/__init__.py,sha256=csBf3qU7InPmGUAx1EGN3QZo8vAUUTTLvgRxuAf7YmU,405
spacy/lang/eu/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/eu/__pycache__/examples.cpython-312.pyc,,
spacy/lang/eu/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/eu/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/eu/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/eu/examples.py,sha256=4Yemvo2C6RiLmonJKRD1qRljmMMqu4ID0yl0BHTO_Lg,430
spacy/lang/eu/lex_attrs.py,sha256=6K1uXNmsJx_mBubPfzHua435LA5vd2TuAkLybbh-Yrc,1169
spacy/lang/eu/punctuation.py,sha256=HWXmxBKR0LsoS0kq01YNYulgzl1MR5aXqRFzc_SPEZc,80
spacy/lang/eu/stop_words.py,sha256=q-d3a_gjlgRVf9bm_-l33KICnlb1qITNLLJBw_OH8YE,865
spacy/lang/fa/__init__.py,sha256=s9pLeOQE2Ekuqe2cT_TqxYoyMLEAGzBs8w7nZjvSaro,1359
spacy/lang/fa/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fa/__pycache__/examples.cpython-312.pyc,,
spacy/lang/fa/__pycache__/generate_verbs_exc.cpython-312.pyc,,
spacy/lang/fa/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/fa/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/fa/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/fa/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/fa/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fa/examples.py,sha256=b00tiJRxEWeJ_wjkaxmQV4eCwGW-lM8Bp8QAT9SYehE,530
spacy/lang/fa/generate_verbs_exc.py,sha256=-Uwhwh4B2KTYu24t9mrLnowurNoJ0eMGUIXsYbIBDKc,15507
spacy/lang/fa/lex_attrs.py,sha256=ksndiguuxYvTJ1o5p94A8R9_X93-qy2wIdjzaxRo9dU,1486
spacy/lang/fa/punctuation.py,sha256=gJhsl3iPjyl8ckc9HlW61O7ya5O4jh-juy4e77gEx6w,532
spacy/lang/fa/stop_words.py,sha256=VCbkQeLN8_NgMOYxSqQlXR5OQicCS5Ybo4Xm-bY63TI,4161
spacy/lang/fa/syntax_iterators.py,sha256=IYDndeLdr8dJQ1Owa1JXRPOJwco4clV8p2i1LBM3K_4,1607
spacy/lang/fa/tokenizer_exceptions.py,sha256=i0A6WF7_Fpws_N9LBIsagP0UdN_WO3xzAhzaNJgsQw8,65666
spacy/lang/fi/__init__.py,sha256=2YHMHSal5agYuklnZCNrQjbv94rv0gp7rIXKLMn5i7E,655
spacy/lang/fi/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fi/__pycache__/examples.cpython-312.pyc,,
spacy/lang/fi/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/fi/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/fi/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/fi/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/fi/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fi/examples.py,sha256=Y8OCJxQnDjWVeoACOcn_rTO-XXdEb2KC_qynFKSrPBQ,560
spacy/lang/fi/lex_attrs.py,sha256=CZl3cvPYta4tnWLcOjH7AMeSo61849lOByiFSOzXhyY,1131
spacy/lang/fi/punctuation.py,sha256=3gXtJIWl5NKFQVYOiyTouOLMlkagGd8TL4_WZBt6j7U,898
spacy/lang/fi/stop_words.py,sha256=fpzLwU1H0F_L3BzDjFCg90YfwVpGXibyJT517cCL3QM,6546
spacy/lang/fi/syntax_iterators.py,sha256=zzpmBGEXWPi3-0eYhf6ogZDvXJUlaarT-W1q51NLaPE,2460
spacy/lang/fi/tokenizer_exceptions.py,sha256=xvqsihjjiZVTNLaawOOsN7TD8KhnOifSGsdCFkYjy6M,2578
spacy/lang/fo/__init__.py,sha256=m2hYP20rHUcyZVPfuW4QvuGz0GyUOOMUz3edYuQfQXs,489
spacy/lang/fo/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fo/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fo/tokenizer_exceptions.py,sha256=FvMUl7puVxyJPQT6FKdW4_d-Kig8ERe5C1TSDG-ouFI,1394
spacy/lang/fr/__init__.py,sha256=YPpqGcl9C3Qha9w8VZJJauoF6taSvTMsR5Y5WKKohCA,1434
spacy/lang/fr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/fr/__pycache__/_tokenizer_exceptions_list.cpython-312.pyc,,
spacy/lang/fr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/fr/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/fr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/fr/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/fr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/fr/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/fr/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/fr/_tokenizer_exceptions_list.py,sha256=NFyro9MHB3ESK7XC5UrTR8JevRjdxhIsPfUpZnKZlr4,376238
spacy/lang/fr/examples.py,sha256=U19QRWst70fOOTYlQy-XNaBj5vdQZbvz6f6Ka8bc82g,960
spacy/lang/fr/lemmatizer.py,sha256=Wso4aXTdK8wxD7Q25mzAr32rDqTyyd0BRE3piG8XiE8,3103
spacy/lang/fr/lex_attrs.py,sha256=edsvNVRD_f7kauHOqf1LQPdN-o51vvdnzW5CN7uN_ik,1669
spacy/lang/fr/punctuation.py,sha256=ANKsXYbQqY7ysV5oqBYeocJf6Po60EQUI1pwXzLrUZc,1524
spacy/lang/fr/stop_words.py,sha256=sOe7bx2a3Q6XleqnE7ZTsvxrEO8hyHebB5cA7rg5BdA,3588
spacy/lang/fr/syntax_iterators.py,sha256=k2DlEqtN28dNa9v7C-83h-bICN-PCsohetLOfqvvJ1A,3209
spacy/lang/fr/tokenizer_exceptions.py,sha256=rYt7XuXvaY0Ia7H9JBl6cxZj4HJ1U4BY108LeMSZd_0,11674
spacy/lang/ga/__init__.py,sha256=P2qVh1Mp3DdlVRB0wJovV9IQOxCELqbse_wgiDGI67M,852
spacy/lang/ga/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ga/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/ga/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ga/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ga/lemmatizer.py,sha256=Z_T0JpoO7o0f0eEzdWtSM0asf-tXuqUbh7f8LF0RTNM,5098
spacy/lang/ga/stop_words.py,sha256=hhQX1ZWyWAbGR_xqjbMvNxn_NOGD2T57ndaYprSYmAI,651
spacy/lang/ga/tokenizer_exceptions.py,sha256=c1VQeG2to4xDfob9Uv1CvnO58Ee9nrCqfdQZjGwW4cQ,1953
spacy/lang/gd/__init__.py,sha256=V2CkAmyYks61k2qE3kAih6CeSyaIw8uao3crhOY6zXw,401
spacy/lang/gd/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/gd/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/gd/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/gd/stop_words.py,sha256=BOfbjZX8XGFh14CKT2tiddnCUABoIUb3Fn9ilUoh8yA,2756
spacy/lang/gd/tokenizer_exceptions.py,sha256=b0zqlz_-XdKGJJSYv4w4YTxzSoTPPM8Q2jeBJz9BTo4,26563
spacy/lang/grc/__init__.py,sha256=JOVM3Q73ohDUbg-AeMekQaiAeM0dPmLUjZW8T4vFzEk,642
spacy/lang/grc/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/grc/__pycache__/examples.cpython-312.pyc,,
spacy/lang/grc/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/grc/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/grc/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/grc/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/grc/examples.py,sha256=qc2idWpdjDmWvdJh-0A_-WfB5ZVUpGeSUZn0H_ZoG-U,1083
spacy/lang/grc/lex_attrs.py,sha256=KpguDXdOc7opj1BrgSlQ_QMhQFeEmFUNu7OV_O45Dgw,6953
spacy/lang/grc/punctuation.py,sha256=4Mupg3paIFU-R5PVnbr4PyYu0xUyQssRrC9HyWTv1ZE,1162
spacy/lang/grc/stop_words.py,sha256=BJnEOTTM-iK5qAwC7UGiE-O7HHjHIQS2TyPox4Z7keg,9425
spacy/lang/grc/tokenizer_exceptions.py,sha256=mNsw_rdRbuIYst4tdaLCmx4hIH4dHc1Mbl-x5G0d-lI,6879
spacy/lang/gu/__init__.py,sha256=HzOWL8fj9S7gq71Y71xAjOJcF_gkCMsft8DfVZIfsOQ,265
spacy/lang/gu/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/gu/__pycache__/examples.cpython-312.pyc,,
spacy/lang/gu/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/gu/examples.py,sha256=azYslIKVVuXf1XqKLtPgc2ZyUz0RsrVM43lebWbhlZQ,1233
spacy/lang/gu/stop_words.py,sha256=M5mZ-PJ6f-CP9R__YhCEkesa30i8wwG2-vwXk7fLSuI,1092
spacy/lang/he/__init__.py,sha256=qi57MrqoR98ERHu0GOHcKW6clDbarTAWsD0-5eVhNO0,408
spacy/lang/he/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/he/__pycache__/examples.cpython-312.pyc,,
spacy/lang/he/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/he/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/he/examples.py,sha256=Rsi8orYnRL8xiT9vfvOabOp1aF_uP8haN-dCB9EXE1s,1018
spacy/lang/he/lex_attrs.py,sha256=CmsTNg4bfhZyIZivs9Bkw2S2LL3MpsJzU1sdJ81n3wI,1854
spacy/lang/he/stop_words.py,sha256=lL3fSBVBI81D6uVi8vsZIUy37a1tYPER1GZMOvKsBpY,2078
spacy/lang/hi/__init__.py,sha256=cZohl0X1pJ8v_z1EK9YibDR-x8e0NUbQqM7b6dlt_Xc,321
spacy/lang/hi/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hi/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hi/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/hi/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hi/examples.py,sha256=rcUt8Ai2YA0Fwhyeovf8WpJH4jt18j09jPMgxKa8qSc,1538
spacy/lang/hi/lex_attrs.py,sha256=dfRkSNpFX72joE_WUACTpCFIvyvJ2n4BNQDt4BvVyRY,6076
spacy/lang/hi/stop_words.py,sha256=W3rrJYPIAwA02rb5cLgA_7TBXgpjRafbd6vxhCYGxlI,3214
spacy/lang/hr/__init__.py,sha256=OpLYm9nm2d_-qD_KDGM4oE3Q_i9I9L6tCquxKGTvRyw,265
spacy/lang/hr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hr/examples.py,sha256=QcYby6oQFVNzDetgOyfbiy8hcJqRbKH1GQyP5nYxtt0,504
spacy/lang/hr/lemma_lookup_license.txt,sha256=rr3OQRFnIIIQxPwBWYKSXVq9OQgIy0E_lXwOCbkdHwM,986
spacy/lang/hr/stop_words.py,sha256=YF0ae-a9QfOyk9cNohZadb_LT2_4qVrmZ66D1H5QEFs,2343
spacy/lang/hsb/__init__.py,sha256=LmneRw8WZitH-AqYXbZcvAOJQyTpXwDzuvD55u2w2gA,455
spacy/lang/hsb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hsb/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/hsb/examples.py,sha256=RkJUOETfPcnyk1cAAMndC7GWAEtUwm5umYzYjcYLEmg,655
spacy/lang/hsb/lex_attrs.py,sha256=FkHtl2P55IAoM9VXuKXgCcpC-wz6h3fufIXRQxB_jx0,1897
spacy/lang/hsb/stop_words.py,sha256=61RWIlR0ctPp-j1f0vhUDZ5cVcaPO4pJYyC7fSe0YgU,142
spacy/lang/hsb/tokenizer_exceptions.py,sha256=dGjreo4aifP6tfccNpPWmoXT-1Gzqz1Wone5IwTjePs,404
spacy/lang/hu/__init__.py,sha256=J6ItbOBVKjxEZ0QMQWsSPDWbHO8vSlpQNN_m7YJqxJE,605
spacy/lang/hu/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hu/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hu/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/hu/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hu/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/hu/examples.py,sha256=XSt0PLEabm2cqGiUPLluh3IPy5WRZ9HMKpm3B5T6uio,420
spacy/lang/hu/punctuation.py,sha256=6yBo6q1oRrqpMt34TCXVFNpPiGK58N7skXqCVXg0QdE,1566
spacy/lang/hu/stop_words.py,sha256=f27xbpOgpENj-af3GkCFj2rbDX_Zabe-1voHJ_4klik,1446
spacy/lang/hu/tokenizer_exceptions.py,sha256=2HAQsJ7-SWfMIVQjTydV9ohfiE94dQu0NDOjNM48f9M,9054
spacy/lang/hy/__init__.py,sha256=eQ3U7mm9muJ9fUS7dK2YaJ5EBJqbt2-hrAfThFoC35I,333
spacy/lang/hy/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/hy/__pycache__/examples.cpython-312.pyc,,
spacy/lang/hy/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/hy/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/hy/examples.py,sha256=qMN1LPP6W93LoNCSUrGGxaW1hy7EJQRTOzLbUbuFGzU,454
spacy/lang/hy/lex_attrs.py,sha256=L5bH2loHa_1QKGPmVNNzJinhyZjk4xJ3fHaWVSZgOKY,1215
spacy/lang/hy/stop_words.py,sha256=_FgA_mjC3WBjVGGT5ocZcizo18baVvVRxYIiQIU7Ct0,1174
spacy/lang/id/__init__.py,sha256=uKW0bZtps3EK5QRKBp-DqxPq7qo5FNI9OVpKibjyMPE,722
spacy/lang/id/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/id/__pycache__/_tokenizer_exceptions_list.cpython-312.pyc,,
spacy/lang/id/__pycache__/examples.cpython-312.pyc,,
spacy/lang/id/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/id/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/id/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/id/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/id/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/id/_tokenizer_exceptions_list.py,sha256=szqi4HgQvdkesccZ6lIfsuChTtcrbpvkonGHnr-hAUM,57501
spacy/lang/id/examples.py,sha256=zalF8AFkwpK1m47rijeZjD0aJZTylzu0NXXPNKoMZQs,744
spacy/lang/id/lex_attrs.py,sha256=d-2icoWp4ffuiuOdxDa7aJCM5jru-SUzENGOdwi9g6s,1342
spacy/lang/id/punctuation.py,sha256=GVwgYsGVMvCff1nSYa7Fdyl6dIQxaZdc_CY0uza0ZUk,2198
spacy/lang/id/stop_words.py,sha256=rfrNIC1F8G3KnF1PUUVgpEBKROsIQd1Ynd1ArMiRoFg,6625
spacy/lang/id/syntax_iterators.py,sha256=5D0d-ztAAd-U6nWEY6fpSyknIRXOemHYUAfZkWLQQSs,1579
spacy/lang/id/tokenizer_exceptions.py,sha256=ahoJdBtFEwqNQ9_lYc4hiiXd-y2HQwwSwIv_8jk5x1k,4424
spacy/lang/is/__init__.py,sha256=cfZ9upWQeDmuv1qwJ4EyCuA1aeZTvMKI5nZmoLkKWLk,269
spacy/lang/is/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/is/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/is/stop_words.py,sha256=EXvUI9qz7EXQvuATHfJDD9oF6oM4_exf1Uyv9hAvAx8,1177
spacy/lang/it/__init__.py,sha256=Al3e3lbPigjHnnaGGMxYYKc8f2pYNSJc_hXAYVr6Oa0,1280
spacy/lang/it/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/it/__pycache__/examples.cpython-312.pyc,,
spacy/lang/it/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/it/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/it/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/it/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/it/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/it/examples.py,sha256=LJ4T3pfU6GmnfaOpcCDrxABzZB72BFvKLLX5PuMzsBs,485
spacy/lang/it/lemmatizer.py,sha256=SZUX2cDKTJpiJ4j2eqpsE1IJWAXUWUlVVNchdwwcx-E,4747
spacy/lang/it/punctuation.py,sha256=AM0PrKWZzSH7bjm4-8MPF0Zkc0kHBb-vkEEo6utzxgk,887
spacy/lang/it/stop_words.py,sha256=agTiYrl4t-8vHlleZOjdFstYZD2HdQWtKoar7iMpKsY,4197
spacy/lang/it/syntax_iterators.py,sha256=nAO4AabPE2gOZDNzydkTs6y5VQCbjkyQkeZ36a-e470,3223
spacy/lang/it/tokenizer_exceptions.py,sha256=8SjeV9lapozI9Tiof7hv4-EbspX1RuU2NCCayevHQnQ,1224
spacy/lang/ja/__init__.py,sha256=0thH-Djm2SVYqkj25m0zVpOnwxeILZy19Y0NuEbPwXI,12913
spacy/lang/ja/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ja/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ja/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ja/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/ja/__pycache__/tag_bigram_map.cpython-312.pyc,,
spacy/lang/ja/__pycache__/tag_map.cpython-312.pyc,,
spacy/lang/ja/__pycache__/tag_orth_map.cpython-312.pyc,,
spacy/lang/ja/examples.py,sha256=8-O7TAHWXFykbPhC4NlTVxbrUmraQ-ZamzTtZEf3qu8,513
spacy/lang/ja/stop_words.py,sha256=L0DZgqvjvTpJYq2f0Wrkue5423i6gEqxJJnUNmMUhIA,1376
spacy/lang/ja/syntax_iterators.py,sha256=XkJYj6jtixAshjsE5jO4IeApes5YFgaGW_J-bajEdWg,1679
spacy/lang/ja/tag_bigram_map.py,sha256=vurQQpZV__0ZMuMnkgpXUHULzJ3AaX6NSPdi0P0-2OM,1675
spacy/lang/ja/tag_map.py,sha256=AMhUVFevNjvZEEgRVgYvS1hC2hKbvmwndVi-szgtx00,3880
spacy/lang/ja/tag_orth_map.py,sha256=XHc6dKfvs4-s3CO6-fgxobsBjosIxpc1WKGITqPTo-4,568
spacy/lang/kmr/__init__.py,sha256=MT9AfGfA_NjE_mb1U1Opp5tYdyBWhh02oOCPuYcBXF8,334
spacy/lang/kmr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/kmr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/kmr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/kmr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/kmr/examples.py,sha256=ZhjCwSaF9PFH05wx_noV8vx2hn5ndJQjAK6XhWAQSI0,1343
spacy/lang/kmr/lex_attrs.py,sha256=IyLaEtgAIB7cpBLKfJFrf9JkElPNmyVzaL5EBpMPNps,2466
spacy/lang/kmr/stop_words.py,sha256=-nmq6FG7YuU7z-EdG6NVY6EfberWhvqN7z2ZAYYCxc0,247
spacy/lang/kn/__init__.py,sha256=p81cc359bwddtx3KtP54U1KLnxk9Bl9IFbllhihsIx0,261
spacy/lang/kn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/kn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/kn/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/kn/examples.py,sha256=vmndHquv2M8-H9UHpjwJTeDYs9FnmluL2Etx9515_i0,1229
spacy/lang/kn/stop_words.py,sha256=heqqOvegkHUVuMyhfFdbvW9MXHoOmIjy42XE9JK-WtU,1339
spacy/lang/ko/__init__.py,sha256=W1AUICsEsbnwZrY_GqqzqB6ONwDvmK6J3TtwlmeARVI,4376
spacy/lang/ko/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ko/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ko/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ko/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ko/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ko/__pycache__/tag_map.cpython-312.pyc,,
spacy/lang/ko/examples.py,sha256=e7Hfr5AklFlXqV5Xgv9O11L9Si6y1KWllSuuVkm5tPc,544
spacy/lang/ko/lex_attrs.py,sha256=X-JI3uS3PtYO66iYKdlPOyBf_OPk1jPre54z0QPBHUU,1114
spacy/lang/ko/punctuation.py,sha256=4wG2SL8ViJtm35owSqwEv6YVclja1kEhhQH46y0GPtU,278
spacy/lang/ko/stop_words.py,sha256=mj55PmpDCzfFKD0S-aPWhfslUH676mngevUgGLq0N7M,416
spacy/lang/ko/tag_map.py,sha256=fQZ_Xp18WgERCAUhwEE66d8xuiyVNZk0yyzNMn6itf0,2047
spacy/lang/ky/__init__.py,sha256=OpNhmrHVctubgZy041HXHQ4DpSi0CAjDn1SiNgtQsCI,507
spacy/lang/ky/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ky/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ky/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ky/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ky/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ky/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ky/examples.py,sha256=Y0DLLQAJqFbw6kLhweF4BDsGUb9jh2e1Hk_1gTiustE,933
spacy/lang/ky/lex_attrs.py,sha256=dQQcKGMrbmyXHzr0TW3_ZhP_ded2ioeHliZjdXM4Qy4,973
spacy/lang/ky/punctuation.py,sha256=q1Z2KfirrbBeAY-BpcVn72P57Rh6qPAdgrQe5iBSItI,883
spacy/lang/ky/stop_words.py,sha256=CbB8f1CS366hu5FBbbbiCX6FB9ZDcNgzl58chZnUB9Q,1106
spacy/lang/ky/tokenizer_exceptions.py,sha256=do8egs4S9LNjrdshDS4X8mGvv6ArjN3AlyiIB6-is7E,2102
spacy/lang/la/__init__.py,sha256=Vp4bZPF-BHV3hf8lOl921BMc3rtyFBrm8Uf-y79s0qQ,515
spacy/lang/la/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/la/__pycache__/examples.cpython-312.pyc,,
spacy/lang/la/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/la/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/la/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/la/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/la/examples.py,sha256=nXvyD4f-jT40lgIMAgAEhsHBwJn67g5oArqJDZRiKp0,1168
spacy/lang/la/lex_attrs.py,sha256=OlnCNhrr7xJwNTIgYfd3AXLeXjyaqPH9wxDOBRIN7aM,2406
spacy/lang/la/stop_words.py,sha256=A94nMEoHTw1GyF1ushvyqFtmeDlESEHD-bEAU5pN9cQ,656
spacy/lang/la/syntax_iterators.py,sha256=yfWnxbN0glh6OuRsvfkkpD04L7QCeroCcORXDGAK1oc,2478
spacy/lang/la/tokenizer_exceptions.py,sha256=UtNEC4qY7zadbJINGrkDj3PCNzLTYkgvYb9emGReTuo,1260
spacy/lang/lb/__init__.py,sha256=Jz1cLNmPIXTZEOS5f9RoJHFbUqZUyuoT7-8cEvAWjTA,535
spacy/lang/lb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lb/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/lb/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lb/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/lb/examples.py,sha256=px80hXqUPVv0tJlsDQSJYmcVMm_6kdmvhM4qUrFIlqQ,921
spacy/lang/lb/lex_attrs.py,sha256=BSBWdP8QQ7Ajpgeti6860Jvmhag_c-izRpI6gUGGpkw,1382
spacy/lang/lb/punctuation.py,sha256=q_wiWEeUVM7Rcq9G6X8_4RJGr79bO-kdQwux2x7qIz4,662
spacy/lang/lb/stop_words.py,sha256=DTkY6Pgb1XjDv3bD1jG2eIkYvjygM6cTLr5j2SW10Eo,1338
spacy/lang/lb/tokenizer_exceptions.py,sha256=6QtK7topcWdheHh1UltLg7wvvs3-2woMVMnbtZwbfCQ,1219
spacy/lang/lex_attrs.py,sha256=Xm6KqUE55c6pdLTl-uE8h8bolLLVXO4JWJcHIVeJwlE,6156
spacy/lang/lg/__init__.py,sha256=FsjecflXNUSZNXJrP6yvdUlaRNASOJpxqNdSP1QU0-w,406
spacy/lang/lg/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lg/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lg/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/lg/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lg/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lg/examples.py,sha256=RcpWEuFjI9XmZktNs2YenN0WBhyUHRc6pXqd4yz1hTg,539
spacy/lang/lg/lex_attrs.py,sha256=OndRpwzBfwbOr6IY-IAjEU6LAMkfRvJvNbSVjpkt6No,2775
spacy/lang/lg/punctuation.py,sha256=NE246fA9_BmJyArRIROb48Z02x03wk2m1gmuHYbqlz0,602
spacy/lang/lg/stop_words.py,sha256=0zdX5GzLJQGOHHGlWetYbeyEaJfTTsZaWh07MvOkSoY,1380
spacy/lang/lij/__init__.py,sha256=PEM-ORQXvoGdDrcm0wQNHclZfCOryjtOeYfK6BmtqZw,448
spacy/lang/lij/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lij/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lij/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lij/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lij/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/lij/examples.py,sha256=4wG7CJIBIN5o9x3UaezvSAe4-K1Il72Hb3bTu-yYh38,411
spacy/lang/lij/punctuation.py,sha256=af8T0EMchBXzX53ecfwPF1yiRkAdnw3_hMulZ0xTgdM,280
spacy/lang/lij/stop_words.py,sha256=_8_wvIcuk3uWBBvxQsKKt5LLo3A5tvFloH_aM9z0ykk,892
spacy/lang/lij/tokenizer_exceptions.py,sha256=oT-gZFJ7y2HQaDit4ftUQsCkPndhgDaKwTFlkN8SDWY,919
spacy/lang/lt/__init__.py,sha256=d7BdjqldDUSE3k747BjW5Dl6LK-gQ0oBdCD_ta1C1aU,578
spacy/lang/lt/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lt/__pycache__/examples.cpython-312.pyc,,
spacy/lang/lt/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/lt/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/lt/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lt/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/lt/examples.py,sha256=xRt9Oa5AnmHf0pdfCpoOFdv2r54mzZfCaGyTYWD2f0w,620
spacy/lang/lt/lex_attrs.py,sha256=8gugw5vY22Owx1dsmmO-UYKClgo_5p70XoGIkbSMraQ,23614
spacy/lang/lt/punctuation.py,sha256=eZ-4c6zdQldS1I6RFHl8jeZBP38XmM5UHfAOASintZE,727
spacy/lang/lt/stop_words.py,sha256=QZAMwwPmw0LZRzen79mUA3C_8N3q16KmLSYeh-bLu74,21024
spacy/lang/lt/tokenizer_exceptions.py,sha256=9FcSmPheEa2dAqgWtaAXROl3u8NeTFM4oJWHrogHRd4,396
spacy/lang/lv/__init__.py,sha256=FZ3EzxzqP2Kr4U9jEGawZloMIcCtgxi73DSMeoKMCZI,261
spacy/lang/lv/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/lv/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/lv/stop_words.py,sha256=RmsiFu7FpypSXR47ylxFqwphsD1v1KlDShcpgWByJY4,1252
spacy/lang/mk/__init__.py,sha256=oNqkOmjzgdzkkvLZIJnzLhN38KHNRh85aAv9CjFUBoE,1568
spacy/lang/mk/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/mk/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/mk/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/mk/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/mk/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/mk/lemmatizer.py,sha256=w6teXALJc-AXklYe2CDnKl99XJ3We_H31GmfnSPgotg,1776
spacy/lang/mk/lex_attrs.py,sha256=QkW-7NK5tUwIQTL9HUYHTpFEJ5Czm52u9b2jrYb1Sc4,3606
spacy/lang/mk/stop_words.py,sha256=uqaWWVWbohSY5cVBxpBkXkAb5I_Of1VDkOLdFQdhWpg,9921
spacy/lang/mk/tokenizer_exceptions.py,sha256=u4R7dvVhA0JWhXpFLSoFCN-5S_7QwZT67QV6muwQ1WM,3669
spacy/lang/ml/__init__.py,sha256=kpgAMvVKz1bOKKEcgXLYU4gAhqSWVO0dRvkfmx5-VZ4,337
spacy/lang/ml/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ml/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ml/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ml/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ml/examples.py,sha256=kPYoIerYcUBl4See8O64pKnApQuL6OqaZKCtg-BTaQ0,1418
spacy/lang/ml/lex_attrs.py,sha256=OyaWrDTfLdpg2W_BdI4YP_M7qr8TZhD8d8rFG-YlAho,2420
spacy/lang/ml/stop_words.py,sha256=9qiBsIRcMYKSjoyd1rymt16ADnmncJ8CPH2jidkV9R8,197
spacy/lang/mr/__init__.py,sha256=z-e7xbSE196tn4T365D8x7a4fv1QLV46WvWqNcdKNC4,261
spacy/lang/mr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/mr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/mr/stop_words.py,sha256=FAmWTdp-CqIRshGvfke7l0bgSS2I5OOg28__hiDMvjw,2648
spacy/lang/ms/__init__.py,sha256=TCgZcVTf7IPAKAe8F9KqgrIbo0vmcch3mhxK-pSKaq0,702
spacy/lang/ms/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ms/__pycache__/_tokenizer_exceptions_list.cpython-312.pyc,,
spacy/lang/ms/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ms/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ms/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ms/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ms/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/ms/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ms/_tokenizer_exceptions_list.py,sha256=zxRzWOfMxGCod3g769ni6eS1bJrqA0GkzpxNWRNDNNE,29388
spacy/lang/ms/examples.py,sha256=SAPHTUy8oP7YZwkORMQIAIfhM2LUGm9euTkE7_sDs4o,697
spacy/lang/ms/lex_attrs.py,sha256=jLrCLFpdl4MnWZ0hVbejWLQSVC9H3X9mMeDSPIduRes,1330
spacy/lang/ms/punctuation.py,sha256=GX2G2VOBArn9WJe3Raxz7zh7KfxqtT5oV9BqMGpNMXY,2194
spacy/lang/ms/stop_words.py,sha256=rfrNIC1F8G3KnF1PUUVgpEBKROsIQd1Ynd1ArMiRoFg,6625
spacy/lang/ms/syntax_iterators.py,sha256=5D0d-ztAAd-U6nWEY6fpSyknIRXOemHYUAfZkWLQQSs,1579
spacy/lang/ms/tokenizer_exceptions.py,sha256=QAAqcEcRBWIAE2DEIPoLU_KoXpwzoax5bWOkugt3QM8,20641
spacy/lang/nb/__init__.py,sha256=5oGrVNSoUJhGYecM15IyW_AfxPzcL_Xyq5uM9SUrHmI,1325
spacy/lang/nb/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/nb/__pycache__/examples.cpython-312.pyc,,
spacy/lang/nb/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/nb/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/nb/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/nb/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/nb/examples.py,sha256=HN4wF08J9u3eVe0JcdSR57tY5QhUgk2ctd8awXvu4f0,442
spacy/lang/nb/punctuation.py,sha256=yFXBMTuERHTQZheCaea32CBPZTPf9e63_VNHnl2tLbk,1732
spacy/lang/nb/stop_words.py,sha256=Yb_pOJAkxa466znM8-uaXhJywLY4ga_xqU2WHIeVofI,1210
spacy/lang/nb/syntax_iterators.py,sha256=Wm5hYmwtISYSyQ5UfGlZgRVJ-hZ8DhMriuuuOlyRLGs,1562
spacy/lang/nb/tokenizer_exceptions.py,sha256=MiAiKl-5eXHY3IWubGe5LU4Y4FluU9nHOoow-KbAoHU,3290
spacy/lang/ne/__init__.py,sha256=lbkYEx2LOl87s96SCPg7IPaAxgAy2XEYd1SwTOhE8kQ,325
spacy/lang/ne/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ne/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ne/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ne/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ne/examples.py,sha256=H5WyGuVuVC5GEUzA-BixEcPatUHsbrq8CkCwH_kiuso,1122
spacy/lang/ne/lex_attrs.py,sha256=9a4F83z_LQktHUI8AjMbTwkQta4DFuflCypF3-Q2zBA,3369
spacy/lang/ne/stop_words.py,sha256=SkKIXg7nHfUdK27DSnfnh0g2Ud9Ba_P6wMC96mjZJHo,7629
spacy/lang/nl/__init__.py,sha256=2c97g1eIM6b_oN2HQXw7IW0co8ULFRtcbCmP5jPmWR0,1383
spacy/lang/nl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/nl/__pycache__/examples.cpython-312.pyc,,
spacy/lang/nl/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/nl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/nl/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/nl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/nl/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/nl/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/nl/examples.py,sha256=28vswa9lz5GCDMxOxdx2iVkdNxuCYH1WK2HjFOrEwi8,455
spacy/lang/nl/lemmatizer.py,sha256=V3ADssYt7NaB6HVh8qcaRvBNZ1Ryd3CH0nVQQuEnpuc,4740
spacy/lang/nl/lex_attrs.py,sha256=gqeNRbvSWNffj26XAtML8cSJd9pe0CLikdz1P7ECGoU,1342
spacy/lang/nl/punctuation.py,sha256=9Br77tJIBMWE-uswpw_axUFCCIL5eTbyecCeSA4Tbtk,1602
spacy/lang/nl/stop_words.py,sha256=Rr2DXFE-WPYgm28fHXllZHGO2OboVDIIMmFCVparArA,3162
spacy/lang/nl/syntax_iterators.py,sha256=2j6sua3CqeXwf3timdQFyI5eCMs5fRzkRpE43IrFndA,2943
spacy/lang/nl/tokenizer_exceptions.py,sha256=qoLbeHMwUhRkA1rUVvOkoYCx-XhYAx-IdcB2LVjm8s4,25905
spacy/lang/nn/__init__.py,sha256=L-LWr-KDaYUimw9VQ_N8Ukjry2ZDHqV_DZItkkGTOjc,600
spacy/lang/nn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/nn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/nn/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/nn/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/nn/examples.py,sha256=wmr2Dnn_BHUsm1zqcmgCszcIHTWZqPZKYOtllKnJiEk,690
spacy/lang/nn/punctuation.py,sha256=lMF61rbtjOTY2TJxBxGiP2jn2z8od1om4OtTX8AJBd0,1870
spacy/lang/nn/tokenizer_exceptions.py,sha256=UTN6KcbmCyUPygeEl686iMu0XZ4wHJqqwNXPjCoCTDI,3526
spacy/lang/norm_exceptions.py,sha256=ZGmzDTcCexrANn_gPdEqNbLiceSDaHh4n39sm-Zt5y4,1484
spacy/lang/pl/__init__.py,sha256=z408EdR-3_ejUPb7JlReIR4U8utnHp3T9mYvsRHv2vw,1413
spacy/lang/pl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/pl/__pycache__/examples.cpython-312.pyc,,
spacy/lang/pl/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/pl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/pl/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/pl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/pl/examples.py,sha256=HJilebEFe1z97PqOy9whydjGEXJUQO195Qpojg48V14,701
spacy/lang/pl/lemmatizer.py,sha256=RpIGwd3S0YxPR3Wc2RD3CTGcjZuXdO6cmOrbtCeZXLo,3652
spacy/lang/pl/lex_attrs.py,sha256=w0O_LTajPz8-FT2EvPVoA8m-cN-Zdq14b6RkbjKv85w,1257
spacy/lang/pl/punctuation.py,sha256=W5tYtS5_P4IoFHj-oTKOce30URpuIxEHqcjjV6Qtow0,1419
spacy/lang/pl/stop_words.py,sha256=ZGtJuxZ7afMHQNRBay2ma7JVGGZh8xIzDGtXjRheQkc,2438
spacy/lang/pt/__init__.py,sha256=2XMmHh87Bayn96ctKgC8aExi7zGRmnj0SjCYWZnLC0g,667
spacy/lang/pt/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/pt/__pycache__/examples.cpython-312.pyc,,
spacy/lang/pt/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/pt/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/pt/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/pt/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/pt/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/pt/examples.py,sha256=DVRcAlpxr4Nshpn9nHOqn6WI-PRQC42ZmxyIY3xfOx8,486
spacy/lang/pt/lex_attrs.py,sha256=1TkW3mhEsWvLXqDwBaZP4v4eHzu9OQXIhJo7IOWd6S0,2155
spacy/lang/pt/punctuation.py,sha256=ca_tUv2yVr1Cm3qp4gm79TJ6N2CzSXGD0Y104SGDPF4,469
spacy/lang/pt/stop_words.py,sha256=NhbgY6eIlAvV4GOXf5fQVd9qnD-lxUJyVj9_v_BVrc0,2632
spacy/lang/pt/syntax_iterators.py,sha256=01-Hiyb3tIGHnYvOENaP4hzZDz3XDrzvk895DlLwaGo,3173
spacy/lang/pt/tokenizer_exceptions.py,sha256=AHnPW3f-wCm-_6HQgckkmy6fij6iYgaHiKO9tP6g3yo,769
spacy/lang/punctuation.py,sha256=q6Pf4A04k4jfZu5fPjJtvGSbx9o-tmx5Am5K-3DnT0Y,2280
spacy/lang/ro/__init__.py,sha256=v7o-bF7JD89VuaQFSt7MHSt6u-mAd1P4b08xZUPAegs,804
spacy/lang/ro/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ro/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ro/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ro/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ro/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ro/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ro/examples.py,sha256=fih3NZrF7U8gQp-HG45MpTwR1s_iqjSUf5snsLkL8ro,620
spacy/lang/ro/lex_attrs.py,sha256=HAQbaFkzgODqDv9qplSVSNw-tsNIeAkCl-RgSowrEWY,1721
spacy/lang/ro/punctuation.py,sha256=lON9hgOw8uZVYYQy8d3PdgALyPfisu2m3kQXPakUKB4,3324
spacy/lang/ro/stop_words.py,sha256=3w-5tCaUQrO0eK9XUFdaltPKiOHQ5aPGneaxnCh5vWs,3444
spacy/lang/ro/tokenizer_exceptions.py,sha256=lEN2sB7mizKXWuucjlp4IEzlPfYmwCisQXIAADECcew,1555
spacy/lang/ru/__init__.py,sha256=cmCapNclWOYxPTivfpclQslkmuZZd2rLzWQ5hxYqqTg,1359
spacy/lang/ru/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ru/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ru/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/ru/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ru/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ru/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ru/examples.py,sha256=KyKqOedlBau3S6yC20YRk5yqPJhSEIHqryD7hzMWcTc,4621
spacy/lang/ru/lemmatizer.py,sha256=xCzNgNWPN58CLvtLDgTAb55YLlHkx_hhahPYrEilIog,8192
spacy/lang/ru/lex_attrs.py,sha256=LAW69sFQ7288wxh0pXgJ0VWLlUoT7xYhxZXoMLjq704,18961
spacy/lang/ru/stop_words.py,sha256=TIDDXCUlHrownYkmreNwqjibo_FU5PNBj8UaTUPHEOY,8467
spacy/lang/ru/tokenizer_exceptions.py,sha256=ujM5m2Pkb0UzHiMgE-J7jK1sEe1R3UR4fzcvAYagWkE,25801
spacy/lang/sa/__init__.py,sha256=_YExAAxTwDKsOsy6DS7JDDHaLDheMMePX7Yo987q82o,333
spacy/lang/sa/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sa/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sa/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sa/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sa/examples.py,sha256=ut7L8BMbtOhRaAOIpARNLestlRWL9u607CwHamEIziY,796
spacy/lang/sa/lex_attrs.py,sha256=BzQaYB4SxLYAvfifQIMUzZ9ojcjgx2ox_1nCiZvcE1U,4338
spacy/lang/sa/stop_words.py,sha256=PSmEfRkQRzIcqKAWTr5q-s5oO-q8E1cYr6tFeXF6OJw,9879
spacy/lang/si/__init__.py,sha256=HaN2UDXgp-c1FDCdrWeIpyZVPQbAzj7VSWF6NDctjq0,329
spacy/lang/si/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/si/__pycache__/examples.cpython-312.pyc,,
spacy/lang/si/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/si/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/si/examples.py,sha256=IYXhhVA7W-pgeucEW4mGCKh9ibnTOnUzqZvltH4sOJk,963
spacy/lang/si/lex_attrs.py,sha256=Ch9E4iWCc78Vwxzc6b6hex1JziLww9mPg8Su_yLgJeA,1314
spacy/lang/si/stop_words.py,sha256=Bo1UgDYSSCJDGckpMCfD4kqAwp_28_43obV0hmSXxB4,2602
spacy/lang/sk/__init__.py,sha256=oczD9sUK4R9vniQBQGo5suyvkT4qg3NXDDPxFUt9asc,325
spacy/lang/sk/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sk/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sk/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sk/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sk/examples.py,sha256=a27R3m7xM7FrGztxUiJCBkJOGyHBAOx53LMToZCz-dI,752
spacy/lang/sk/lex_attrs.py,sha256=LPiqigDoWakm-d9p4t10lB4o9Rsjie1WQMXk6RaYYxs,1129
spacy/lang/sk/stop_words.py,sha256=9M1izuYTadYb1Z39RBKlseL4PS_SYGqTZaRwsHXLgQk,3063
spacy/lang/sl/__init__.py,sha256=HS07jBx8-872xHyUhoSXVU2ywaC16cqlinVfna6mmqg,629
spacy/lang/sl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sl/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sl/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/sl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sl/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/sl/examples.py,sha256=eLstCmIbo1Zjh4y84ArrwoNLroS8EaqC4l1UYbBFFUk,593
spacy/lang/sl/lex_attrs.py,sha256=Ni6UbOcQAM1lvr7KRPq1x7Xgdcb64D0rKu4DXmj-RAI,6727
spacy/lang/sl/punctuation.py,sha256=KD-xVp-d98SYBdjhgbFajO_KyGkUiRGjgUxm0AwZvEE,3307
spacy/lang/sl/stop_words.py,sha256=vn_Gc9OjH_bP7GKsDavkzKf-9XlPyparLG7W7LFBzds,2562
spacy/lang/sl/tokenizer_exceptions.py,sha256=cXzDT3Li7D8HCXNWRutkym8lD4WgFVPIENrY4TXJLgY,13702
spacy/lang/sq/__init__.py,sha256=PFIpV9bONWHQ3DlQbeMORvm3y3HmYO3VFjvmxgcW9R0,265
spacy/lang/sq/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sq/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sq/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sq/examples.py,sha256=fHDerNI_lZqt5dvKOc4bJvAuy8IEZhbVvHDBFg_I2ug,481
spacy/lang/sq/stop_words.py,sha256=Z9BeFjdiCeh54CCVQy5gmUUqnc-3929YnRvBm3DkLUM,1439
spacy/lang/sr/__init__.py,sha256=DrDdWxVm2stHMjl7EJpZ4XNce22L3ZIM9fi4SA9WXvQ,566
spacy/lang/sr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sr/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/sr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sr/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/sr/examples.py,sha256=kXXFBobg6dRLOcaEXT5L7EW6_Gb9fa6TDOgX9qzIuSY,931
spacy/lang/sr/lemma_lookup_licence.txt,sha256=grEvnqf2QM4EdMoQEtglVRz73zHXSBLUw-l8QLEKnIM,1580
spacy/lang/sr/lex_attrs.py,sha256=ljFq70IR0q9HHEy-IOAaI3jQa24Rtb-RKWo4VPBcKTA,1490
spacy/lang/sr/punctuation.py,sha256=70XWsDuMM-AehakXKVQ5Hyiakjuo-Di-ChXD3ORIGsE,1005
spacy/lang/sr/stop_words.py,sha256=NZA6m9NUrzF2VdnFB4rPG8Yw8qB6a0XfbdnwFCnumNc,4288
spacy/lang/sr/tokenizer_exceptions.py,sha256=7MsxoarRrse7XR8Dl8fbM4EsZyhXtc1TZOr7Z8KuecA,3615
spacy/lang/sv/__init__.py,sha256=DT5-GrVNWwLCvqr22FkEsLfT5GZgwuAgOiVhROOIBJ4,1328
spacy/lang/sv/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/sv/__pycache__/examples.cpython-312.pyc,,
spacy/lang/sv/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/sv/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/sv/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/sv/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/sv/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/sv/examples.py,sha256=bGgneC4bRd7IKOujyrwhJ_zuw14ZvTEGVQfOhh4YnYM,455
spacy/lang/sv/lex_attrs.py,sha256=MSSexewzxkxNmxh5dqwHEKM89rR9ImoMD76Q3sa5F-w,1011
spacy/lang/sv/punctuation.py,sha256=AION2JV62SMGcPbSUmFOpZJVW89Bx0cG4aD8DFL_gsA,1063
spacy/lang/sv/stop_words.py,sha256=vOxCdNYwB3_yQ1J6qJ8fvg2ujTZy-VhO0rNhauybPs8,2611
spacy/lang/sv/syntax_iterators.py,sha256=ovVWW2bhM1vCDS-8GfBofuyPcdILMggMfRGbNp5yUC4,1570
spacy/lang/sv/tokenizer_exceptions.py,sha256=Px0xExIIAg3xA0UZ39o0RenNfaxpwtuSb5CP6bClzg0,3811
spacy/lang/ta/__init__.py,sha256=0PWfqEBJhMN9ereNtU3FWRs3J9RXpKRF103CoRjZtpg,321
spacy/lang/ta/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ta/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ta/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ta/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ta/examples.py,sha256=EV3SgndvOMJ4DSCLQdREezQeJtDPQljZ1he8b1uKwaY,2727
spacy/lang/ta/lex_attrs.py,sha256=E4naC6T33158evappxumYcX8RvxxZNOz4prP4z6k9Q8,2367
spacy/lang/ta/stop_words.py,sha256=O7cCwUSk8gvCjm3vc8AJTt6SyqNLFs3luBhGLGOZDAA,2149
spacy/lang/te/__init__.py,sha256=nP2EewTJFotZXPYbJrZx9HssMvoh4WuNuGs1bXDzYtQ,325
spacy/lang/te/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/te/__pycache__/examples.cpython-312.pyc,,
spacy/lang/te/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/te/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/te/examples.py,sha256=h8NXcL1kqqlC3jHjFgQNHeIHvd3fFebhm-4xT3Z9SfE,1263
spacy/lang/te/lex_attrs.py,sha256=LLGx2EJqiJZ4XO-5n9yUcN7J61FPb7d9sEHMgConWhs,1317
spacy/lang/te/stop_words.py,sha256=grEG5XgO80PZfB2FbU4_qOSq3TgH035FWVa7t-Q5MMo,1163
spacy/lang/th/__init__.py,sha256=gXGqQIMX0QS787ELlsGKETa3kX8SgjfjADMAjcXgReQ,1376
spacy/lang/th/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/th/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/th/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/th/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/th/lex_attrs.py,sha256=JaZY68UOc6tvERNsdA1lRDJftBQEo4CcSO2KUCMA9OU,1535
spacy/lang/th/stop_words.py,sha256=XDPxX727hZFYO_cag1bE2IJ5tvkxkC7saFViCCLwR-k,19538
spacy/lang/th/tokenizer_exceptions.py,sha256=b3fQsGZip7ydkh-_ftCNKgMOf83X9GtJsIjFNYTcoIQ,18771
spacy/lang/ti/__init__.py,sha256=wtJWKW05oxoQFewmqUgnworJQ_DA_OdYzRFIAqJlkg8,860
spacy/lang/ti/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ti/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ti/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ti/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ti/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ti/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/ti/examples.py,sha256=6AL1XkQCqnaOAFJlredzSwsRculdxRs9JsRp5ocY6YM,839
spacy/lang/ti/lex_attrs.py,sha256=GprYGv-NZ-KWX-oypdySINOjSauyPp25x_7sZOOsS6M,1581
spacy/lang/ti/punctuation.py,sha256=ATe5MajFhkSOLjmpFLpdMqiwF-p_QVY_Dl94BPXqCrs,573
spacy/lang/ti/stop_words.py,sha256=2fH5we0_yrBpueRhnLt3IZl1bDzRUk7jC5k0fKlhu2Y,2004
spacy/lang/ti/tokenizer_exceptions.py,sha256=siasLF-3vHTe-v7Aqor2bv-VacqsE4yTwxjoxHKVN6c,359
spacy/lang/tl/__init__.py,sha256=z4GFjq-KXjHkgHGjzjeHVFRrdx-6LLVkDHGVVloYmhQ,434
spacy/lang/tl/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tl/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tl/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tl/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/tl/lex_attrs.py,sha256=ZuSmrqEBR9L9PVgjtqyTD98XS4nzEzI48pcgXB-ALcE,998
spacy/lang/tl/stop_words.py,sha256=eLIUpFYpthu0IldXrxOFESniG6nqVARKX4BD9-CC-L8,1116
spacy/lang/tl/tokenizer_exceptions.py,sha256=BN5g_ZDDGPfi0GQdogKWWHtX4zEViSFWvmDMgz5UqwU,705
spacy/lang/tn/__init__.py,sha256=9Fnm-0jA8u323E2hLDrM6elQQ9LCOQXuEag5NLOGS5A,410
spacy/lang/tn/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tn/__pycache__/examples.cpython-312.pyc,,
spacy/lang/tn/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tn/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/tn/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tn/examples.py,sha256=NIZ7ErTpo6p-qQCXajonm0FOzySWTZjUCMszzRvgkfU,436
spacy/lang/tn/lex_attrs.py,sha256=ZJL_Z6SYUDGtNOLa1qGMdPO0bNRhkvZZGgnkFb9_KCA,2050
spacy/lang/tn/punctuation.py,sha256=NE246fA9_BmJyArRIROb48Z02x03wk2m1gmuHYbqlz0,602
spacy/lang/tn/stop_words.py,sha256=WEOgc_FnSf52V-mdMooCOPyjahEzvD1zHYZHUMytBmM,816
spacy/lang/tokenizer_exceptions.py,sha256=pkbY095NvhQz8_O4EvYzlWUXciZ2t0JPy__GwOA-_8s,3732
spacy/lang/tr/__init__.py,sha256=Riu35GNXV0Dh5BlyCrFncp3TLNGhxRc3i-jpPBOFHU0,567
spacy/lang/tr/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tr/__pycache__/examples.cpython-312.pyc,,
spacy/lang/tr/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tr/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tr/__pycache__/syntax_iterators.cpython-312.pyc,,
spacy/lang/tr/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/tr/examples.py,sha256=E0eaS42RgTuT9-R1SqHS-F8fgoirGm6hz5mZ12xCnAY,748
spacy/lang/tr/lex_attrs.py,sha256=WERxbqEXl7w1s3_18igBdeK_1W3Eia5QIDAP_kSq-tM,1761
spacy/lang/tr/stop_words.py,sha256=eva9AuhPLrVI3BLdu7faLSY34BXpQequHsbmsTLwciI,5063
spacy/lang/tr/syntax_iterators.py,sha256=JKRi0mc1Er0SI7jDwz0dbgt5OnUA9XY1apUR9Gw21oI,1912
spacy/lang/tr/tokenizer_exceptions.py,sha256=n9-Eq3K6gUyXByrEqSwGT5O1CQRTpZ3c3sA5I0d46rU,6281
spacy/lang/tt/__init__.py,sha256=dre2skBSPx72WSyPRtRuL406iJPJKJdX3VCs22T7ybk,503
spacy/lang/tt/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/tt/__pycache__/examples.cpython-312.pyc,,
spacy/lang/tt/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/tt/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/tt/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/tt/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/tt/examples.py,sha256=1pvKNtPIFow7_mayaj4cHqtyP5EWbVb_y13YTA21DxI,913
spacy/lang/tt/lex_attrs.py,sha256=48lx_3n_hdRppHJXtzO0h7OBkBgVTSkY-r6Q-aLR8ok,1175
spacy/lang/tt/punctuation.py,sha256=vGauR4NckQ3uDw8GAHSmpfohLVARO0pZyS2a8N-b63w,833
spacy/lang/tt/stop_words.py,sha256=8ORDfQ8g48stMfO-qGlL3DtBCYZIaZqMLTq17P5ElgI,18740
spacy/lang/tt/tokenizer_exceptions.py,sha256=MSx8M6k-93YIdUqbFUFcOL-6Sl1mPujgIc3hthf7LAI,1807
spacy/lang/uk/__init__.py,sha256=Z1a6M_oVmmv1TmSEdLmqqNHXIMOjqpubP2liYqfx_9M,1373
spacy/lang/uk/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/uk/__pycache__/examples.cpython-312.pyc,,
spacy/lang/uk/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/lang/uk/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/uk/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/uk/__pycache__/tokenizer_exceptions.cpython-312.pyc,,
spacy/lang/uk/examples.py,sha256=dc47hrk1fs2_alTzyxzaOVCV4fCEChmHTH29spcg6KY,1817
spacy/lang/uk/lemmatizer.py,sha256=vL5AwUTzIQm1lws1HiYbujzw7EP0an1QpI9BtEddqnQ,1761
spacy/lang/uk/lex_attrs.py,sha256=zsyQYi3usR5wtpF41c5NLHD0SaP5oJrIwNXy9LYhotM,1645
spacy/lang/uk/stop_words.py,sha256=U7hOjkC10YeEL3osa5W9z6RbfFAeVy2XWKJh9Qsa4vA,5356
spacy/lang/uk/tokenizer_exceptions.py,sha256=Hou3HOE6oQsbiujWGOBuBN6f-O8H7tJ0If5C-bnynz0,1424
spacy/lang/ur/__init__.py,sha256=EKYul3PYLUf_LLpQqGb-teqZXZIYWCb2FM206hT_KnY,480
spacy/lang/ur/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/ur/__pycache__/examples.cpython-312.pyc,,
spacy/lang/ur/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/ur/__pycache__/punctuation.cpython-312.pyc,,
spacy/lang/ur/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/ur/examples.py,sha256=yoJDYpxNh8DbFmO6dDIz-Hv5gCca3mdeESzhtRNLrr4,315
spacy/lang/ur/lex_attrs.py,sha256=MI4PQL6PG3DlouJQBVXB0i28lzd7coZhBUTY9gU-VEg,2282
spacy/lang/ur/punctuation.py,sha256=HWXmxBKR0LsoS0kq01YNYulgzl1MR5aXqRFzc_SPEZc,80
spacy/lang/ur/stop_words.py,sha256=pf7hT7MY-5R_ZzN1hgDwqU08nKPfQa47Ia6asVsPMAU,5235
spacy/lang/vi/__init__.py,sha256=oGNd0mCF_87Bac0oq4jVX9xnh7EEdBcxBdVTQvG_F4o,5689
spacy/lang/vi/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/vi/__pycache__/examples.cpython-312.pyc,,
spacy/lang/vi/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/vi/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/vi/examples.py,sha256=XE7nnoOZhfP6QhUOSVRdkf5RzsbCL5RNMeTaEC6rthQ,786
spacy/lang/vi/lex_attrs.py,sha256=lMnlLWodJHUHXbp8Qu2SFUObMn5Aandh0zSBpr8Fk0g,1441
spacy/lang/vi/stop_words.py,sha256=QgL_H7UlMX8MnHrg4nmo80_Z21gx4Cl1P8cuhFs7N3E,22544
spacy/lang/xx/__init__.py,sha256=V2Ww_qAgVKPIn-zlyj-HY7piYNCOM5cShDFGAXoe1Sg,278
spacy/lang/xx/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/xx/__pycache__/examples.cpython-312.pyc,,
spacy/lang/xx/examples.py,sha256=n3SJglw4nCNj-lmqNcbtxAxL8lSXzBuTOZpDLIi2dRU,8256
spacy/lang/yo/__init__.py,sha256=mmnw_yF8Olfip_B7Y6TQFQcTzO37rhYG04Xj1EtWoJw,325
spacy/lang/yo/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/yo/__pycache__/examples.cpython-312.pyc,,
spacy/lang/yo/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/yo/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/yo/examples.py,sha256=Vm6fSr-ZQHA_IO2vdiFZ6s-QYK6Q2AH_m5ufYGfudSw,1291
spacy/lang/yo/lex_attrs.py,sha256=v2Y-b-mp2dpG6rbUo4jLDboujSR2ucHrF4pRixJPT0c,2633
spacy/lang/yo/stop_words.py,sha256=c81W0td_RiCY231omW4l4wHsXhDYSlNE19BdTMkRl8I,617
spacy/lang/zh/__init__.py,sha256=qxcauXzomN96OFlDhh2J4-qTzTqorND_tFnovGLLNmE,13024
spacy/lang/zh/__pycache__/__init__.cpython-312.pyc,,
spacy/lang/zh/__pycache__/examples.cpython-312.pyc,,
spacy/lang/zh/__pycache__/lex_attrs.cpython-312.pyc,,
spacy/lang/zh/__pycache__/stop_words.cpython-312.pyc,,
spacy/lang/zh/examples.py,sha256=nWohZaaGZ6IP2PNCi2bI48gzkdBMfQJmY3animjlLp0,808
spacy/lang/zh/lex_attrs.py,sha256=brPQtpEFdBRXB-rAZrUw2dkehgui4pe4ZbJ3ZNWhnBo,1709
spacy/lang/zh/stop_words.py,sha256=hjVdk_pF5Xg3jiy9talatR0ofMhk_uATcGO_uWeQ064,15308
spacy/language.py,sha256=9FbS-e-Rr9AYPh0r2fC-H-5WBZ7fRea_bw4Lh-qB7bw,109214
spacy/lexeme.cp312-win_amd64.pyd,sha256=Mf81oRNpT-6VppwX31ztdJCMhxvDNxn_NrJuVlL9Q1I,108544
spacy/lexeme.cpp,sha256=84XmsHBjm72LdN9zc3gooZm5-A7TXwDaIkRHTGlUPng,989114
spacy/lexeme.pxd,sha256=70G_8aPNsdzLpTvuOHhvQcZkrJCsn94R9zScX_Hk42c,2780
spacy/lexeme.pyi,sha256=xTW6bXIzSAYqxPUCqcsYkKRXE8Y2coKdf6O8WcPmwFA,1496
spacy/lexeme.pyx,sha256=xpkdeB1IC-7dMRqkSjEUlNuQQAEKZTVlAoLq7gbcbks,17212
spacy/lookups.py,sha256=deNe8xXxlKH96QdkyLeSAbRE4CnHEdJYJVY2n6oP-s4,11165
spacy/matcher/__init__.py,sha256=kIb1D2M_eTAUSQM6kHJHz8VaBZPp66SmkFhS0IzhrTU,238
spacy/matcher/__pycache__/__init__.cpython-312.pyc,,
spacy/matcher/dependencymatcher.cp312-win_amd64.pyd,sha256=HnSSWqvnTuEoreCE6NBw0svCqDhmjPegmQHb-KqqxMw,263680
spacy/matcher/dependencymatcher.cpp,sha256=eNFIAxkinzXhYi-7FRB0WiWfwrJmykkICxB-FN0Neag,2083812
spacy/matcher/dependencymatcher.pyi,sha256=yE8MEwEkd-JetJiFcyGC49h5fRRJ4vvIGIB3ZH2bClM,2192
spacy/matcher/dependencymatcher.pyx,sha256=gEA2eI1mOC1x8u7STb8BXqv_hp_GY07KU1kNtnKULDM,18704
spacy/matcher/levenshtein.c,sha256=i_IbiFmv00UV-t-ZfB2wVVj1Vti6HqwshN_IpNmy7bg,353655
spacy/matcher/levenshtein.cp312-win_amd64.pyd,sha256=WLtqxL5VvmJRI3vwaIB9BA99Yl266pZyPRq0rV9lIgg,45056
spacy/matcher/levenshtein.pyx,sha256=v-GPEETH5NKD4_5_D6i6RmG72zG86HKpoEBlEbeBVN4,933
spacy/matcher/matcher.cp312-win_amd64.pyd,sha256=E0mRJm0VuCodqIsct6dNmF3_dV6IPe6bgK7b8WfyNKQ,386560
spacy/matcher/matcher.cpp,sha256=rUHVgma8e2KXx_cqYWxXtN4qNmpvBZfPsmQklvxpat4,2939329
spacy/matcher/matcher.pxd,sha256=sTnu_05ZZtEQmKFcn6px1Ch9lBClHuJIe8_9s2QbnZM,1610
spacy/matcher/matcher.pyi,sha256=cL3I2QAIa5pcBOGc8tPq3UwDLIaeOgwNJdvk09Y026E,1944
spacy/matcher/matcher.pyx,sha256=EeVoiz_UDnyOjNzU1t3Ee0c8kKG04fBL9AkhWVC-U68,51491
spacy/matcher/phrasematcher.cp312-win_amd64.pyd,sha256=_yIVcF7wBzUaR9fuG-x-kAx2MHKE0Iz7jf29xcouz78,199168
spacy/matcher/phrasematcher.cpp,sha256=mMxI_uZbet6BbaZ7yCw9_UvhvLTYQZpswMcRFd3EQXU,1717573
spacy/matcher/phrasematcher.pxd,sha256=Rjk3z1OLS34Fs04kPxXlS368fo4QwaUULBej9GdbMDk,576
spacy/matcher/phrasematcher.pyi,sha256=rOCTPV5ZwTo0y4JU9ts5NwohPsfxNs55djPGL8SdAAA,1083
spacy/matcher/phrasematcher.pyx,sha256=oGV_SgN7mhV_-sLQVqO0mAkzafJBhNcedX9cJvY6mm4,14628
spacy/matcher/polyleven.c,sha256=2Qn5qoQ4s7MopAg4UUKq-gQNL8RVse1Y1Ex3rxqmoSk,9955
spacy/ml/__init__.py,sha256=NJlyIGLT19zxcigWJTCYjLOMvlVofpkyZbeNjVZ75r4,111
spacy/ml/__pycache__/__init__.cpython-312.pyc,,
spacy/ml/__pycache__/_character_embed.cpython-312.pyc,,
spacy/ml/__pycache__/_precomputable_affine.cpython-312.pyc,,
spacy/ml/__pycache__/callbacks.cpython-312.pyc,,
spacy/ml/__pycache__/extract_ngrams.cpython-312.pyc,,
spacy/ml/__pycache__/extract_spans.cpython-312.pyc,,
spacy/ml/__pycache__/featureextractor.cpython-312.pyc,,
spacy/ml/__pycache__/staticvectors.cpython-312.pyc,,
spacy/ml/__pycache__/tb_framework.cpython-312.pyc,,
spacy/ml/_character_embed.py,sha256=GzPWy2F-ViTp9yONCm6Xn5bvZ6iVNnk1d_D6NNhYI78,2014
spacy/ml/_precomputable_affine.py,sha256=AuWZUnUc7TnfiWELQXGQ0qhh2lrNchJDfesninksOM8,5948
spacy/ml/callbacks.py,sha256=kUK5DMR0YY6aFmRbA0eWYVRDk57XaDETI3NmH7aVC6w,3789
spacy/ml/extract_ngrams.py,sha256=MBdhIuiFyqKVQHKDcQ7w6aBUFldvYBBzuW309pS-JDw,1180
spacy/ml/extract_spans.py,sha256=TdalDQmCeZ0PPs-2s1O4-Gh8LC1ks2zxsqX99jJWrLw,2327
spacy/ml/featureextractor.py,sha256=WwuutftCsbMmxugevs0lKax6gRl3hywC0WmPb20Rtug,989
spacy/ml/models/__init__.py,sha256=hiQA-_aMPpf3F5m2F8WArqARIZjg-M21tl-HfdIAdB8,267
spacy/ml/models/__pycache__/__init__.cpython-312.pyc,,
spacy/ml/models/__pycache__/entity_linker.cpython-312.pyc,,
spacy/ml/models/__pycache__/multi_task.cpython-312.pyc,,
spacy/ml/models/__pycache__/parser.cpython-312.pyc,,
spacy/ml/models/__pycache__/span_finder.cpython-312.pyc,,
spacy/ml/models/__pycache__/spancat.cpython-312.pyc,,
spacy/ml/models/__pycache__/tagger.cpython-312.pyc,,
spacy/ml/models/__pycache__/textcat.cpython-312.pyc,,
spacy/ml/models/__pycache__/tok2vec.cpython-312.pyc,,
spacy/ml/models/entity_linker.py,sha256=czvRWTskPYtOb11waiqjIHfox80lUIqmkZkH9qw-MKc,4165
spacy/ml/models/multi_task.py,sha256=xT9ZJNfERQhr5nNfE9eLgBcRj7cV2pdQ-HlbDLeVc9Q,9434
spacy/ml/models/parser.py,sha256=a9pmFKZ-aIiAs9mF4kMzG7-yv4cUvTuW0WXAhJv2KRs,7013
spacy/ml/models/span_finder.py,sha256=pgL09e2XH2gdJFbxgJ7Or-0xlkMA7-ItcaCDZN8LRmI,1223
spacy/ml/models/spancat.py,sha256=0JZ9gs5lsxLfnP9in3PrIky-O1Hu7uBes5pa_HSVLrc,2318
spacy/ml/models/tagger.py,sha256=NqCNqeijiy_SYcpjm2hUQC3OJ8CCI2F6JwwvDh20mbY,1240
spacy/ml/models/textcat.py,sha256=z21scH8umWVGdpFnP_nSEalXCpDWZY-yfsAd2f0E3l8,13151
spacy/ml/models/tok2vec.py,sha256=YhsqQNSROZ6_D5W0DhZA0KqJrMZvaPZIm7MJEx49bdE,13998
spacy/ml/parser_model.cp312-win_amd64.pyd,sha256=Tfl7ZMTg4hIMgqxPbYf3Zhw3IzuKj1k0rFVVT9h9GGA,246272
spacy/ml/parser_model.cpp,sha256=Xk9HV16DALBsAPV1jRJwzfTFwJWlzYIlle5TiQxk-Ws,2212059
spacy/ml/parser_model.pxd,sha256=p4XhrWYq57-cAKQZaaIPLe0LvHGpzy8Z_lG0Rg4_K5Q,1231
spacy/ml/parser_model.pyx,sha256=G9-eQpBHE31JVhTlZoCNHWczxhcpK1uRTolb7AeuS6M,19030
spacy/ml/staticvectors.py,sha256=EQxOLG5BB_caCBw2P6NJQr8S1N0VBHF7VEPleV7xMeE,4330
spacy/ml/tb_framework.py,sha256=L4uUzdOtIV-vzc3RATGVQlS140VM_Xll2HHNdiBocZQ,1470
spacy/morphology.cp312-win_amd64.pyd,sha256=SGxFX9ltAG4iqUt8vV6SrTelR5aPNBO6X-QB64dNrzI,96256
spacy/morphology.cpp,sha256=1dC3KlFEWdAbjDAweJJKB4cQv2ICsrdyIjy3YmkMJyI,829433
spacy/morphology.pxd,sha256=OWIhYZ17I6Za57a1f0dRSggqOZ121LtCF4hqNbsZhQk,851
spacy/morphology.pyx,sha256=K6AwX_P3BAdd3i0m90SG6Tl8egNThtaTkleawnHda8Q,8459
spacy/parts_of_speech.cp312-win_amd64.pyd,sha256=OZR5BbS-phXPQz800we8DC4FjamhoY28588YLRT9dPk,32768
spacy/parts_of_speech.cpp,sha256=kWiTSNEsJSoAmYNfPb0Aa329Uy65CeeejIDqJbG8yAQ,305379
spacy/parts_of_speech.pxd,sha256=Yo7beauYy1M_PDzX4UATOOjn20sa07bofhPTcCfTde4,283
spacy/parts_of_speech.pyx,sha256=C_lzfc3SVjkQQvK2O3NOdnhDiMU2ua0u1Tm67PL22Pc,607
spacy/pipe_analysis.py,sha256=JNEFuBjg9U-ddhblhqA0AhLGDtwHch_13nbvWp6SEwU,6380
spacy/pipeline/__init__.py,sha256=daO9R_CahTrTfeFxs73dAYmHngEkqBbncET5WWLGYvA,1324
spacy/pipeline/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/__pycache__/attributeruler.cpython-312.pyc,,
spacy/pipeline/__pycache__/edit_tree_lemmatizer.cpython-312.pyc,,
spacy/pipeline/__pycache__/entity_linker.cpython-312.pyc,,
spacy/pipeline/__pycache__/entityruler.cpython-312.pyc,,
spacy/pipeline/__pycache__/factories.cpython-312.pyc,,
spacy/pipeline/__pycache__/functions.cpython-312.pyc,,
spacy/pipeline/__pycache__/lemmatizer.cpython-312.pyc,,
spacy/pipeline/__pycache__/span_finder.cpython-312.pyc,,
spacy/pipeline/__pycache__/span_ruler.cpython-312.pyc,,
spacy/pipeline/__pycache__/spancat.cpython-312.pyc,,
spacy/pipeline/__pycache__/textcat.cpython-312.pyc,,
spacy/pipeline/__pycache__/textcat_multilabel.cpython-312.pyc,,
spacy/pipeline/__pycache__/tok2vec.cpython-312.pyc,,
spacy/pipeline/_edit_tree_internals/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_edit_tree_internals/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/_edit_tree_internals/__pycache__/schemas.cpython-312.pyc,,
spacy/pipeline/_edit_tree_internals/edit_trees.cp312-win_amd64.pyd,sha256=UFGJGusCkba7zf2RXDXPU1afWlAFLIbaYwTJAuaDh80,124928
spacy/pipeline/_edit_tree_internals/edit_trees.cpp,sha256=o0ff1AfdgT-41X9uoM1LRmQzipR8vkYIElLdgDaZ8Gw,890077
spacy/pipeline/_edit_tree_internals/edit_trees.pxd,sha256=-oIrW2funh_WtHlcoNJ59PPB6umcgGBki03RzSm3naM,3575
spacy/pipeline/_edit_tree_internals/edit_trees.pyx,sha256=4uRLM6z6D6_sb01iUM2RUtv95Cx7eYRzuugX9qFXVhk,10965
spacy/pipeline/_edit_tree_internals/schemas.py,sha256=uq9AmVxhy6vAaHzIHAdGCNqSdI9SYPwjn4Ynil6scTk,1718
spacy/pipeline/_parser_internals/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_parser_internals/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/pipeline/_parser_internals/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/_parser_internals/_beam_utils.cp312-win_amd64.pyd,sha256=w0X-0FEwTzKX96FyE1MjLeHJF5SBKA-S9621FVyEy1M,226304
spacy/pipeline/_parser_internals/_beam_utils.cpp,sha256=1CMwnQXdYPs18G8Dc7N7Z5IpwbSvtsPrU2BxKa-_M0U,1985518
spacy/pipeline/_parser_internals/_beam_utils.pxd,sha256=cvZkeBdbG9IXGYh5-HjX9qkVqiovG5ARh-E5_SyWiwE,262
spacy/pipeline/_parser_internals/_beam_utils.pyx,sha256=udUyONbruB4OVKEZLpse53XavUdOGxZ_COUVsp_KwQ4,11830
spacy/pipeline/_parser_internals/_state.cp312-win_amd64.pyd,sha256=xz7mFJkkugbwmzAMF1-jHjHUvNkC9EErDkU0AynMuN4,24064
spacy/pipeline/_parser_internals/_state.cpp,sha256=Igd8K0Guv_ZCk_miKEfnSbJlanZ9m66ClU7if6YPBag,586535
spacy/pipeline/_parser_internals/_state.pxd,sha256=2TEPcpWL29xKwZFBiqf2Pmanq0mfk68KLhe0nAorox4,13468
spacy/pipeline/_parser_internals/_state.pyx,sha256=BHrIYTbTg0wPXDU4nEu7Ep0XlDEAfwyyZzHZlzdkFeA,25
spacy/pipeline/_parser_internals/arc_eager.cp312-win_amd64.pyd,sha256=i2PoOFGJ5bX5gWcTFCoxuT96UoUqkprNU8VUcXfwADQ,312832
spacy/pipeline/_parser_internals/arc_eager.cpp,sha256=DkG92hlp9i0dKTFP5QjklGsWs1YN9g_OBpmWGdnydcM,2658244
spacy/pipeline/_parser_internals/arc_eager.pxd,sha256=sSiPLSH8mvXad3ynHKWfYJtkWZAkHGpAn4PUDCWWXBo,218
spacy/pipeline/_parser_internals/arc_eager.pyx,sha256=o5bLQp1qGvlUo2utOtCTivZ5IQA22tWDmSmd7TEfN_8,31554
spacy/pipeline/_parser_internals/ner.cp312-win_amd64.pyd,sha256=jQnTMqF6lzRxu1dCcGi3hddeczBf23C9mSgUcFZrp1M,235520
spacy/pipeline/_parser_internals/ner.cpp,sha256=ElcUUNqUMdcE-c7ISSLfkL-yToTw_sd0ACfQy9n9Zdc,2298325
spacy/pipeline/_parser_internals/ner.pxd,sha256=t4C1A08YZB6G8pg7ewex8XCdqfGgRP51-ML4PwHi4X8,109
spacy/pipeline/_parser_internals/ner.pyx,sha256=Mrgz6Z4VfB4hvzR7vE6L0Mrj3hIWr9N_DEGgmUD6wgY,23940
spacy/pipeline/_parser_internals/nonproj.cp312-win_amd64.pyd,sha256=GYAdB6JedAa3MFOfqtx1R92Vp7UKHSAZRYKVolKEZSs,190976
spacy/pipeline/_parser_internals/nonproj.cpp,sha256=PlHnlLMBP8wXJ15MCSFYRF_7GYDemOLWrGShh0QpNsU,1544382
spacy/pipeline/_parser_internals/nonproj.hh,sha256=O2QsGbVxKQ8Y9GBho6javj3wsSCV8NRT2ND0eHOssxA,198
spacy/pipeline/_parser_internals/nonproj.pxd,sha256=MoAQutcnqG2xc5JrrqnkiyU1InuGNoVjhUAUpLKiGLU,139
spacy/pipeline/_parser_internals/nonproj.pyx,sha256=u6fpklrA7BSAbk7JewP1ksxoxLqbs6rMxiFxJHWrHX8,8919
spacy/pipeline/_parser_internals/stateclass.cp312-win_amd64.pyd,sha256=SPyNgdgA4-NLGJ84KrAEj5KAzOjWL-OnfkkRHB0aUJ0,207360
spacy/pipeline/_parser_internals/stateclass.cpp,sha256=N0YAk_3DTyIvnVKYvC1WXbpCz4ovOPz8BnFk8wQY0-I,1788334
spacy/pipeline/_parser_internals/stateclass.pxd,sha256=lUZokB62t67xzd8l4FQqK5Jl-bTZizbk6QVpRt1KZ2Q,771
spacy/pipeline/_parser_internals/stateclass.pyx,sha256=5b7smN38M1aKnCyTO05YnpUffmPSEjQmlIfAKbmxI4U,4801
spacy/pipeline/_parser_internals/transition_system.cp312-win_amd64.pyd,sha256=193cSkCdcwkeP33Jh9B3ba_yog3MPtYYZIgCMzqPBoQ,224256
spacy/pipeline/_parser_internals/transition_system.cpp,sha256=ImPcH0khIKdCNaGdyT5QmYfKaMuKVb7rF2xPisBXZoA,1842150
spacy/pipeline/_parser_internals/transition_system.pxd,sha256=Ic2hwFgRPG59ssVEMNxzDAWd9bGUByU9VzGYcpkq-dg,1861
spacy/pipeline/_parser_internals/transition_system.pyx,sha256=72YoEOICK96LAUzCt6M1PqmYE-Y1p1XHTS2Dw5I0Jvo,9388
spacy/pipeline/attributeruler.py,sha256=F54dYWbVuo6KsZvhVpZ4PF--q0TrvDeGX-hdb7boDiM,13954
spacy/pipeline/dep_parser.cp312-win_amd64.pyd,sha256=QHMlkodakdwRtKqhmLhfAmJ4uaYIkL-Nza9QA4_3vOw,177152
spacy/pipeline/dep_parser.cpp,sha256=Tc8Al9u5pah0Ut0HrIxuRr3VUWgy7YMUQW1vA8bH2aI,1699087
spacy/pipeline/dep_parser.pyx,sha256=TT1EvcHvormU_AHQwHi-W4Rs4SdEJUY6WrjBMn8g6dY,5912
spacy/pipeline/edit_tree_lemmatizer.py,sha256=8nmB4uSeuyT1veeCYj2r2xF4paV1Vfv2sv2xd-90-FY,15192
spacy/pipeline/entity_linker.py,sha256=7x5nyFCKaDqZULtAlpyVrg1l_iH0SJqtY2FWhzFfUd0,24770
spacy/pipeline/entityruler.py,sha256=DspLB1CgTW6W9yXnr6zogB_mStjloeCPMIsJbiNk1Fo,20778
spacy/pipeline/factories.py,sha256=v6eR1LZIjKw6vUo64LlUoT5NZd3K47ZiqfsuLJh5O2A,27749
spacy/pipeline/functions.py,sha256=hMhYyzZU0PZUfANtF75kCE-ho18xY5Fo6Y3jG3FHe1k,6807
spacy/pipeline/legacy/__init__.py,sha256=1j5edMXlnmpKU_OkEj4p6Nt2SP5vKbRprVWX2L3g-p8,77
spacy/pipeline/legacy/__pycache__/__init__.cpython-312.pyc,,
spacy/pipeline/legacy/__pycache__/entity_linker.cpython-312.pyc,,
spacy/pipeline/legacy/entity_linker.py,sha256=NUsNlC5rUSMVHW8OUMiUvnxQLfn1YV-psMo4gOfHUjQ,19210
spacy/pipeline/lemmatizer.py,sha256=zpzgep8hal84zqQ7pG3Zfb01wKqqRn0EKB7ah3fky2Q,12223
spacy/pipeline/morphologizer.cp312-win_amd64.pyd,sha256=L5nF_i0RV1l1hf-9tevE4VmeRzG8AkmaSefMpis71jE,203264
spacy/pipeline/morphologizer.cpp,sha256=ibhpdzBVp2kjTOA5-XX2VwTx0dwkjrkq3ENSE4rZa-0,1597950
spacy/pipeline/morphologizer.pyx,sha256=XmLgS1pi3suxkKRUZhXJVFcJbJaRM0HSCj41DfP6W-k,12535
spacy/pipeline/multitask.cp312-win_amd64.pyd,sha256=sj0cMFUyNvkRbeEN4jsAiwx-KVflGUiWdPaBs3geAp8,120320
spacy/pipeline/multitask.cpp,sha256=illJYarACbBclSv_0nElG_5tujKcqtaAqNrvuI7BaU8,734733
spacy/pipeline/multitask.pyx,sha256=BNkeJgxkfWM3dlWKazTfvwNwu89s5UGguB-HEmxA26w,7621
spacy/pipeline/ner.cp312-win_amd64.pyd,sha256=hvVFwCqVafoak-CLVcp-ahYmeVinWCUAI2JVPgGkxFg,174080
spacy/pipeline/ner.cpp,sha256=-fEghvwm-vczS_JkPuaR-Wq9oSBSbrrPC-ggbIUBrUE,1684202
spacy/pipeline/ner.pyx,sha256=WH98Lbln220GPVWVQHmb_g0qXrcoqh-WE8N38Y8YMVU,4253
spacy/pipeline/pipe.cp312-win_amd64.pyd,sha256=lSi1U0PONSfVdPmxy4i3H1LU1Kbc6btfK2rl9mJydmU,169984
spacy/pipeline/pipe.cpp,sha256=tMA_ALukKJvaOd4lubAAgx42rqI5OdsczAiBi7-B31I,1428468
spacy/pipeline/pipe.pxd,sha256=mQBwT5sVlL33DiQa9dMiw5C95c_DHpQhsZsXLPRizug,44
spacy/pipeline/pipe.pyi,sha256=cSVU7tUfDWQAqrOFf-BMTC5VJWe4kdslaOIOYr_cIIg,1299
spacy/pipeline/pipe.pyx,sha256=C598xDCj205D3ucnZtDHDp4ofG-fStYOIXmqAinOxns,5434
spacy/pipeline/sentencizer.cp312-win_amd64.pyd,sha256=3QE2U1dV95O1o2tSLrvKLhJRuQKf2Eiz706kXyp_Bcc,180224
spacy/pipeline/sentencizer.cpp,sha256=vlSj4eJkv31DSOrOt_8CnIxeVAjPbxhuBDSp93FuaFI,1497706
spacy/pipeline/sentencizer.pyx,sha256=wb8urVPsygJv7CSxbNPBUrEmPrD73xB-9t71Lx0viMw,6719
spacy/pipeline/senter.cp312-win_amd64.pyd,sha256=vT7QlNBAqPK8Ybz7AjEuImJNMh1ZEAeazWJmiVby0xk,180224
spacy/pipeline/senter.cpp,sha256=mAiBUiuDWecMRfLMOTA9ky3mmPom6YyZ3Nkicio8p_g,1460468
spacy/pipeline/senter.pyx,sha256=QnyBpbFo9i8cm71z-7nhDly6_R_kHqfBIx8SLGCyb1g,6484
spacy/pipeline/span_finder.py,sha256=K_07xtM7j0n18FrpxeG-_EqG_dyofHAsX0__wvYlSNQ,10867
spacy/pipeline/span_ruler.py,sha256=srKGmK2LwNXHrrT3Y1-SrwXC2Zq2YS_CUoCchqI-Pgw,19501
spacy/pipeline/spancat.py,sha256=vGQgTpRXaqYsZyRyIfkFOP2utbuU5C9VIO15SbUWNoE,25657
spacy/pipeline/tagger.cp312-win_amd64.pyd,sha256=67Xefv_5Bk2n04FvA9YIp5ZiWgZCjQ2mSMzm9tuEpO4,223232
spacy/pipeline/tagger.cpp,sha256=u-lOQT_0QOlyuyjBeop55L0ZIFtHR8ow2FiHiLGEJvI,1691236
spacy/pipeline/tagger.pyx,sha256=nVoCTt6din-TjHI4Z_9SOK64nfbs2r6sIdB7eBYjB18,11768
spacy/pipeline/textcat.py,sha256=g3rjj0aO54KyaaqQoDD4je6u4pHhEs-yALra6QqI_mg,14421
spacy/pipeline/textcat_multilabel.py,sha256=FWL_vIEX-MKiZoNbf081_BxkCPe61HZJUcZ3-yMlv28,5758
spacy/pipeline/tok2vec.py,sha256=u7f7U7BiIUZRmcJpAtXxBVBfN5tesrcSlpewqwrv9lY,13801
spacy/pipeline/trainable_pipe.cp312-win_amd64.pyd,sha256=43eaf-MMN-UDksGvVoVMn9xCbKt0waOBD35TblFXlYg,250880
spacy/pipeline/trainable_pipe.cpp,sha256=lMh0YU3U21uSH9dNdEO2SWnOLljcdyuBF2nUUfIGBtc,1888851
spacy/pipeline/trainable_pipe.pxd,sha256=lZQNYUVj8u_Ysgtcs-2wZpbUUrpzVqRiDbBI0PONjPg,208
spacy/pipeline/trainable_pipe.pyx,sha256=nbGtfD2OEnN3Ni-evSh4RlkWiyp9TORINuE9RWZLK7c,14364
spacy/pipeline/transition_parser.cp312-win_amd64.pyd,sha256=IBTe7qIAw3YfYR1w64D-Jyji9YYUOl1zg4ZOAuG5trY,355328
spacy/pipeline/transition_parser.cpp,sha256=n5ycXddt64AFtC_S8WCjNkMez3d96lXtMlgH5P7IyKQ,2630273
spacy/pipeline/transition_parser.pxd,sha256=8knaZLWbpkAa1m5h54J6DvNUecmITdz1071dypcI1H4,831
spacy/pipeline/transition_parser.pyx,sha256=TRqv-eK9xKOYy_5jq1NctLRgAHI7tdXSaK0exwYugdI,28137
spacy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/registrations.py,sha256=KQMKlDscjH5Bu0ZVAYKbVeS6cConeRhbfmb07OB6gKw,11616
spacy/schemas.py,sha256=82PpvyyioLYzyBNluZTbPfrJ_6UCtvzPeieH1CljDSY,22237
spacy/scorer.py,sha256=Lt7L1pnApp-kPWS1AiczH1ZISvWILwrqO4THz_8rZLM,46935
spacy/strings.cp312-win_amd64.pyd,sha256=8UEMHoCGZGzuoLlYkMgkCsWwClqI2sR-RxnMuEDJ3y0,110592
spacy/strings.cpp,sha256=rX7iV2qlRrWlsaCUFCJkSyKfrKD4m8AS2fUY6xrK-CQ,776896
spacy/strings.pxd,sha256=XotAlwmHkv9XM52h74QvHgxw6cjJ9krEmYtKkBOB90s,871
spacy/strings.pyi,sha256=1a3dI9kPz1GOHd9rNQd1VFT8UGaCJFqXhAITzhPT_xI,1123
spacy/strings.pyx,sha256=0VwALkKBQsyjusDOGR2U-IUmJexHmdd7U-91FfeE9hI,14411
spacy/structs.pxd,sha256=H8CcXDFyDOb-JSu_56izd08oQfRXMfpGuHWyPyGVNRQ,2615
spacy/symbols.cp312-win_amd64.pyd,sha256=xD67gUiqeZkc4A_1igSg-_g3-lc7OAACkfoLvHJTd1Q,84992
spacy/symbols.cpp,sha256=3sI1d1sO8bsOzivV9MZsieo19DA_HJ9hfWEBluTW_jA,703295
spacy/symbols.pxd,sha256=RVen8KP27p8CgQ5pLAkOUEjtjWWYHrkGBnhuKz-84I4,7412
spacy/symbols.pyx,sha256=v7eadInEoIIiaszY0jACa-7NgiC6-Tqo_RLd9N9pYfI,14687
spacy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/__pycache__/conftest.cpython-312.pyc,,
spacy/tests/__pycache__/enable_gpu.cpython-312.pyc,,
spacy/tests/__pycache__/test_architectures.cpython-312.pyc,,
spacy/tests/__pycache__/test_cli.cpython-312.pyc,,
spacy/tests/__pycache__/test_cli_app.cpython-312.pyc,,
spacy/tests/__pycache__/test_displacy.cpython-312.pyc,,
spacy/tests/__pycache__/test_errors.cpython-312.pyc,,
spacy/tests/__pycache__/test_factory_imports.cpython-312.pyc,,
spacy/tests/__pycache__/test_factory_registrations.cpython-312.pyc,,
spacy/tests/__pycache__/test_language.cpython-312.pyc,,
spacy/tests/__pycache__/test_misc.cpython-312.pyc,,
spacy/tests/__pycache__/test_models.cpython-312.pyc,,
spacy/tests/__pycache__/test_pickles.cpython-312.pyc,,
spacy/tests/__pycache__/test_registry_population.cpython-312.pyc,,
spacy/tests/__pycache__/test_scorer.cpython-312.pyc,,
spacy/tests/__pycache__/test_ty.cpython-312.pyc,,
spacy/tests/__pycache__/tok2vec.cpython-312.pyc,,
spacy/tests/__pycache__/util.cpython-312.pyc,,
spacy/tests/conftest.py,sha256=NfvOU-GVaobs-yqM4GTmNWOsQ1ouvMugTzwEcp7Bx1U,12430
spacy/tests/doc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/doc/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_add_entities.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_array.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_creation.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_doc_api.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_graph.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_json_doc_conversion.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_morphanalysis.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_pickle_doc.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_retokenize_merge.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_retokenize_split.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_span.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_span_group.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_token_api.cpython-312.pyc,,
spacy/tests/doc/__pycache__/test_underscore.cpython-312.pyc,,
spacy/tests/doc/test_add_entities.py,sha256=GElMSdbkH2EpAEXPhoCBebEHy4elSApX2FzlvZUNEYo,1861
spacy/tests/doc/test_array.py,sha256=e-q6i2vq8UdN--P2u1qjTgm3gBYJoT5-njdeeVM1rCY,5124
spacy/tests/doc/test_creation.py,sha256=lAvqP9rqJi52oL8WY8fNMFHfZDfl6a6FRve_jBqqvz4,2731
spacy/tests/doc/test_doc_api.py,sha256=Ph2YpECW_t_D0PdRz2e-NtqWUDwvZSHnpBr8blKKy4A,36922
spacy/tests/doc/test_graph.py,sha256=6z5uFDXYars77gLqt6_XsL504VmEhHsVpoV9aK5Ym00,1867
spacy/tests/doc/test_json_doc_conversion.py,sha256=3Rev7YR54J9WD83CFU-J12uU9pRx7Q0unezalWzffSA,14043
spacy/tests/doc/test_morphanalysis.py,sha256=K-haVQ8osFVzftsc82IWZZ_Q9a4FQGVncw6Kmp4gBy4,3245
spacy/tests/doc/test_pickle_doc.py,sha256=ZdHn5IHK2M8xtY9URShdODXn1qUW_8dOFos-TuhIDf0,1524
spacy/tests/doc/test_retokenize_merge.py,sha256=ES35gW76xC7KExE0Q81gjb9eIr12LWq_YrQH0QtLA20,19451
spacy/tests/doc/test_retokenize_split.py,sha256=TQsW2atsDBkasWjxJHqSKLBY7AxDCSnDC3itkxvb--E,11233
spacy/tests/doc/test_span.py,sha256=RnaG6TyI6OViGaSxH_YKSjYzWgCQTrSit1xgXPqSx7c,26418
spacy/tests/doc/test_span_group.py,sha256=iQtIV7EBnA-p5dIv5Hft2HUdB58DuB_KpNRMRiFXNPc,8947
spacy/tests/doc/test_token_api.py,sha256=Db8oG3XptLbeNrePmALN6WmPW6IV8MO7OzAcHnWBnxk,11719
spacy/tests/doc/test_underscore.py,sha256=iSfObYTRRCrZFBCHpREgqznYRBRwePRpilnLev6LIG0,5739
spacy/tests/enable_gpu.py,sha256=3SmrS2K52tlcV7qrld5UzkTMF2zKfit0ptIU68_D_TE,48
spacy/tests/factory_registrations.json,sha256=A48e65CZPYF7nu7SulonLqagaNmsqSY2Z2xpslHeKa4,3621
spacy/tests/lang/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/__pycache__/test_attrs.cpython-312.pyc,,
spacy/tests/lang/__pycache__/test_initialize.cpython-312.pyc,,
spacy/tests/lang/__pycache__/test_lemmatizers.cpython-312.pyc,,
spacy/tests/lang/af/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/af/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/af/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/af/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/af/test_text.py,sha256=38UgXyHz3N1gJHxLRo19WsXut-pp_Uh8NNBi_Hg0PTY,953
spacy/tests/lang/af/test_tokenizer.py,sha256=WJZHJv4D4V3pIyBSm7I7TItz4Vaq1x2WiGUOLEWPHg8,739
spacy/tests/lang/am/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/am/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/am/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/am/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/am/test_exception.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/am/test_text.py,sha256=80u6QPuD3XDcfKU_IbjyWZkaslunfLrIr5-YwZhgboU,1888
spacy/tests/lang/ar/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ar/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ar/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/ar/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ar/test_exceptions.py,sha256=hM9owHEhXVNhksqQ4b81H4d6r6ljmeCbFJewJAKvOYM,641
spacy/tests/lang/ar/test_text.py,sha256=4yvJ035BgYB9cUzEoGpJIPuhOiGQ01ChtPjDuhB2xQ4,830
spacy/tests/lang/bn/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/bn/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/bn/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/bn/test_tokenizer.py,sha256=_0XlFYY-wcTQZeJ0maHB9zHbV_qeJVgwAN9ZdH9DTrY,3658
spacy/tests/lang/bo/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/bo/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/bo/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/bo/test_text.py,sha256=4KZwrGWcTR_LI4oaYNXGrq3YmlWNIT6_J5_BfDdMiDA,538
spacy/tests/lang/ca/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ca/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ca/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/ca/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/ca/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ca/test_exception.py,sha256=yPcIuayC-qGnDPV1hNB54iuE1Tq2dgruMrwZlid7AsQ,649
spacy/tests/lang/ca/test_prefix_suffix_infix.py,sha256=kEY88p34-SDccWCwIaltNb62d5ow9oMVr499mMPrdb0,511
spacy/tests/lang/ca/test_text.py,sha256=s_QVnqAMS17c_UtDdqOU6Sor5IauxaIyfXJRB1EYvEY,1957
spacy/tests/lang/cs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/cs/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/cs/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/cs/test_text.py,sha256=4yupiA1KXaRR-3cNxq-3TlxPuxpWDHOxB-TzO5loFXw,533
spacy/tests/lang/da/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/da/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/da/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/da/test_exceptions.py,sha256=Q00wmSCcZPU1_obLsayuKp6L4JnvmF3kXxfcan1EgLQ,1883
spacy/tests/lang/da/test_noun_chunks.py,sha256=rgRHkKmM8BeSI4HkgaZhwfTgI0dKYB7feybWG-C3n4s,2135
spacy/tests/lang/da/test_prefix_suffix_infix.py,sha256=B14Zwf8SuhntKmngQ22zLccJbvZxxc7ELUZ-nlM6OPw,5591
spacy/tests/lang/da/test_text.py,sha256=3P5LhMgnrlbFut1QUlDwqZR6OxpHre4dfGPIrdtlLdY,1261
spacy/tests/lang/de/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/de/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_parser.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/de/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/de/test_exceptions.py,sha256=vqKBIOfx70X8HMc04h7qxZh98oU4bVfwRqM0FuROtQU,618
spacy/tests/lang/de/test_noun_chunks.py,sha256=r90RAxjpHxdofxTElS9-Byg_xaZTP4A5CcY2aU2U9pM,274
spacy/tests/lang/de/test_parser.py,sha256=RRA-tpkZCo9KIehVJkc5b8KzyGy-DVt886S2Yg73UOI,1216
spacy/tests/lang/de/test_prefix_suffix_infix.py,sha256=RH7-f79GFUniRs10a3KYT_CBdirkSaZmI4WgsPcbS5E,3503
spacy/tests/lang/de/test_text.py,sha256=SYBCcSMS0RnMCXLXbVn2OZqP7Ir6CJC9cen_Zrb0LvU,1550
spacy/tests/lang/dsb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/dsb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/dsb/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/dsb/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/dsb/test_text.py,sha256=k-BaAZyAdbKZf_nz22m2xo2FgijTTLC5fu1zCIFJWTs,585
spacy/tests/lang/dsb/test_tokenizer.py,sha256=yU9DVyRl4XR4vcmYwVkM3QBOgPdjwZVZH3MdUTV4knM,778
spacy/tests/lang/el/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/el/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/el/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/el/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/el/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/el/test_exception.py,sha256=P8b5DEK8Nengb7EYOgnq1KOSCxf2IThOzbidrfzOyHY,528
spacy/tests/lang/el/test_noun_chunks.py,sha256=r1RQf-woEbKUlYmtzYYrXz8vwzl9PaAgCVpThtcYi44,314
spacy/tests/lang/el/test_text.py,sha256=MQU2nWLXwzSgn1D2Bij6Nlmq46XTaWWyiM6ZjNJfTdI,1799
spacy/tests/lang/en/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/en/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_customized_tokenizer.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_indices.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_parser.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_punct.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_sbd.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/en/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/en/test_customized_tokenizer.py,sha256=6Zt4wd9cueM5Yd2iZpwM6vlyaY-aNj3IENfWW4uDvgM,3932
spacy/tests/lang/en/test_exceptions.py,sha256=utGMVXoxZRdaCHgUHg6E9BIK8oW8X_D8-t8K0Nzjdv8,4285
spacy/tests/lang/en/test_indices.py,sha256=hG93NO7QCJT4jjq0i8FNWTXD545CafHWptlrvanxEhg,748
spacy/tests/lang/en/test_noun_chunks.py,sha256=655Bk4LIF6LolemPPi0UFkimtnnY7BAxN5QpPvkp0qE,1594
spacy/tests/lang/en/test_parser.py,sha256=6gSLK3O9Xm7nEEnArkh5KoIcyh6lj3r_F13DzvYxoMg,3020
spacy/tests/lang/en/test_prefix_suffix_infix.py,sha256=n7PEePPBt7sPP0EF-TkZ9PRDE21zGtO5BwGrwDCTBl0,4391
spacy/tests/lang/en/test_punct.py,sha256=qrJL3CbW4z2MhoKaarvs-cAeXa-MlVARwJgW43AorQA,4549
spacy/tests/lang/en/test_sbd.py,sha256=g8GTHe5dtppt9FVQ_cVmAixy7CCZUgxvcY-fhnHfF-U,1754
spacy/tests/lang/en/test_text.py,sha256=-H-PXA45mCh0f__VyuNR8-fTxc16mEbMw2YpglyBQok,2033
spacy/tests/lang/en/test_tokenizer.py,sha256=p1YI09mmwTkTQh7CpXM0lwlWzO_15uA6S6rxb_EpxEs,6116
spacy/tests/lang/es/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/es/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/es/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/es/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/es/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/es/test_exception.py,sha256=zW0QYlCHcYqxfSn-JZV6KphkXWvJm2CS_hZSpTqDAS8,571
spacy/tests/lang/es/test_noun_chunks.py,sha256=0aBOulvG9P9lAls8JHWFNxScGH2n_YENA38Pt7Zp-qo,10126
spacy/tests/lang/es/test_text.py,sha256=jVX1NoMmU2Is6LMoghSPaIxni4_pfs6WDfVVHYW2sJA,2100
spacy/tests/lang/et/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/et/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/et/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/et/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/et/test_text.py,sha256=CGlPU82c-ks2PvE6uBsx5bcFrN9C-I1HhyRQTyxXUtc,959
spacy/tests/lang/et/test_tokenizer.py,sha256=aSKDcg4Qta1y7eTLwyCRB-KxEFQ2tfp2ovl9JWOP5ME,766
spacy/tests/lang/eu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/eu/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/eu/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/eu/test_text.py,sha256=DAAJjFeYxB4YYH6UvDWwjZ6CODc_7KFy5MM1caWvbDI,507
spacy/tests/lang/fa/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fa/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fa/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/fa/test_noun_chunks.py,sha256=zRmA8IcpUnHMRp-PwPKWwgoD00WpLGf1PxjjX0nZu80,305
spacy/tests/lang/fi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fi/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fi/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/fi/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/fi/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/fi/test_noun_chunks.py,sha256=Zm36QAEARQtcfB-0zkiEMCMrQCWc8P_TgmIyp5IQPIc,7447
spacy/tests/lang/fi/test_text.py,sha256=J0M5uI5eTkvbEiMW5CxTjyMkusg5q5pBbNVfD8wO0rc,566
spacy/tests/lang/fi/test_tokenizer.py,sha256=2yy2UtLW6ZY6RFMIY8Ocwx-5kjVon5Qo_Y1xZTlCIyY,2983
spacy/tests/lang/fo/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fo/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fo/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/fo/test_tokenizer.py,sha256=jcJTLFirkAZFjjKeF1_PXoZNznt0mpBB1erp_kl_EEQ,1541
spacy/tests/lang/fr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/fr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/fr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/fr/test_exceptions.py,sha256=h49lZwR0jmcSlHgn8l7JvaBbpOQl17KAQ33IJprCO6U,2301
spacy/tests/lang/fr/test_noun_chunks.py,sha256=dl15F-l2ZjNt-psfHhm9p3rfJ4D2OtgC2mR_v_RSGX8,8585
spacy/tests/lang/fr/test_prefix_suffix_infix.py,sha256=VlGWSWdOL0G59Bg7k8inCjJ6nAPQeIyUAfBxWlJf_bY,796
spacy/tests/lang/fr/test_text.py,sha256=hvKHbGJbDdKrPrAgDM5oQuYFupFmSwsLt3zbq97yv5Y,1035
spacy/tests/lang/ga/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ga/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ga/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ga/test_tokenizer.py,sha256=qv3OJfxSNQC6ZpiC8AVWfsyUdvZYFJ93zlLYDbQDnlw,699
spacy/tests/lang/grc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/grc/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/grc/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/grc/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/grc/test_text.py,sha256=yIvgaa4CeGuK7dgD5b6nTBmK5wKblmX4YYh-wl47jNM,566
spacy/tests/lang/grc/test_tokenizer.py,sha256=ZJp2nFCIcIpQ3S21FiFuOAlT-gluN8nSBzCW4yssWiM,1343
spacy/tests/lang/gu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/gu/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/gu/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/gu/test_text.py,sha256=GLtbOyuRLKwhPeNJCi6L-P8ampU0akZx18CWT_mK5xo,701
spacy/tests/lang/he/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/he/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/he/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/he/test_tokenizer.py,sha256=2hchbiV1Q3i-unY1vmRvnpba9sHSMB41Rmmb0XmRoMU,2326
spacy/tests/lang/hi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hi/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hi/__pycache__/test_lex_attrs.cpython-312.pyc,,
spacy/tests/lang/hi/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hi/test_lex_attrs.py,sha256=c9a-ELXQ-S5GnHMJZ1V3QqxZ8C9_igJ4tx7jsEozdPA,1886
spacy/tests/lang/hi/test_text.py,sha256=U0B_QxW_HM1eBwH3yDyswdhJnLjuxqtCHFdUn9iBjKE,413
spacy/tests/lang/hr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hr/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hr/test_text.py,sha256=fpr_xDtu2MUoHf0TWmvZIaIoKNo4ElrkqVd5Y7WQ0lg,980
spacy/tests/lang/hr/test_tokenizer.py,sha256=W_I5gOtbKrAQ9nTlb9zq29nEVPqk9DvjJzGCohBKADs,832
spacy/tests/lang/hsb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hsb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hsb/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hsb/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hsb/test_text.py,sha256=ardRbipilRC6GOwR-Wp1dqrtWp4ZuNp020wHTXWeO1U,591
spacy/tests/lang/hsb/test_tokenizer.py,sha256=GVCgP5hw4A3W5y5OcFYMLRUiyqTHgh_R7jHnZYGkMpE,898
spacy/tests/lang/hu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hu/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hu/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hu/test_tokenizer.py,sha256=yEkP9APRViKo6Kx-BfDPCsIsbXwAeDgLg8ar9AseCDY,14810
spacy/tests/lang/hy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/hy/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/hy/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/hy/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/hy/test_text.py,sha256=niI-qWftaBdeZhcEna6f0QqaLzx5G0DIoNwwNCzJDYU,220
spacy/tests/lang/hy/test_tokenizer.py,sha256=tYfinvy71CWbgnwPFK8oh1oJ7OQ-Kdz3lrSFZ7KBzbc,1394
spacy/tests/lang/id/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/id/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/id/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/id/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/id/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/id/test_noun_chunks.py,sha256=ULACuOkyjRfARyui3trrac6BURIhp64a3Ab1rknvfH4,264
spacy/tests/lang/id/test_prefix_suffix_infix.py,sha256=ICH4G1DhZJ-XqzpqeYF8pQxYVjpwZmFU_kQXk8WT4bw,3603
spacy/tests/lang/id/test_text.py,sha256=Ps_Q4nWodE-Z3sEMgJyk-MqE-dNBW3M0vKw7PgWfMdE,215
spacy/tests/lang/is/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/is/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/is/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/is/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/is/test_text.py,sha256=9u0bEr9n_DYB5YcuMhL2qkcPcJnOQZG1pA5J70wp5pA,1006
spacy/tests/lang/is/test_tokenizer.py,sha256=CbAJo1uWexq0dRC7DjoXfUjyp5ADSdVGUeF24DLYgdM,813
spacy/tests/lang/it/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/it/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_stopwords.cpython-312.pyc,,
spacy/tests/lang/it/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/it/test_noun_chunks.py,sha256=cTfp7fQxKyt2vITWshxaWGlgLWXl_gqauYokF5JcUAc,8862
spacy/tests/lang/it/test_prefix_suffix_infix.py,sha256=tcjrTqy6cF_G3V_0Qx6iD0Bb_L0Hck3kRtu5nMJ36rA,370
spacy/tests/lang/it/test_stopwords.py,sha256=CtIWKyApYsBQ4MQcQKrNzNqpij2XRfI4yAzIn484jIo,466
spacy/tests/lang/it/test_text.py,sha256=FDHmZjI9RF03-G5lEQU-7P5CJZ56rbTv7iXdYYzmqKE,425
spacy/tests/lang/ja/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ja/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_lemmatization.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_morphologizer_factory.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/ja/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ja/test_lemmatization.py,sha256=Vioqvy8QkMxDzEq44-30r_iJaDOAFWqdp3Hq3fOJKVY,771
spacy/tests/lang/ja/test_morphologizer_factory.py,sha256=pgoWPOy0bW7sWbtMBbHlLMlCoXmgMjrC_xn6sTDpVP0,254
spacy/tests/lang/ja/test_serialize.py,sha256=TNlZPBRxyVC1LY9ocVWC3PO_1Ml3UH4ubhUtzGSMIjw,1349
spacy/tests/lang/ja/test_tokenizer.py,sha256=kwXVsx0w0pQrcHrZ5SB-WjtImxXh8di7eqTS9Z0yNdA,8153
spacy/tests/lang/kmr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/kmr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/kmr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/kmr/test_text.py,sha256=DFZAp-_J84vg2Si6eTWNJeZkvONJJD-X2VvZq2jtw28,529
spacy/tests/lang/ko/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ko/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ko/__pycache__/test_lemmatization.cpython-312.pyc,,
spacy/tests/lang/ko/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/ko/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ko/test_lemmatization.py,sha256=JvB3VCq9QAI7OPOjrASr6K1CKTq8iiobGjxUD2ICC6g,377
spacy/tests/lang/ko/test_serialize.py,sha256=bY3rB9__Ekgwbnu-Dx_HHoleFoeIbXWb5iYgHHYoMrk,738
spacy/tests/lang/ko/test_tokenizer.py,sha256=i57i_tuf1Vr-y6sitMUtP4lDq6gC2atezLG-iXqzGHQ,2968
spacy/tests/lang/ky/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ky/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ky/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ky/test_tokenizer.py,sha256=3ocKMCeL7XpGCxskRMOOX8MRI0dNZFGtstjF_PkDrjg,4098
spacy/tests/lang/la/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/la/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/la/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/la/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/la/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/la/test_exception.py,sha256=pnlUIANbRNLTIyx31qdzqHCz-WEqPTsJqPeX7AHXNXk,244
spacy/tests/lang/la/test_noun_chunks.py,sha256=xk0Bw94KJkGVMh3g0R6BXasWLE2h8LH14-59vDiwJtY,1681
spacy/tests/lang/la/test_text.py,sha256=LuyDKdXn4emum6ow3kx_Ot-pzXwuJLiuIVND4TBvTDY,840
spacy/tests/lang/lb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lb/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/lb/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/lb/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/lb/test_exceptions.py,sha256=Uneh-YjxNseMsCTv2lb-PQAhe-HJ0XwJFItmPcsAZRg,610
spacy/tests/lang/lb/test_prefix_suffix_infix.py,sha256=yL2VY1iUNTdIaq1qFQlREhPxm39Gi_Eyt2ITd40K26M,603
spacy/tests/lang/lb/test_text.py,sha256=2gMZkdSN8pwCdALSbAzGiAni7Wz5WPCYYF8an_Swujk,1323
spacy/tests/lang/lg/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lg/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lg/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/lg/test_tokenizer.py,sha256=6kZvUhCpOrQ2fdn8sQ_cFDP02wT4FxJwUjvCeWuPP_Y,464
spacy/tests/lang/lt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lt/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lt/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/lt/test_text.py,sha256=ufLYjnQSHZ_5DYlYo8NX0O6H-c_FORSCA2hI-7u9gj4,1697
spacy/tests/lang/lv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/lv/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/lv/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/lv/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/lv/test_text.py,sha256=dZZCSKffPxNlVFC6ByCfKUv7AHwl1_Osys0PwWzySwE,1045
spacy/tests/lang/lv/test_tokenizer.py,sha256=Y4g_uaHPtgy4IwY2b4yW9o0UOybW83-jX7woTi27pUQ,808
spacy/tests/lang/mk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/mk/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/mk/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/mk/test_text.py,sha256=7TzwJgyjy1E57QUfMeDwOxxGuti6PizKr6t9VQxtqKQ,4431
spacy/tests/lang/ml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ml/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ml/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ml/test_text.py,sha256=qQGlrSmaubLNc-9ARiQSYzktEKU_lPXiXzFUY3zUW1M,1097
spacy/tests/lang/ms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ms/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ms/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/ms/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/ms/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ms/test_noun_chunks.py,sha256=rObkOYo2e2EjmFlujXTfsQ22fup3vtuAxsNBLKoHD2o,264
spacy/tests/lang/ms/test_prefix_suffix_infix.py,sha256=8LeZo8Gjw9KbeEnFLfLZVi8mDiD_056IAU61DwZFvKQ,3624
spacy/tests/lang/ms/test_text.py,sha256=JHcNswD0Qy6AaiUgP8WhbVEWu4yHKqwOmVnIoorZuys,215
spacy/tests/lang/nb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nb/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/nb/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/nb/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/nb/test_noun_chunks.py,sha256=B-NlCy_FMhkUmt8DtpQ9b0y3-rxoyxnV2mnEuSCpsPw,285
spacy/tests/lang/nb/test_tokenizer.py,sha256=9pXx6Xc6FjSgmdlbUWL6x6mC93zxSDjw7ZatjWWOvvg,648
spacy/tests/lang/ne/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ne/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ne/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ne/test_text.py,sha256=No3TyHdAS5Ybe0rV7OOHjqOwfRatygU10ktnXZsZGxs,783
spacy/tests/lang/nl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/nl/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/nl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/nl/test_noun_chunks.py,sha256=4HmmFzNVtKFP01PoJKIKKzsJ65wNMW9eSuW4PukX9jk,4530
spacy/tests/lang/nl/test_text.py,sha256=7P8lr420qwpscsXF-r9IwV5du23BS0sIdJphitdJUdA,719
spacy/tests/lang/nn/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/nn/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/nn/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/nn/test_tokenizer.py,sha256=RspoAFvb6JYTk235AjwsUdqhm3DFEFtBcfCvnWYduAs,1834
spacy/tests/lang/pl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/pl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/pl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/pl/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/pl/test_text.py,sha256=hHIb-ModI2nQyhzJ8Zn5HxkkQ-MtmOyrz8HSg7jJH2U,547
spacy/tests/lang/pl/test_tokenizer.py,sha256=l0nvFUb0ADooJ77Y-ZbS2P81mWsQJsr5qVWoAwhYCIM,578
spacy/tests/lang/pt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/pt/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/pt/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/pt/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/pt/test_noun_chunks.py,sha256=xP_Rew1pRcHNlXBhWRHuxD5vpjQj1cQkCxuvHoPY9PI,7971
spacy/tests/lang/pt/test_text.py,sha256=3dai_Q1fKSmAxYj2RIqqVlGaDflKcNvS5mR6Obo0dfo,229
spacy/tests/lang/ro/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ro/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ro/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ro/test_tokenizer.py,sha256=pKMXrjzp7p7ouSYpi6vUgcshte7lD0AryPXtQDfoOao,754
spacy/tests/lang/ru/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ru/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_lemmatizer.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ru/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ru/test_exceptions.py,sha256=DL1fT5b9asOsVn4yF-AKINWFHExPUVaI4rA5QCRLYSE,364
spacy/tests/lang/ru/test_lemmatizer.py,sha256=di8v2-e1_gEJoUNjdfSmgikdp8r6ZG1VbdIctg5JZNU,4067
spacy/tests/lang/ru/test_text.py,sha256=mz6siyEcLCuU55Dtnpdx6-TYs0ym78idJcc9Zaz7uWA,230
spacy/tests/lang/ru/test_tokenizer.py,sha256=aKWxwCducV-nYVvb7foQlnClSYxelN4JGcR4PDeyv1c,6141
spacy/tests/lang/sa/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sa/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sa/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sa/test_text.py,sha256=zrIvezsBb5twTG3R-4FMP5dmdiSikdS158m_ymyA_LM,1339
spacy/tests/lang/sk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sk/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sk/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sk/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sk/test_text.py,sha256=NwbRs3s4Ct237UKc-l5BWGKrnppOj5cs8tA2LCFEuGo,1533
spacy/tests/lang/sk/test_tokenizer.py,sha256=mG4Kq2xP0tiIE5oy5jRv4WA4yJD-nfs-l6O9l9XYs64,468
spacy/tests/lang/sl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sl/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sl/test_text.py,sha256=J9_mWfor_w8KcIMPtk3EnBJjMzyNU3TyqFEdvHK72xs,1019
spacy/tests/lang/sl/test_tokenizer.py,sha256=zZrM7PMHdBqBBB4BCr-YoIAlXOkczdjbeH5FseMSFlw,863
spacy/tests/lang/sq/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sq/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sq/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sq/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sq/test_text.py,sha256=dXj7Cg1Ea7iVg19saOSxDsIdnJFxCCHGzmb64qU9MmI,1203
spacy/tests/lang/sq/test_tokenizer.py,sha256=Og8WFtuSG9ILc4H7lLdOYMQCpkS-R8W9Urlq7xpvJMI,848
spacy/tests/lang/sr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sr/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/sr/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sr/test_exceptions.py,sha256=KqnQeBQAH0fbrIZ27V8lkW1TwJTsKDKpH3gDBl3njiQ,526
spacy/tests/lang/sr/test_tokenizer.py,sha256=0kmMJwhAQs4pNDyClHmLiZHVyuMIWsU_nx7ikGwsIeY,4424
spacy/tests/lang/sv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/sv/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_lex_attrs.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/sv/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/sv/test_exceptions.py,sha256=1v15qkJKJgFjHoupaI8in9BwfF4YFGuXljKU6gPlavM,2530
spacy/tests/lang/sv/test_lex_attrs.py,sha256=T4Hkq26ou3L0dTPDM7GV7OUN6MSvfi1MdT9QkQJf67U,715
spacy/tests/lang/sv/test_noun_chunks.py,sha256=-0D7JMUXm54c7w_X35hJtF-LRnirleIaltg790iF1rg,1907
spacy/tests/lang/sv/test_prefix_suffix_infix.py,sha256=_3lbAyO8j1zRb47vlmEqs-dYhB2QrfWadCVFali4Waw,1306
spacy/tests/lang/sv/test_text.py,sha256=u-SjK4xInt_BUnvuUhjabYxbb2JqnqpLvz7voTivIg8,737
spacy/tests/lang/sv/test_tokenizer.py,sha256=aAQFC4msaTx-u5d2cpmdIXHRrQDPeRmbB-bxwK3YReM,1034
spacy/tests/lang/ta/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ta/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ta/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ta/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/ta/test_text.py,sha256=qxd6Bx6lighmUCUuV-95Q_b33oBqJA8C1tATslHPqBw,2764
spacy/tests/lang/ta/test_tokenizer.py,sha256=ivyNLSWMgM2nK0WkhEwM0TUtGLC5N-xw5xEosXxq3Qo,8019
spacy/tests/lang/test_attrs.py,sha256=Fikb2CKAUcuRtQVYkCoOREgJHmO1VUwHSV09WV3wUgU,3667
spacy/tests/lang/test_initialize.py,sha256=WKRSkPYvktkYhYQ-uLw7n3njEZ_OcfdIhxZ5m0YKZ4w,953
spacy/tests/lang/test_lemmatizers.py,sha256=ODCk5ErOmWu18Dv6GjGXgQfbvI6n6wLwmKLkro279K8,1964
spacy/tests/lang/th/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/th/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/th/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/th/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/th/test_serialize.py,sha256=eXdWfFmjCSr_3FM5CTWKCyEXAdSa7YGXo6-8w0yd3Q4,732
spacy/tests/lang/th/test_tokenizer.py,sha256=V0ctcHhaepZdZ8U2jjDf0WpiehJbqi_6pvnBC13pE-w,327
spacy/tests/lang/ti/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ti/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ti/__pycache__/test_exception.cpython-312.pyc,,
spacy/tests/lang/ti/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ti/test_exception.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ti/test_text.py,sha256=_ljfsUdaLB0jC3Kvc0PVVbsce56ywqKCehce2sj8Mak,2171
spacy/tests/lang/tl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tl/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/tl/__pycache__/test_indices.cpython-312.pyc,,
spacy/tests/lang/tl/__pycache__/test_punct.cpython-312.pyc,,
spacy/tests/lang/tl/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/tl/test_indices.py,sha256=wO9XybcK95I4Jma0uHV271x5UHdG5rBN26Q7GEKgLcc,265
spacy/tests/lang/tl/test_punct.py,sha256=LSQrXjzq_xIG84XKsqU148jN1j0fWNaMVyPNMBDiqgk,4547
spacy/tests/lang/tl/test_text.py,sha256=84-CZ-dJCbWY6ZCtdcwKEbpnNRSF3eNeGf_Ob3wwFVE,2554
spacy/tests/lang/tr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tr/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_noun_chunks.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_parser.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/tr/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/tr/test_noun_chunks.py,sha256=S2VnDNh-psiE3idxxtvZSV0HgnCVY1n_N6Jr7JYyLgI,434
spacy/tests/lang/tr/test_parser.py,sha256=UdFSv1O2fyYQWxm--VJPOVrCcdE2SdrYtx0C8yi3OkU,20359
spacy/tests/lang/tr/test_text.py,sha256=PTpNTPDB3AuMw7p1MY0SPN7_WRGnlD8BkqPJguEqRVc,1845
spacy/tests/lang/tr/test_tokenizer.py,sha256=ZzU1u3OOWrGNjFVvBfOedh2lFDhHjV0L_c0YQbQzkp8,20284
spacy/tests/lang/tt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/tt/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/tt/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/tt/test_tokenizer.py,sha256=XWoQcJGs-j8So7c31NvCd569oweDLEyAvN_YMuv_MFg,3803
spacy/tests/lang/uk/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/uk/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/uk/__pycache__/test_lemmatizer.cpython-312.pyc,,
spacy/tests/lang/uk/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/uk/__pycache__/test_tokenizer_exc.cpython-312.pyc,,
spacy/tests/lang/uk/test_lemmatizer.py,sha256=9EURieXon7v_NLeLAPkMy8bRQpW67C-OvV7uWUr6R9A,864
spacy/tests/lang/uk/test_tokenizer.py,sha256=7m0eRq1xgIX6suymx__fKMp7CHmr45WRqiwd151dFH4,5563
spacy/tests/lang/uk/test_tokenizer_exc.py,sha256=AjcwFraAyqJbbTjVUCktYuQmO1H8H0Of-WdAjQMsn3I,375
spacy/tests/lang/ur/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/ur/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/ur/__pycache__/test_prefix_suffix_infix.cpython-312.pyc,,
spacy/tests/lang/ur/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/ur/test_prefix_suffix_infix.py,sha256=zxcOQGPBjV_tKrMteLQxVU52Sd_NkYLd5gV0edKiGfk,237
spacy/tests/lang/ur/test_text.py,sha256=ycw5veAPOiifwJ0zbOMH8mCEpEZZNpCzM5T74Zokzx0,492
spacy/tests/lang/vi/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/vi/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/vi/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/vi/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/vi/test_serialize.py,sha256=v1Nz_OBptO58fw7-Gxp4Qt5vkBdC-zJA-9Hm-kG1_8E,1351
spacy/tests/lang/vi/test_tokenizer.py,sha256=1TUBAdJGeo8ZyNEPlylTwjcSfM-CM5qjfgx6uFyYBuE,1724
spacy/tests/lang/xx/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/xx/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/xx/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/xx/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/xx/test_text.py,sha256=w0yzR8-rXBEkyS7eS99MwWZk3P6sD7HDFrD5fcAk8Y0,1744
spacy/tests/lang/xx/test_tokenizer.py,sha256=GvABfRk9yWz4mRk1PHOXTAzSADx7suOgR8Vr45-YiF0,694
spacy/tests/lang/yo/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/yo/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/yo/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/yo/test_text.py,sha256=wfaaU17kQV1BFwVw32b-4yudl-ulQY9P8_RpE_bicv4,1521
spacy/tests/lang/zh/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/lang/zh/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/lang/zh/__pycache__/test_serialize.cpython-312.pyc,,
spacy/tests/lang/zh/__pycache__/test_text.cpython-312.pyc,,
spacy/tests/lang/zh/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/lang/zh/test_serialize.py,sha256=ZyVGZ_kGR1JN_ATYwt3brrB7rzwg1ngbNSGI9Pec6tc,1294
spacy/tests/lang/zh/test_text.py,sha256=Kn58rL0dPZF2DIt0yVvfOBEJanXjAXv8lmr5XXrTXYw,475
spacy/tests/lang/zh/test_tokenizer.py,sha256=xxL0O_Jnw8MvdL0TKAySAt9pBTeNwCRrgxR3ZIEMRos,2897
spacy/tests/matcher/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/matcher/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_dependency_matcher.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_levenshtein.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_matcher_api.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_matcher_logic.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_pattern_validation.cpython-312.pyc,,
spacy/tests/matcher/__pycache__/test_phrase_matcher.cpython-312.pyc,,
spacy/tests/matcher/test_dependency_matcher.py,sha256=9fUKhR4bC_4AWDlxJFBpQN8KrzZNoeA33AcP35oSN2s,15539
spacy/tests/matcher/test_levenshtein.py,sha256=uafUAY_WQRF2kVKpQoOerzcU_yDWpAzaR3TIG7EqT8U,2872
spacy/tests/matcher/test_matcher_api.py,sha256=X9nhtqubYn-wrBEtxrUI88qfcfxmTIB1mHIkloehoGY,30826
spacy/tests/matcher/test_matcher_logic.py,sha256=b-SMBE8pJbUtUki_KywzRl7Kfbcj2c0LTLcAAzykeKk,27971
spacy/tests/matcher/test_pattern_validation.py,sha256=EDN5t0Wh_R2JzzzLsNrTfbvTZBFe1BAttza8P7Pkxl4,3453
spacy/tests/matcher/test_phrase_matcher.py,sha256=GE5-IWUxlFXvxjZYjA4v42rolJLVfdpp2LkQnub8By8,18476
spacy/tests/morphology/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/morphology/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/morphology/__pycache__/test_morph_converters.cpython-312.pyc,,
spacy/tests/morphology/__pycache__/test_morph_features.cpython-312.pyc,,
spacy/tests/morphology/__pycache__/test_morph_pickle.cpython-312.pyc,,
spacy/tests/morphology/test_morph_converters.py,sha256=Ds6BVEguOfp-D3MlxlV87QNK7nBlrFcGrIE4b8TVJ3I,877
spacy/tests/morphology/test_morph_features.py,sha256=HACDAVIuSzoLtIb3kLTw4YwczO5wvbfhFMliAx-4csg,1399
spacy/tests/morphology/test_morph_pickle.py,sha256=jNm72vTmXCxU_1wv_ZhsyCggST6JvXFPBiFgDvEIyuQ,693
spacy/tests/package/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/package/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/package/__pycache__/test_requirements.cpython-312.pyc,,
spacy/tests/package/pyproject.toml,sha256=0e6GcfCmGGHC2Mql1bXi9Qp7L0pK44rN8R2Uwo6LDbg,1809
spacy/tests/package/requirements.txt,sha256=3iB1fI4PCsBk8BURtStdwdIz_Kf7J9zb0MKDp2sPneQ,936
spacy/tests/package/setup.cfg,sha256=T-Ls8RQSC0RphdQi-tvbac5RKLnNoc0QTyMTGGg4mUE,4014
spacy/tests/package/test_requirements.py,sha256=cy__e0Be1KlrmAtYaQhhvxXtEAM852j02jfOR34iglg,3303
spacy/tests/parser/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/parser/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_add_label.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_arc_eager_oracle.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_ner.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_neural_parser.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_nn_beam.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_nonproj.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_parse.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_parse_navigate.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_preset_sbd.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_space_attachment.cpython-312.pyc,,
spacy/tests/parser/__pycache__/test_state.cpython-312.pyc,,
spacy/tests/parser/test_add_label.py,sha256=ke2MGr7zeXbbj_OtGPNahNsKN7B0cKXDqTp60nGpc5Y,5079
spacy/tests/parser/test_arc_eager_oracle.py,sha256=orrbi21eBBR5FDDZ9AOBMM-9aOEBVTDmtIbggAdDkJI,10380
spacy/tests/parser/test_ner.py,sha256=0NUfZwTTZHxQ5wU29X9yGPuVk69eeOideRfB2U2AXDE,30004
spacy/tests/parser/test_neural_parser.py,sha256=b4L8BuS7hSHtEnHMa5n4PyNxqx38S8VENxISmt5jDys,2969
spacy/tests/parser/test_nn_beam.py,sha256=z3RxA6G7XQm5JnpNujSTmbcTnA-rHHoMv9-AVbA6wcM,3744
spacy/tests/parser/test_nonproj.py,sha256=52vV9Qq7n6-aAYKPvL2o4T6YN3NpXfy9-3ciUz0jL5c,6413
spacy/tests/parser/test_parse.py,sha256=ZbSWfOqPLH-9_0eC_2GsScdszuqx7SLnY0wlZdnX_jA,20702
spacy/tests/parser/test_parse_navigate.py,sha256=dUBud3QzsCAbUFzPy-f-w3ic6aQc2kQGdyCorrozJ7c,6342
spacy/tests/parser/test_preset_sbd.py,sha256=obn0zZoWhwPIr7xrHcK8m-T16gK7TKApz4UmD1kAtyM,2649
spacy/tests/parser/test_space_attachment.py,sha256=KlHoT7FSpwYgNzssOzQUVmsYRlbY1Q6QiT6kxgwOKMk,3007
spacy/tests/parser/test_state.py,sha256=thpb-vcd4_39mijtqxU6HCUv8zfRiazlFtAUh32DYiU,1973
spacy/tests/pipeline/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/pipeline/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_analysis.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_annotates_on_update.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_attributeruler.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_edit_tree_lemmatizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_entity_linker.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_entity_ruler.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_functions.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_initialize.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_lemmatizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_models.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_morphologizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_pipe_factories.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_pipe_methods.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_sentencizer.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_senter.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_span_finder.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_span_ruler.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_spancat.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_tagger.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_textcat.cpython-312.pyc,,
spacy/tests/pipeline/__pycache__/test_tok2vec.cpython-312.pyc,,
spacy/tests/pipeline/test_analysis.py,sha256=B6hAwi9iHSmtcD4UsfPW7m3lfVQoU2zfGa-ENiDQ8bw,3572
spacy/tests/pipeline/test_annotates_on_update.py,sha256=XGbRlpGERdD2346hpoWreR2YZPXkBc04aE-BrIr8Kk4,3306
spacy/tests/pipeline/test_attributeruler.py,sha256=0cziPPwQMJcKLuypbhOKVq-Hkd-qFSXjdMV8PjZ3o58,9830
spacy/tests/pipeline/test_edit_tree_lemmatizer.py,sha256=lmbYkApeKjtkj1BF_0S4QooN-L2Eil_X359qcCo0Iw8,10782
spacy/tests/pipeline/test_entity_linker.py,sha256=yT42wpFNDZ-tRHz8jCj6uhMaw5tznoJ50XrZpGFEntQ,50196
spacy/tests/pipeline/test_entity_ruler.py,sha256=9T6BxoWd2Kuoh6YK8_jOGph1jQFF5BMQPDlmhnyW19M,26578
spacy/tests/pipeline/test_functions.py,sha256=eVwzA0XHt_hgxJOvCiGtZ0BGW0JWyObL7NglWk_DeXI,3291
spacy/tests/pipeline/test_initialize.py,sha256=XFsFE7SW44x_suRXAM2WflEw_LUtyt1kTLFiomXB0RA,2512
spacy/tests/pipeline/test_lemmatizer.py,sha256=lddcoLVtBNkUURZrf2AY0zTOXazCE0bXEx97jS8lwRg,3842
spacy/tests/pipeline/test_models.py,sha256=DQhBWe6WFeai0NMYN-6Emk5yI9Ll5pVHfnXcniH0OJU,3950
spacy/tests/pipeline/test_morphologizer.py,sha256=4-SFw43dowwVwYx8-rSqx5VBaqu8xL2UO9bOKfi52dI,8320
spacy/tests/pipeline/test_pipe_factories.py,sha256=WrYW6TfzoXX_QNoA7ahEJXTTeoaG5XuLn1JotqA4Sd0,20593
spacy/tests/pipeline/test_pipe_methods.py,sha256=cq9vWrgtho9XSaUdvdx8akLgRYBjE4aQJXLdwRfV0I8,23718
spacy/tests/pipeline/test_sentencizer.py,sha256=rmkPs8t3mgWyJBJf6T2kCN7RaiS8yYmxR-VytU7WIrw,18427
spacy/tests/pipeline/test_senter.py,sha256=AvygHwhjBw5n0tZdrN1_Z6mOPCdpxYcBTfzUpL82HZg,3416
spacy/tests/pipeline/test_span_finder.py,sha256=HQfS3kau87rogmznkNvIoJwYHwMODRVjG6uSeKbvLHA,7669
spacy/tests/pipeline/test_span_ruler.py,sha256=qGqw4hsmnPYn2XdTK04ZcYA98zfRn5ixKaQsQ1jfXBI,16684
spacy/tests/pipeline/test_spancat.py,sha256=rusGPOfKDR8NI7EjzqZ2peTvS281NcXDRjrTRQBP3ME,22588
spacy/tests/pipeline/test_tagger.py,sha256=-6B06jtpbKX3FJC9d-IJ_UoeDvniburu2k-ozSWi1o4,7895
spacy/tests/pipeline/test_textcat.py,sha256=A6_ikk0Qm6cOlXJQRP45j9gBhvVVVLih3BS95rhqTa8,40702
spacy/tests/pipeline/test_tok2vec.py,sha256=8AkB5D23uLefLaz--Mu6LzPa7SO5Lnqn2wkI2kzucE4,22434
spacy/tests/registry_contents.json,sha256=x3s8fQ5xhr4MqgGuFIZTcD5ahDg5V4mMukHPYCCNkMM,7251
spacy/tests/serialize/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/serialize/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_resource_warning.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_config.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_doc.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_docbin.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_extension_attrs.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_kb.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_language.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_pipeline.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_span_groups.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_tokenizer.cpython-312.pyc,,
spacy/tests/serialize/__pycache__/test_serialize_vocab_strings.cpython-312.pyc,,
spacy/tests/serialize/test_resource_warning.py,sha256=r99gJxPRqwy3K5KUPatmXPrT0SCLqPfRd76aqKSK510,4434
spacy/tests/serialize/test_serialize_config.py,sha256=5QKjj3rHtp9Zzl_btr9G99FC4Bo4G5eJc8zuQseNxTA,18682
spacy/tests/serialize/test_serialize_doc.py,sha256=afu1kWRW5_LLQ0sxO2yeEs_vVW2E8tHLPNcvcqKY-L4,7595
spacy/tests/serialize/test_serialize_docbin.py,sha256=ZNMcwlDE8__hRWqNtPObe8ENujznsi1oT8VokD2rAxo,4141
spacy/tests/serialize/test_serialize_extension_attrs.py,sha256=LdvaNO-al2FfpBBHNgi33-6Q7E3Dw10gmkirDkuINas,1142
spacy/tests/serialize/test_serialize_kb.py,sha256=nbltJZqiVe-D9FQ06Cl9YuXkaBD6V2M8kmluETwA19E,7263
spacy/tests/serialize/test_serialize_language.py,sha256=nl3vjRt7qf0x02MwGuxEpoM1Q7cHQB4NYjtp_n3oZOo,3729
spacy/tests/serialize/test_serialize_pipeline.py,sha256=9COMLonx0SlWncobdoZnCQngQp0FaDUcgmQf-_nglvA,17317
spacy/tests/serialize/test_serialize_span_groups.py,sha256=y3DDgaQzY7hrn-GX1J1RNWJJjViQRDVGDHQkJHCrPFA,8929
spacy/tests/serialize/test_serialize_tokenizer.py,sha256=rPQIr-fn72sVNSUoxnxCDYmrT3dsKk-6wo2qKUb1sl0,5594
spacy/tests/serialize/test_serialize_vocab_strings.py,sha256=1M2AaQgAaDaGPSr1swKLo1QrpZIR_C11Jol_Us9ph24,7262
spacy/tests/test_architectures.py,sha256=wninXT5UFQQMF24XXyHqkdwLnVFU4DNWvTKnW5juCfk,464
spacy/tests/test_cli.py,sha256=RtB0molInDf0O9-avNgv8qxXvNmerJ8w3RRMo5ckqPA,41970
spacy/tests/test_cli_app.py,sha256=hbVWOKmUdRXJ26t76EMSWA0zKdrKH2T_zn1zHMWzOA4,14414
spacy/tests/test_displacy.py,sha256=1fmajhROp_r2I3vza1yNtmzshmmD3AcqBKNjFm5K4qQ,18264
spacy/tests/test_errors.py,sha256=awSfQ69K1XuL4X82-pfjRbJg4kMoeqnJvPTl1LoTkEI,349
spacy/tests/test_factory_imports.py,sha256=aOQREHrtQbMUzdHPMyZGLPt0gfyaBlrSwKVM9DpnXEk,3946
spacy/tests/test_factory_registrations.py,sha256=iqP8jPZajXdsQ4QBimnpBtOKGh-VPoPs1L-ZkkuNXpY,3105
spacy/tests/test_language.py,sha256=tj3UP_vmxlLc1Fk1lbHkR8hCxRNAv_cdkhiSdH8FdbQ,28001
spacy/tests/test_misc.py,sha256=6cmg0jlwjVk0eKsdcwZ8nT7cvl3ZUrR9RuEY2xgKek4,15994
spacy/tests/test_models.py,sha256=BCRBmDttIhWigTj6PPYYND3gcPcd_VFW7CamNzy5FOk,9892
spacy/tests/test_pickles.py,sha256=0IcG2ThqN_D6v6my6qw5OM-RCSFFEfIDT0PEb4ne73k,2085
spacy/tests/test_registry_population.py,sha256=LJZYILs1y-oVjQ-i4LK4_yQhIkOOfW_qfoicn5SNqKI,1965
spacy/tests/test_scorer.py,sha256=QJzu80S8Kg20wYPww-EmnSrb21_zS-IaUIzGOgDds78,18015
spacy/tests/test_ty.py,sha256=76nkWHSDlnw3hAuVr5GHOratmXxqf-N_lz4Ah5E9xOg,766
spacy/tests/tok2vec.py,sha256=oPgtxVA284eqbqCORCDP0k1u77v-fTnJ6YKI0mjiPDA,1037
spacy/tests/tokenizer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/tokenizer/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_exceptions.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_explain.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_naughty_strings.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_tokenizer.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_urls.cpython-312.pyc,,
spacy/tests/tokenizer/__pycache__/test_whitespace.cpython-312.pyc,,
spacy/tests/tokenizer/sun.txt,sha256=kP35baZeinL_9jG7WR06vfnjJtnGN4nDDkuWeJiiWJU,2595
spacy/tests/tokenizer/test_exceptions.py,sha256=VfDAgzhg8L3Uqv08uUhb8ev8FXLYUS9aSM-iLG50sNk,1811
spacy/tests/tokenizer/test_explain.py,sha256=qpQ7gqeGNFHtTCiQOLemGBklmAut_Og4dKAg-jNO7OU,5586
spacy/tests/tokenizer/test_naughty_strings.py,sha256=twv3xOvYq2ZKpy1BpcTso8aR4-zHWHfV5Q92qEKLfF4,7581
spacy/tests/tokenizer/test_tokenizer.py,sha256=b-_j_pdTQKNHHyqB7fDCnjThKq0h9aCbVbJCAeogv0w,19145
spacy/tests/tokenizer/test_urls.py,sha256=nv4KiRkSTWuvpUe_UX5IHjiFKPfhdA1KsGlkUZkzen0,6667
spacy/tests/tokenizer/test_whitespace.py,sha256=icALPI0Nq5vhcPXl2DS0xBU5KZ1nLm40Ngb8X86EU4A,1340
spacy/tests/training/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/training/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_augmenters.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_corpus.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_logger.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_new_example.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_pretraining.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_readers.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_rehearse.cpython-312.pyc,,
spacy/tests/training/__pycache__/test_training.cpython-312.pyc,,
spacy/tests/training/test_augmenters.py,sha256=WIHLelHutX9Yc6g4jWpV_naB89D9oNqvBIRjrv4Z9rA,10803
spacy/tests/training/test_corpus.py,sha256=VLEFfkaxXS6KdZ5GUf2C32ccC3K21g3RJXURtCpZ9Dc,2021
spacy/tests/training/test_logger.py,sha256=HQl8z647uCsShdsW556rYSCclQmArSNfy-YiC0z4Ez8,630
spacy/tests/training/test_new_example.py,sha256=6UU9HqrdVO9CMYjw5H4pL0wkVtWUSynGSRH1ihDll9k,16618
spacy/tests/training/test_pretraining.py,sha256=R6yysZd2hUJ1YlFcFBAjtvTbau0eeixfQNL46h58ZMM,12446
spacy/tests/training/test_readers.py,sha256=w5zJjQnBmGXasEB2eupoWW_BaE9JDFVFWIf6dFMeDOc,4071
spacy/tests/training/test_rehearse.py,sha256=ZMxptffVTHSYMgAiNhQYQ0GHv-R5xih6VT6NMelYXJw,6616
spacy/tests/training/test_training.py,sha256=rHt2orTH0EczxREdYWTlLfFfADj7GaIEoA1X5Wj3kzU,46713
spacy/tests/util.py,sha256=n-i7P8P29K_aWv1v1guAzLwGkliGbDyglej13MyYy8U,3385
spacy/tests/vocab_vectors/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tests/vocab_vectors/__pycache__/__init__.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_lexeme.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_lookups.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_memory_zone.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_similarity.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_stringstore.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_vectors.cpython-312.pyc,,
spacy/tests/vocab_vectors/__pycache__/test_vocab_api.cpython-312.pyc,,
spacy/tests/vocab_vectors/test_lexeme.py,sha256=Uqw3UCUkGoSxXyEpun8A3u2g_8qclAZI3dLWRpI0Jaw,2937
spacy/tests/vocab_vectors/test_lookups.py,sha256=UIOM2DK6e-Y0zRyQiYGNwLrJ13TGTqQTmWaedxkhFyw,4794
spacy/tests/vocab_vectors/test_memory_zone.py,sha256=JNr8NTr2JzZzDTOcb8648rZOZNAM5ZozaoP9Z22KUpk,941
spacy/tests/vocab_vectors/test_similarity.py,sha256=P0UmldkrNa3wgO5yozSYh0lGSmCQjbD38bl7UFIPif0,3946
spacy/tests/vocab_vectors/test_stringstore.py,sha256=KW4mBSkEeL_8Dj2eyEAV0qRGRSPLZNPh3SQnxn43DSU,3437
spacy/tests/vocab_vectors/test_vectors.py,sha256=iuWuOEClw1ASLfc6Kj5LZ0INMc-iftwhgKVj7DygEuc,24492
spacy/tests/vocab_vectors/test_vocab_api.py,sha256=QyP0270Qia-BigyPqr9YxVvXIXMH1om0xvy3hbZshT0,2231
spacy/tokenizer.cp312-win_amd64.pyd,sha256=jLi82KfljX181xrGMgNSuZwMmzPzwoyVEAalDprW_Vg,308224
spacy/tokenizer.cpp,sha256=d5aH8stdY2Qx5qjO_EYlVuhEioSfCProJVF84JWU11k,2289343
spacy/tokenizer.pxd,sha256=c44eqf-5D-mQMm7w6MOheCDWmbUKM3-LF54UF8jnCUY,2304
spacy/tokenizer.pyx,sha256=wxcGr2wM18S6nAbnHf1xxe4CA6KIA87OJ3jla2VYt8M,37652
spacy/tokens/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/tokens/__init__.py,sha256=EFMegQPp3oDrCMXLEnJ2-vqOhmSk-CeYe1J336dP1CU,259
spacy/tokens/__pycache__/__init__.cpython-312.pyc,,
spacy/tokens/__pycache__/_dict_proxies.cpython-312.pyc,,
spacy/tokens/__pycache__/_serialize.cpython-312.pyc,,
spacy/tokens/__pycache__/underscore.cpython-312.pyc,,
spacy/tokens/_dict_proxies.py,sha256=7dmU5yJSIOwKtnSXGG783UjAYTjcQjKu6NFnGwKzS90,4625
spacy/tokens/_retokenize.cp312-win_amd64.pyd,sha256=v0xZ6e-4-pEgJjjcELBZ11sBaa8r_cpUnCwOopW7LTk,224768
spacy/tokens/_retokenize.cpp,sha256=rHrF9eGRn_MDAS6h-Bh8bp-hAoqqDriU1vB-rPYKNOg,1898178
spacy/tokens/_retokenize.pyi,sha256=sXUAjADL1mgc9oepMebnQJkZUwI2yAPI9UtTt9nWWIc,725
spacy/tokens/_retokenize.pyx,sha256=bYMZfBM8YGxE4cycu3n_GZmU9sg5FW3LUTUREZDsbVY,20885
spacy/tokens/_serialize.py,sha256=bAOQv5sdN7_UWsrDL-jBRxTE5SlPgBuKb4YnXipvaKU,12298
spacy/tokens/doc.cp312-win_amd64.pyd,sha256=5IThm32E8X_7hCnEUiQWEKdbJEDfUk8zUyYxX_KmTfs,536576
spacy/tokens/doc.cpp,sha256=XTA-QCiXvNWlOpbkyjJ8yw1rLAvJX8buTm4V5St8Sqc,3750212
spacy/tokens/doc.pxd,sha256=A5gYPyhsLgRuJHJT--GZbcBXGhe4t30Ln-P4ZuQEtRc,1754
spacy/tokens/doc.pyi,sha256=txov5nETMpTJ7xTO0mM2du4DTk4gwwopkh-FgQcr_qA,6361
spacy/tokens/doc.pyx,sha256=GLTbqr05wZplS1QFOkdLJ0VEXxw8U9k2fcjaNzvtH_o,85347
spacy/tokens/graph.cp312-win_amd64.pyd,sha256=RceC_AwO8OOhUE4mXJMeyjWBGkCgUM5aWACXDnsHXdI,208896
spacy/tokens/graph.cpp,sha256=WxwYAyHL0oppqxAGBl_cMlAiBbluWhT4E2gB_auPqVc,1439049
spacy/tokens/graph.pxd,sha256=KcX4zjsN9AC57AsXr5PO1kIIT7ZJ2nNiWgdHT9cylG0,310
spacy/tokens/graph.pyx,sha256=SVH8J5vXdCJFXTCL3VfMUnxy3SSI7K2KkdhtmdDrAGA,23620
spacy/tokens/morphanalysis.cp312-win_amd64.pyd,sha256=nypZg8uOO7HvbY2MdtkL1dLpLuaTLxI3NXLjefYWVuM,78336
spacy/tokens/morphanalysis.cpp,sha256=aczqbXrpAS2-tk0COfAFLQxeFbrifsuJwCuaoTIm-m4,733692
spacy/tokens/morphanalysis.pxd,sha256=EPtB6tCP6ReQLSTIm8wCPqmlXfwM7gVy_VATr3VfmK4,218
spacy/tokens/morphanalysis.pyi,sha256=zMBBItLGUzYCvqd4edXj-0JDIuMI347KP9oIGLKTdh8,882
spacy/tokens/morphanalysis.pyx,sha256=HzFVh4er3CueJ6UdDEMqnr86iXHw5hsgnpPIHDL9BJY,3108
spacy/tokens/span.cp312-win_amd64.pyd,sha256=hRMA0Yu-BqzXiyIbMQXVLicaEEEiryJJ9blwh1_c40U,295936
spacy/tokens/span.cpp,sha256=spuyHVjQIteopL3ve1mbEnigd8QSjq9VsZHcXImuhx8,2272129
spacy/tokens/span.pxd,sha256=7iIQnVxur6yVBcw3qlwFf8OfDJkEjVu3UjpjxfjBzeI,546
spacy/tokens/span.pyi,sha256=Ak9qqshGkDp9lecNcYCMTnd-avJBSw7nWVp6FN_3jag,4091
spacy/tokens/span.pyx,sha256=axS-CpOpMHkC9DCic2OGOWlDgdIeKnPZ6OqSZD8caek,34445
spacy/tokens/span_group.cp312-win_amd64.pyd,sha256=QNgRkCIB34KjmJkqT74fG-kc0F0paAL1U-NYBGlr8Yg,209408
spacy/tokens/span_group.cpp,sha256=JXU28S9q5CPheKpPXkQO9sEi7CNVMQJIUCEO5nQgyWU,1676382
spacy/tokens/span_group.pxd,sha256=mIvYUGEbN3L0ADyX5BG06U-oLawZjahFyhqvFV1K1lQ,258
spacy/tokens/span_group.pyi,sha256=InJCWf1zR7TXYEMatvApWdRV2_GmzGSNT8fQgMA_dY8,893
spacy/tokens/span_group.pyx,sha256=K1G1if0Ncov9no0tp2TPWNitSHeMRPx3Bc_WvmtG0R4,12021
spacy/tokens/token.cp312-win_amd64.pyd,sha256=B9Bhc3NSl51HjFB5eWW1TUM3w2DOxsr3b-HaKR4Py-Q,257536
spacy/tokens/token.cpp,sha256=5PynSDfAHT0NJ1bY3C-CCr3WnsYcvdMLwYFhmSI8rAg,2189184
spacy/tokens/token.pxd,sha256=UXWezOPVBgnQUi8mqQEt4VORqYG3XnogKuTh-T49dCs,3563
spacy/tokens/token.pyi,sha256=CDWBXkp-0YCleSi-pHaFdYmZ5GPeHDMTJ2HIoGk3gxQ,5788
spacy/tokens/token.pyx,sha256=ZOMpbBHkvpCOZhV9sT0QglbFO7SDmVKPxwT1362ar0Y,34455
spacy/tokens/underscore.py,sha256=-QrFZVnTCSBWJx48DYnQJhWn_xcxaXCQSHSFzBcL6PA,5729
spacy/training/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy/training/__init__.py,sha256=-DorNoEVRmsbIitNQkM5OYKJNH3TqmazFgn7nKSFVUA,1345
spacy/training/__pycache__/__init__.cpython-312.pyc,,
spacy/training/__pycache__/alignment.cpython-312.pyc,,
spacy/training/__pycache__/augment.cpython-312.pyc,,
spacy/training/__pycache__/batchers.cpython-312.pyc,,
spacy/training/__pycache__/callbacks.cpython-312.pyc,,
spacy/training/__pycache__/corpus.cpython-312.pyc,,
spacy/training/__pycache__/initialize.cpython-312.pyc,,
spacy/training/__pycache__/iob_utils.cpython-312.pyc,,
spacy/training/__pycache__/loggers.cpython-312.pyc,,
spacy/training/__pycache__/loop.cpython-312.pyc,,
spacy/training/__pycache__/pretrain.cpython-312.pyc,,
spacy/training/align.cp312-win_amd64.pyd,sha256=MJXW_CW5D-TevuKLTNHPP1jJ4dq5AftENa7tDIUIA2A,69632
spacy/training/align.cpp,sha256=TmnSqe9_iCNhTx6ASZ3hGGBRnuG9rOUZ6WVbdyc-4kg,508756
spacy/training/align.pyx,sha256=Nbquxj8PYZC3b0oTuZzcN7ZThzrRReBZfkIq4WFRagQ,3367
spacy/training/alignment.py,sha256=5l1Ztzajrdlv8bDdoEKUQaiA9JYHiBJD2zwBZxTwB7Y,636
spacy/training/alignment_array.cp312-win_amd64.pyd,sha256=lX9HqhSu4t9oTxSoD_5YhF-XKGRoEGFhwSlTK-6ss68,61952
spacy/training/alignment_array.cpp,sha256=0qLFZM_it2ml6-YNuHd1MU_QenYo4xozaam1uNIJUNs,612278
spacy/training/alignment_array.pxd,sha256=vQ0-tboV53_orhuT80VeiYs6En3M7k96MTWZXtgTxSE,179
spacy/training/alignment_array.pyx,sha256=wi3o1Ci4kWZ4_knX__oeoZv5VhpaFecCfCC6qT_-KUk,2233
spacy/training/augment.py,sha256=nc4kG9FjEXIGPQdlragqX6_gam3MU32FowTvW5e_Nyk,13463
spacy/training/batchers.py,sha256=JhCjZZLsMg-hmiAJTnUpDWSkSfkz7DBvfCUN7fhz1QA,9227
spacy/training/callbacks.py,sha256=zbhHERd4DU9UNm52IEbqwZ3zk0atunKyeqvcNDgP1MU,1293
spacy/training/converters/__init__.py,sha256=il9NHMm0-WhfxbW5o03PPj4rcEm17gIe-k_xNPc12xg,228
spacy/training/converters/__pycache__/__init__.cpython-312.pyc,,
spacy/training/converters/__pycache__/conll_ner_to_docs.cpython-312.pyc,,
spacy/training/converters/__pycache__/conllu_to_docs.cpython-312.pyc,,
spacy/training/converters/__pycache__/iob_to_docs.cpython-312.pyc,,
spacy/training/converters/__pycache__/json_to_docs.cpython-312.pyc,,
spacy/training/converters/conll_ner_to_docs.py,sha256=IP1VOK84aYNK1k7pHx-uQimR7y50qeoZz9Ofpiwrxvs,6348
spacy/training/converters/conllu_to_docs.py,sha256=TEVmvjMp0g-laKKl6O-cUn_PSpKWlfHn_DwC4N8b3oc,10582
spacy/training/converters/iob_to_docs.py,sha256=-xUHWyHd8Kmmbor8p9ZCsYXtRuHKrmdbKNDnGxcJPok,2418
spacy/training/converters/json_to_docs.py,sha256=VmYoaSfEVJeQ-v0o0U_owV4sPuDAfPDYIFA6ITHXT4w,904
spacy/training/corpus.py,sha256=o3eyUxcPLWoDasIsAxDrUqeILKZPAzLPRmcpw73QMY4,12308
spacy/training/example.cp312-win_amd64.pyd,sha256=mXiEmDbB7dY50c_4MwEKueUcVv6L1oQA-wQJM_N7WKQ,315904
spacy/training/example.cpp,sha256=7t72J-Zsc4MfByi1KdEfoewQlcvs5m6GoICtleWYzX0,2414634
spacy/training/example.pxd,sha256=bicPNu-jB2PsaJGu_xTqXz56lVxhnsE5P0ztROALDCA,341
spacy/training/example.pyi,sha256=0ctIRMeuWwFyUStgZroK-FC9DbV-VJjcKBBjlHdFt9o,2082
spacy/training/example.pyx,sha256=KpQ5Iz74voxF5ArrUXwFd7QIKk4ne8Efh2aiqIJYU4g,25476
spacy/training/gold_io.cp312-win_amd64.pyd,sha256=2YX6sQk4ukUUuYBxKMUSF88jyW54H1nec4ZfLIquAEk,105472
spacy/training/gold_io.cpp,sha256=tCZdjf8EXt-aExjpB1AYvstus1t0Gol7gBE3IgWU2ck,743854
spacy/training/gold_io.pyx,sha256=81WUV_DSdyGo0Tle3XGeqbJzz16OOQHw1jPWDVTl09Y,8466
spacy/training/initialize.py,sha256=Q9r-K8wo7VGMusaItyiBUrq0C8wzA8C3XXCNOVFNgu8,14506
spacy/training/iob_utils.py,sha256=Dsrxr3R4ybPXVt6pKXvGJ2DbxKiSwkxcTTlcSf9Ghyg,9315
spacy/training/loggers.py,sha256=ZlZQ-ZySTn5qk6-TePOGvYzuH8nr9UXFWHZU-Es_AHs,7929
spacy/training/loop.py,sha256=XCwSO5xiQFc2BzNmF_P5nCfBQdDWv1hkljFqPvSNpvo,15415
spacy/training/pretrain.py,sha256=ONT-oPgLUKOwU8jrVSdGKxJW0ZK76d4TrB5ClIiGwH0,9970
spacy/ty.py,sha256=2FUR0ZX1bdFWcXwuzeelfO0XUcHzso7FKSgXtimHJAM,1410
spacy/typedefs.pxd,sha256=Wbphm-PgPqAazRJU9KpiVZ960sujaTWIhF5O80i2rH8,267
spacy/typedefs.pyx,sha256=BHrIYTbTg0wPXDU4nEu7Ep0XlDEAfwyyZzHZlzdkFeA,25
spacy/util.py,sha256=mwdNzcciujYbCdDC4U6K5x8NX5qyFIwtDIxA3BdkMx4,69707
spacy/vectors.cp312-win_amd64.pyd,sha256=1vZe-ZUFqRYxsFjGGKYvR_aCGi9gECPiVwJ-L2B2wBE,283136
spacy/vectors.cpp,sha256=QGOs8acfXEa95pasAelpG03JSOMSN3BjTDeWmu25xUE,1726176
spacy/vectors.pyx,sha256=XOwmPmo8nzA4YuLYYvLU7J8ulIr7CGwGZSdwliNlyRE,29033
spacy/vocab.cp312-win_amd64.pyd,sha256=frcUxN41YJ4BbmL_her4u_l6VDd8EGtlsleC2w2cs8w,299520
spacy/vocab.cpp,sha256=VAS8fcZlVWOddxbrapkWkDLhw_dyWsUQkjA-W9Jq4Lw,2246264
spacy/vocab.pxd,sha256=ayKvYuqFsCBecFN922rNBjNyewjy4VmDTsBp5ljLAAY,1512
spacy/vocab.pyi,sha256=KGx1kwHou3Na28L5poT4dDTUfaCAWhE5lUmKBXe7d9s,2984
spacy/vocab.pyx,sha256=Ya2gkRbWh2HLHjXg0qAp1dRPzW5i3Zl1fqRgmSCtvwg,26713
