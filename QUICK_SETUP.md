# 🚀 Quick Setup Guide - MySQL Topic Modeling Pipeline

## ⚡ 5-Minute Setup

### 1. Setup MySQL Database (2 minutes)
```sql
-- Open MySQL Workbench and run this file:
database/mysql_complete_setup.sql
```
This creates everything: database, tables, views, sample data, and foreign keys.

### 2. Configure Connection (1 minute)
Edit `config/database.json`:
```json
{
  "server": "localhost",
  "database": "TopicModelingDB",
  "username": "your_mysql_username",
  "password": "your_mysql_password",
  "port": 3306
}
```

### 3. Install Dependencies (1 minute)
```bash
pip install pymysql mysql-connector-python sqlalchemy
```

### 4. Test Setup (1 minute)
```bash
cd database
python test_connection.py
```

## ✅ Verification Checklist

After setup, you should see:
- ✅ 4 connected tables: `raw_articles` → `preprocessed_articles` → `topic_results` ← `topics`
- ✅ 8 sample topics (Technology, Politics, Business, etc.)
- ✅ 6 Power BI DirectQuery views
- ✅ All foreign key relationships working
- ✅ Connection test passes

## 🎯 Your Connected Workflow

```
NewsAPI Request
    ↓ (scripts/fetch_news.py)
raw_articles [processing_status='pending']
    ↓ (scripts/preprocess.py)
preprocessed_articles [linked by article_id]
    ↓ (scripts/topic_model.py)
topic_results [linked by preprocessed_id + article_id]
    ↓ (database/powerbi_views.sql)
Power BI DirectQuery Dashboard
```

## 🚀 Start Your Pipeline

```bash
# Set NewsAPI key
set NEWS_API_KEY=your_actual_key

# Place your LDA model
cp your_model.pkl models/lda_model.pkl

# Run single cycle
python automated_pipeline.py --single-run

# Or run continuously
python automated_pipeline.py --continuous
```

## 📊 Power BI Connection

1. **Open Power BI Desktop**
2. **Get Data** → **MySQL Database**
3. **Server**: `localhost:3306`
4. **Database**: `TopicModelingDB`
5. **Import Views**: Select all `vw_*` views
6. **DirectQuery Mode**: For real-time updates

## 🗂️ File Organization

```
topic_modelling/
├── 📁 database/                 # MySQL setup & connection
│   ├── mysql_complete_setup.sql # Run this first!
│   ├── test_connection.py       # Test your setup
│   └── README.md               # Detailed database docs
├── 📁 scripts/                 # Pipeline components
│   ├── fetch_news.py           # NewsAPI → raw_articles
│   ├── preprocess.py           # raw_articles → preprocessed_articles
│   └── topic_model.py          # preprocessed_articles → topic_results
├── 📁 config/                  # Configuration
│   └── database.json           # MySQL connection settings
├── automated_pipeline.py       # Main pipeline orchestrator
└── QUICK_SETUP.md              # This file
```

## 🆘 Need Help?

- **Database Issues**: Check `database/README.md`
- **Connection Problems**: Run `database/test_connection.py`
- **Pipeline Errors**: Check `logs/` directory
- **Power BI Setup**: See `powerbi/DIRECTQUERY_SETUP.md`

Your MySQL-powered topic modeling pipeline is ready! 🎉
