"""
Text preprocessing pipeline for news articles.
Handles cleaning, tokenization, and normalization using NLTK and spaCy.
"""

import re
import string
import pandas as pd
import numpy as np
from typing import List, Set, Optional, Dict, Any
from pathlib import Path
import html

# NLTK imports
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import WordNetLemmatizer
from nltk.tag import pos_tag

# spaCy imports
try:
    import spacy
    from spacy.lang.en.stop_words import STOP_WORDS as SPACY_STOP_WORDS
    nlp = spacy.load("en_core_web_sm")
except (ImportError, OSError):
    nlp = None
    SPACY_STOP_WORDS = set()

from loguru import logger
from .config import config
from .utils import save_dataframe, validate_dataframe

class TextPreprocessor:
    """Comprehensive text preprocessing for news articles."""
    
    def __init__(self):
        """Initialize the text preprocessor."""
        self.config = config.preprocessing
        self._setup_nltk()
        self._setup_stopwords()
        self._setup_lemmatizer()
        
    def _setup_nltk(self):
        """Download required NLTK data."""
        required_data = ['punkt', 'stopwords', 'wordnet', 'averaged_perceptron_tagger', 'omw-1.4']
        
        for data in required_data:
            try:
                nltk.data.find(f'tokenizers/{data}')
            except LookupError:
                try:
                    nltk.download(data, quiet=True)
                    logger.info(f"Downloaded NLTK data: {data}")
                except Exception as e:
                    logger.warning(f"Failed to download NLTK data {data}: {e}")
    
    def _setup_stopwords(self):
        """Set up stopwords from multiple sources."""
        self.stopwords = set()
        
        # NLTK stopwords
        try:
            self.stopwords.update(stopwords.words('english'))
        except Exception as e:
            logger.warning(f"Could not load NLTK stopwords: {e}")
        
        # spaCy stopwords
        self.stopwords.update(SPACY_STOP_WORDS)
        
        # Custom stopwords from config
        custom_stopwords = self.config.get('custom_stopwords', [])
        self.stopwords.update(custom_stopwords)
        
        # Common news-specific stopwords
        news_stopwords = {
            'said', 'says', 'according', 'reuters', 'news', 'report', 'reported',
            'told', 'asked', 'added', 'continued', 'stated', 'mentioned',
            'article', 'story', 'post', 'update', 'breaking', 'latest',
            'today', 'yesterday', 'tomorrow', 'monday', 'tuesday', 'wednesday',
            'thursday', 'friday', 'saturday', 'sunday'
        }
        self.stopwords.update(news_stopwords)
        
        logger.info(f"Loaded {len(self.stopwords)} stopwords")
    
    def _setup_lemmatizer(self):
        """Set up lemmatizer."""
        try:
            self.lemmatizer = WordNetLemmatizer()
        except Exception as e:
            logger.warning(f"Could not initialize lemmatizer: {e}")
            self.lemmatizer = None
    
    def clean_html(self, text: str) -> str:
        """Remove HTML tags and decode HTML entities."""
        if not isinstance(text, str):
            return ""
        
        # Decode HTML entities
        text = html.unescape(text)
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Remove common HTML artifacts
        text = re.sub(r'&[a-zA-Z0-9#]+;', '', text)
        
        return text
    
    def clean_text(self, text: str) -> str:
        """Basic text cleaning."""
        if not isinstance(text, str):
            return ""
        
        # Clean HTML
        text = self.clean_html(text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def remove_punctuation(self, text: str) -> str:
        """Remove punctuation from text."""
        if not isinstance(text, str):
            return ""
        
        # Keep some punctuation that might be meaningful
        punctuation_to_remove = string.punctuation.replace('-', '').replace('_', '')
        
        # Remove punctuation
        text = text.translate(str.maketrans('', '', punctuation_to_remove))
        
        return text
    
    def tokenize(self, text: str, method: str = 'nltk') -> List[str]:
        """
        Tokenize text into words.
        
        Args:
            text: Input text
            method: Tokenization method ('nltk' or 'spacy')
            
        Returns:
            List of tokens
        """
        if not isinstance(text, str) or not text.strip():
            return []
        
        if method == 'spacy' and nlp is not None:
            doc = nlp(text)
            tokens = [token.text.lower() for token in doc if not token.is_space]
        else:
            # Fallback to NLTK
            try:
                tokens = word_tokenize(text.lower())
            except Exception:
                # Simple split as last resort
                tokens = text.lower().split()
        
        return tokens
    
    def filter_tokens(self, tokens: List[str]) -> List[str]:
        """
        Filter tokens based on configuration.
        
        Args:
            tokens: List of tokens
            
        Returns:
            Filtered tokens
        """
        filtered_tokens = []
        
        min_length = self.config.get('min_word_length', 3)
        max_length = self.config.get('max_word_length', 20)
        remove_numbers = self.config.get('remove_numbers', True)
        remove_stopwords = self.config.get('remove_stopwords', True)
        
        for token in tokens:
            # Skip empty tokens
            if not token or not token.strip():
                continue
            
            # Length filter
            if len(token) < min_length or len(token) > max_length:
                continue
            
            # Number filter
            if remove_numbers and token.isdigit():
                continue
            
            # Stopword filter
            if remove_stopwords and token.lower() in self.stopwords:
                continue
            
            # Only keep alphabetic tokens (with some exceptions)
            if not re.match(r'^[a-zA-Z]+$', token):
                continue
            
            filtered_tokens.append(token)
        
        return filtered_tokens
    
    def lemmatize_tokens(self, tokens: List[str], method: str = 'nltk') -> List[str]:
        """
        Lemmatize tokens.
        
        Args:
            tokens: List of tokens
            method: Lemmatization method ('nltk' or 'spacy')
            
        Returns:
            Lemmatized tokens
        """
        if not tokens:
            return []
        
        if method == 'spacy' and nlp is not None:
            # Use spaCy for lemmatization
            text = ' '.join(tokens)
            doc = nlp(text)
            lemmatized = [token.lemma_.lower() for token in doc if token.lemma_ != '-PRON-']
        else:
            # Use NLTK lemmatizer
            if self.lemmatizer is None:
                return tokens
            
            # Get POS tags for better lemmatization
            try:
                pos_tags = pos_tag(tokens)
                lemmatized = []
                
                for word, pos in pos_tags:
                    # Convert POS tag to WordNet format
                    wordnet_pos = self._get_wordnet_pos(pos)
                    lemma = self.lemmatizer.lemmatize(word.lower(), wordnet_pos)
                    lemmatized.append(lemma)
            except Exception:
                # Fallback to simple lemmatization
                lemmatized = [self.lemmatizer.lemmatize(token.lower()) for token in tokens]
        
        return lemmatized
    
    def _get_wordnet_pos(self, treebank_tag: str) -> str:
        """Convert Treebank POS tag to WordNet POS tag."""
        if treebank_tag.startswith('J'):
            return 'a'  # adjective
        elif treebank_tag.startswith('V'):
            return 'v'  # verb
        elif treebank_tag.startswith('N'):
            return 'n'  # noun
        elif treebank_tag.startswith('R'):
            return 'r'  # adverb
        else:
            return 'n'  # default to noun
    
    def preprocess_text(self, text: str, return_tokens: bool = False) -> str:
        """
        Complete text preprocessing pipeline.
        
        Args:
            text: Input text
            return_tokens: Whether to return tokens or joined text
            
        Returns:
            Preprocessed text or tokens
        """
        if not isinstance(text, str):
            return [] if return_tokens else ""
        
        # Step 1: Clean text
        cleaned = self.clean_text(text)
        
        # Step 2: Remove punctuation if configured
        if self.config.get('remove_punctuation', True):
            cleaned = self.remove_punctuation(cleaned)
        
        # Step 3: Tokenize
        tokens = self.tokenize(cleaned)
        
        # Step 4: Filter tokens
        tokens = self.filter_tokens(tokens)
        
        # Step 5: Lemmatize if configured
        if self.config.get('lemmatize', True):
            tokens = self.lemmatize_tokens(tokens)
        
        # Step 6: Final filtering (remove any empty tokens)
        tokens = [token for token in tokens if token and token.strip()]
        
        if return_tokens:
            return tokens
        else:
            return ' '.join(tokens)
    
    def preprocess_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Preprocess articles in a DataFrame.
        
        Args:
            df: DataFrame with articles
            
        Returns:
            DataFrame with preprocessed text
        """
        if df.empty:
            logger.warning("Empty DataFrame provided for preprocessing")
            return df
        
        # Validate required columns
        required_columns = ['title']
        if not validate_dataframe(df, required_columns):
            raise ValueError("DataFrame missing required columns")
        
        # Create a copy to avoid modifying original
        processed_df = df.copy()
        
        # Combine title and content for processing
        text_columns = []
        if 'title' in df.columns:
            text_columns.append('title')
        if 'description' in df.columns:
            text_columns.append('description')
        if 'content' in df.columns:
            text_columns.append('content')
        
        # Combine text from available columns
        combined_text = []
        for idx, row in df.iterrows():
            text_parts = []
            for col in text_columns:
                if col in row and pd.notna(row[col]) and str(row[col]).strip():
                    text_parts.append(str(row[col]))
            
            combined = ' '.join(text_parts)
            combined_text.append(combined)
        
        processed_df['combined_text'] = combined_text
        
        # Preprocess the combined text
        logger.info(f"Preprocessing {len(processed_df)} articles...")
        
        processed_texts = []
        processed_tokens = []
        
        for i, text in enumerate(processed_df['combined_text']):
            if i % 100 == 0:
                logger.info(f"Processed {i}/{len(processed_df)} articles")
            
            processed_text = self.preprocess_text(text)
            tokens = self.preprocess_text(text, return_tokens=True)
            
            processed_texts.append(processed_text)
            processed_tokens.append(tokens)
        
        processed_df['cleaned_text'] = processed_texts
        processed_df['tokens'] = processed_tokens
        
        # Calculate text statistics
        processed_df['token_count'] = processed_df['tokens'].apply(len)
        processed_df['char_count'] = processed_df['cleaned_text'].apply(len)
        
        # Filter out articles with too few tokens
        min_tokens = 10  # Minimum tokens for meaningful analysis
        initial_count = len(processed_df)
        processed_df = processed_df[processed_df['token_count'] >= min_tokens]
        final_count = len(processed_df)
        
        if initial_count > final_count:
            logger.info(f"Filtered out {initial_count - final_count} articles with fewer than {min_tokens} tokens")
        
        logger.info(f"Preprocessing completed. {len(processed_df)} articles ready for topic modeling")
        
        return processed_df

def main():
    """Main function for running text preprocessing."""
    from .utils import load_articles, get_latest_file
    
    logger.info("Starting text preprocessing...")
    
    # Load articles
    data_dir = config.get('output.data_dir', 'data')
    raw_file = config.get('output.raw_articles_file', 'raw_articles.csv')
    input_path = Path(data_dir) / raw_file
    
    if not input_path.exists():
        # Try to find the latest raw articles file
        latest_file = get_latest_file(data_dir, "raw_articles*.csv")
        if latest_file:
            input_path = Path(latest_file)
        else:
            logger.error(f"No articles file found. Please run data ingestion first.")
            return
    
    # Load and preprocess articles
    df = load_articles(str(input_path))
    if df.empty:
        logger.error("No articles to process")
        return
    
    preprocessor = TextPreprocessor()
    processed_df = preprocessor.preprocess_dataframe(df)
    
    # Save processed articles
    output_file = config.get('output.processed_articles_file', 'processed_articles.csv')
    output_path = Path(data_dir) / output_file
    
    save_dataframe(processed_df, str(output_path))
    logger.info(f"Preprocessing completed. Results saved to {output_path}")

if __name__ == "__main__":
    main()
