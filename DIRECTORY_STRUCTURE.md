# 📁 **Final Clean Directory Structure & How It Works**

## 🏗️ **Complete Project Architecture**

```
topic_modelling/                           # 🏠 Root project directory
│
├── 📁 database/                            # 🗄️ SQL Server Integration Layer
│   ├── schema.sql                         # 📋 Complete database schema (tables, indexes, procedures)
│   ├── powerbi_views.sql                  # 📊 Optimized views for Power BI DirectQuery
│   ├── sql_connection.py                  # 🔗 High-performance SQL Server connection manager
│   └── sql_exporter.py                    # 📤 Real-time data export to SQL Server
│
├── 📁 scripts/                             # ⚙️ Core Pipeline Components
│   ├── fetch_news.py                      # 📰 Advanced NewsAPI data ingestion
│   ├── preprocess.py                      # 🔤 NLTK/spaCy text preprocessing
│   ├── topic_model.py                     # 🧠 LDA model integration service
│   └── run_pipeline.py                    # 🚀 Complete pipeline orchestrator
│
├── 📁 config/                              # ⚙️ Configuration Management
│   ├── database.json                      # 🗄️ SQL Server connection configuration
│   ├── database.json.example              # 📝 Configuration template with examples
│   └── pipeline.yaml                      # 📋 Pipeline settings and parameters
│
├── 📁 powerbi/                             # 📊 Power BI DirectQuery Integration
│   └── DIRECTQUERY_SETUP.md               # 📖 Complete Power BI DirectQuery setup guide
│
├── 📁 scheduler/                           # ⏰ Automation & Scheduling
│   ├── windows_task.xml                   # 🪟 Windows Task Scheduler configuration
│   └── crontab.txt                        # 🐧 Linux/Mac cron job templates
│
├── 📁 models/                              # 🧠 Machine Learning Models
│   └── lda_model.pkl                      # ← 🎯 YOUR TRAINED LDA MODEL (place here)
│
├── 📁 data/                                # 💾 Temporary Data Storage
│   ├── raw_articles.csv                   # 📄 Collected news articles (temporary)
│   └── processed_articles.csv             # 🏷️ Articles with topic classifications (temporary)
│
├── 📁 logs/                                # 📝 Logging & Monitoring
│   ├── pipeline.log                       # 📋 Main pipeline execution logs
│   ├── monitor.log                        # 🔍 Health monitoring logs
│   └── pipeline_status.json               # 📊 Current pipeline status
│
├── 📈 monitor_pipeline.py                  # 🏥 Comprehensive health monitoring system
├── ⚙️ setup.py                             # 🛠️ Setup validation and environment check
├── 📋 requirements.txt                     # 📦 Python dependencies (includes SQL Server)
├── 📖 README.md                            # 📚 Complete project documentation
├── 📖 DEPLOYMENT_SUMMARY.md                # 🚀 Deployment guide and summary
└── 📖 PROJECT_STATUS.md                    # ✅ Project status and verification
```

## 🔄 **How The System Works**

### **1. Data Flow Architecture**
```
📰 NewsAPI → 🔤 Text Processing → 🧠 LDA Classification → 🗄️ SQL Server → 📊 Power BI DirectQuery → 📈 Live Dashboard
```

### **2. Component Interaction**

#### **🔄 Pipeline Execution Flow:**
```
1. run_pipeline.py (Orchestrator)
   ↓
2. fetch_news.py (Data Ingestion)
   ↓ 
3. preprocess.py (Text Cleaning)
   ↓
4. topic_model.py (LDA Classification)
   ↓
5. sql_exporter.py (Database Export)
   ↓
6. Power BI DirectQuery (Live Visualization)
```

## 📊 **Detailed Component Explanations**

### **🗄️ Database Layer (`database/`)**

**Purpose:** Real-time SQL Server integration replacing CSV exports

**Files:**
- **`schema.sql`** - Creates optimized database schema with:
  - Core tables: `articles`, `topics`, `article_topics`
  - Aggregated tables: `topic_trends_daily`, `topic_trends_hourly`
  - Monitoring tables: `pipeline_runs`, `data_quality_metrics`
  - Performance indexes and stored procedures

- **`powerbi_views.sql`** - Creates 7 optimized views for Power BI:
  - `vw_topic_summary` - Topic overview and KPIs
  - `vw_daily_trends_enhanced` - Daily trends with rolling averages
  - `vw_hourly_trends_enhanced` - Hourly patterns with time intelligence
  - `vw_recent_articles` - Latest classified articles
  - `vw_source_performance` - News source analysis
  - `vw_confidence_distribution` - Model performance metrics
  - `vw_pipeline_health` - Real-time monitoring

- **`sql_connection.py`** - High-performance connection manager with:
  - Connection pooling for scalability
  - Automatic retry logic
  - Health monitoring
  - Bulk operation support

- **`sql_exporter.py`** - Real-time data export system with:
  - Bulk insert operations
  - Upsert logic for data updates
  - Aggregation generation
  - Pipeline run tracking

### **⚙️ Pipeline Layer (`scripts/`)**

**Purpose:** Core data processing pipeline components

**Files:**
- **`fetch_news.py`** - Advanced news data ingestion:
  - NewsAPI integration with rate limiting
  - RSS feed fallback support
  - Deduplication logic
  - Data validation and cleaning

- **`preprocess.py`** - Comprehensive text preprocessing:
  - HTML cleaning and URL removal
  - NLTK/spaCy tokenization and lemmatization
  - Stop word removal and text normalization
  - Batch processing for efficiency

- **`topic_model.py`** - LDA model integration service:
  - Loads your trained `.pkl` model
  - Applies preprocessing pipeline
  - Performs topic classification with confidence scoring
  - Handles model validation and error recovery

- **`run_pipeline.py`** - Complete pipeline orchestrator:
  - Coordinates all pipeline steps
  - SQL Server integration
  - Comprehensive error handling
  - Scheduling and automation support
  - Health monitoring and logging

### **⚙️ Configuration Layer (`config/`)**

**Purpose:** Centralized configuration management

**Files:**
- **`database.json`** - SQL Server connection settings:
  - Server address and database name
  - Authentication configuration
  - Connection pooling parameters
  - Performance optimization settings

- **`database.json.example`** - Configuration template with:
  - Example configurations for different scenarios
  - Windows Authentication vs SQL Authentication
  - Local vs remote server setups
  - Azure SQL Database configuration

- **`pipeline.yaml`** - Pipeline configuration:
  - NewsAPI settings and keywords
  - Processing parameters
  - Scheduling configuration
  - Model settings and thresholds

### **📊 Power BI Integration (`powerbi/`)**

**Purpose:** Real-time dashboard setup and configuration

**Files:**
- **`DIRECTQUERY_SETUP.md`** - Complete DirectQuery setup guide:
  - Step-by-step Power BI connection instructions
  - SQL Server view explanations
  - Dashboard creation templates
  - Performance optimization tips
  - Troubleshooting guide

### **⏰ Automation Layer (`scheduler/`)**

**Purpose:** Automated pipeline execution

**Files:**
- **`windows_task.xml`** - Windows Task Scheduler configuration:
  - Pre-configured hourly execution
  - Error handling and logging
  - Service account setup

- **`crontab.txt`** - Linux/Mac cron job templates:
  - Hourly, daily, and weekly options
  - Log rotation and error handling
  - Environment variable setup

### **🧠 Models Layer (`models/`)**

**Purpose:** Machine learning model storage

**Expected Files:**
- **`lda_model.pkl`** - Your trained LDA model:
  - Should contain model, vectorizer, and topic information
  - Used by `topic_model.py` for classification
  - Validated during setup process

### **💾 Data Layer (`data/`)**

**Purpose:** Temporary CSV storage (transitional)

**Files:**
- **`raw_articles.csv`** - Temporary storage for collected articles
- **`processed_articles.csv`** - Temporary storage for classified articles

**Note:** These CSV files are only used for processing. The main data storage is in SQL Server.

### **📝 Monitoring Layer (`logs/` + `monitor_pipeline.py`)**

**Purpose:** Comprehensive system monitoring and health checks

**Files:**
- **`monitor_pipeline.py`** - Health monitoring system:
  - 7 comprehensive health checks
  - Email alerting capabilities
  - Performance metrics tracking
  - Continuous monitoring mode

**Log Files:**
- **`pipeline.log`** - Main execution logs
- **`monitor.log`** - Health monitoring logs
- **`pipeline_status.json`** - Current status snapshot

## 🚀 **Execution Workflow**

### **1. Initial Setup**
```bash
# 1. Setup SQL Server database
sqlcmd -S localhost -d TopicModelingDB -i database/schema.sql
sqlcmd -S localhost -d TopicModelingDB -i database/powerbi_views.sql

# 2. Configure connections
cp config/database.json.example config/database.json
# Edit with your SQL Server details

# 3. Place your LDA model
cp your_model.pkl models/lda_model.pkl

# 4. Validate setup
python setup.py
```

### **2. Pipeline Execution**
```bash
# Manual execution
python scripts/run_pipeline.py run --use-database

# Automated scheduling
python scripts/run_pipeline.py schedule --use-database
```

### **3. Monitoring**
```bash
# Health check
python monitor_pipeline.py check

# Continuous monitoring
python monitor_pipeline.py monitor --interval 300
```

### **4. Power BI Setup**
```bash
# Follow the complete guide
# See: powerbi/DIRECTQUERY_SETUP.md
```

## 🎯 **Key Benefits of This Architecture**

### **✅ Real-Time Capabilities**
- **Zero manual refresh** - Power BI updates automatically via DirectQuery
- **Live data streaming** from pipeline to dashboard
- **Instant availability** of processed data

### **✅ Enterprise Features**
- **Scalable SQL Server** backend for large datasets
- **High-performance** connection pooling and bulk operations
- **Comprehensive monitoring** and alerting
- **Production-ready** error handling and logging

### **✅ Maintainability**
- **Clean separation** of concerns across layers
- **Modular components** for easy updates
- **Comprehensive documentation** for each component
- **Standardized configuration** management

## 🔧 **Customization Points**

### **Pipeline Configuration**
- Modify `config/pipeline.yaml` for different news sources or keywords
- Adjust `config/database.json` for different SQL Server setups
- Update scheduling in `scheduler/` files for different frequencies

### **Model Integration**
- Replace `models/lda_model.pkl` with your trained model
- Modify `scripts/topic_model.py` for different model types
- Adjust confidence thresholds in configuration

### **Dashboard Customization**
- Modify SQL views in `database/powerbi_views.sql` for different metrics
- Follow `powerbi/DIRECTQUERY_SETUP.md` for custom dashboard creation
- Add new views for additional analysis requirements

---

## 🎉 **This Clean Architecture Provides:**

- ✅ **Real-time SQL Server integration** with optimized performance
- ✅ **Power BI DirectQuery** for live dashboards without manual refresh
- ✅ **Modular, maintainable code** with clear separation of concerns
- ✅ **Enterprise-grade monitoring** and error handling
- ✅ **Complete automation** with scheduling and health checks
- ✅ **Comprehensive documentation** for setup and maintenance

**Your topic modeling system is now production-ready with a clean, scalable architecture!** 🚀
