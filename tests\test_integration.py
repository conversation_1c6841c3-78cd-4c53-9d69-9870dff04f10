"""
Integration tests for the complete topic modeling pipeline.
"""

import pytest
import pandas as pd
import tempfile
import shutil
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import yaml

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

class TestPipelineIntegration:
    """Test complete pipeline integration."""
    
    @pytest.fixture
    def temp_workspace(self):
        """Create a temporary workspace for testing."""
        temp_dir = tempfile.mkdtemp()
        workspace = Path(temp_dir)
        
        # Create directory structure
        (workspace / 'data').mkdir()
        (workspace / 'logs').mkdir()
        (workspace / 'models').mkdir()
        
        yield workspace
        
        # Cleanup
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_config(self, temp_workspace):
        """Create a sample configuration for testing."""
        config_data = {
            'news_api': {
                'api_key': 'test_key',
                'base_url': 'https://test.com',
                'query_keywords': ['test'],
                'page_size': 10,
                'max_pages': 1
            },
            'preprocessing': {
                'min_word_length': 3,
                'max_word_length': 20,
                'remove_stopwords': True,
                'lemmatize': True,
                'remove_numbers': True,
                'remove_punctuation': True,
                'custom_stopwords': []
            },
            'topic_modeling': {
                'num_topics': 3,
                'max_features': 50,
                'min_df': 1,
                'max_df': 0.9,
                'ngram_range': [1, 2],
                'random_state': 42,
                'max_iter': 10
            },
            'output': {
                'data_dir': str(temp_workspace / 'data'),
                'model_dir': str(temp_workspace / 'models'),
                'raw_articles_file': 'raw_articles.csv',
                'processed_articles_file': 'processed_articles.csv',
                'topic_trends_file': 'topic_trends.csv',
                'powerbi_export_file': 'powerbi_data.csv'
            },
            'logging': {
                'level': 'INFO',
                'log_file': str(temp_workspace / 'logs' / 'test.log')
            }
        }
        
        config_path = temp_workspace / 'config.yaml'
        with open(config_path, 'w') as f:
            yaml.dump(config_data, f)
        
        return config_path
    
    @pytest.fixture
    def sample_articles_data(self):
        """Create sample articles data for testing."""
        return pd.DataFrame({
            'title': [
                'AI Revolution in Healthcare Technology',
                'Election Results Show Democratic Victory',
                'Stock Market Reaches Record High',
                'Machine Learning Breakthrough in Medicine',
                'Political Campaign Spending Increases',
                'Economic Growth Continues Despite Challenges',
                'Artificial Intelligence in Education',
                'Voting Rights Legislation Passes',
                'Financial Markets Show Volatility',
                'Deep Learning Applications Expand'
            ],
            'description': [
                'Artificial intelligence is transforming healthcare with new diagnostic tools',
                'Election results show significant changes in political landscape',
                'Stock market indices reach new record highs amid economic optimism',
                'Researchers achieve breakthrough in machine learning for medical diagnosis',
                'Political campaigns increase spending on digital advertising',
                'Economic indicators show continued growth despite global challenges',
                'AI technology is being integrated into educational systems',
                'New legislation expands voting rights and access',
                'Financial markets experience increased volatility',
                'Deep learning finds new applications in various industries'
            ],
            'content': [
                'Artificial intelligence and machine learning technologies are revolutionizing healthcare by providing advanced diagnostic tools and treatment recommendations.',
                'The recent election results demonstrate significant shifts in the political landscape with democratic candidates winning key races.',
                'Stock market indices have reached record highs as investors show optimism about economic recovery and growth prospects.',
                'Medical researchers have achieved a major breakthrough using machine learning algorithms to improve diagnostic accuracy.',
                'Political campaigns are increasing their spending on digital advertising and social media outreach to reach voters.',
                'Economic growth continues at a steady pace despite facing various global challenges and uncertainties.',
                'Educational institutions are integrating artificial intelligence technology to enhance learning experiences and outcomes.',
                'New voting rights legislation has been passed to expand access and protect democratic participation.',
                'Financial markets are experiencing increased volatility due to various economic and geopolitical factors.',
                'Deep learning applications are expanding into new industries and use cases beyond traditional tech sectors.'
            ],
            'url': [f'https://example.com/article{i}' for i in range(10)],
            'publishedAt': pd.date_range('2024-01-01', periods=10, freq='D'),
            'source': [{'name': f'Source {i%3 + 1}'} for i in range(10)],
            'author': [f'Author {i%4 + 1}' for i in range(10)]
        })
    
    def test_preprocessing_integration(self, temp_workspace, sample_config, sample_articles_data):
        """Test preprocessing integration."""
        # Mock config
        with patch('src.preprocessing.config') as mock_config:
            mock_config.preprocessing = {
                'min_word_length': 3,
                'max_word_length': 20,
                'remove_stopwords': True,
                'lemmatize': True,
                'remove_numbers': True,
                'remove_punctuation': True,
                'custom_stopwords': []
            }
            mock_config.get.side_effect = lambda key, default=None: {
                'output.data_dir': str(temp_workspace / 'data'),
                'output.processed_articles_file': 'processed_articles.csv'
            }.get(key, default)
            
            from src.preprocessing import TextPreprocessor
            
            # Save sample data
            raw_file = temp_workspace / 'data' / 'raw_articles.csv'
            sample_articles_data.to_csv(raw_file, index=False)
            
            # Process data
            preprocessor = TextPreprocessor()
            processed_df = preprocessor.preprocess_dataframe(sample_articles_data)
            
            # Verify processing
            assert not processed_df.empty
            assert 'cleaned_text' in processed_df.columns
            assert 'tokens' in processed_df.columns
            assert 'token_count' in processed_df.columns
            
            # Check that text is actually processed
            assert all(processed_df['token_count'] > 0)
    
    def test_topic_modeling_integration(self, temp_workspace, sample_config, sample_articles_data):
        """Test topic modeling integration."""
        # Mock config
        with patch('src.topic_modeling.config') as mock_config:
            mock_config.topic_modeling = {
                'num_topics': 3,
                'max_features': 50,
                'min_df': 1,
                'max_df': 0.9,
                'ngram_range': [1, 2],
                'random_state': 42,
                'max_iter': 10,
                'learning_method': 'batch'
            }
            mock_config.get.side_effect = lambda key, default=None: {
                'output.model_dir': str(temp_workspace / 'models'),
                'output.data_dir': str(temp_workspace / 'data'),
                'output.topic_trends_file': 'topic_trends.csv'
            }.get(key, default)
            
            from src.topic_modeling import TopicModeler
            from src.preprocessing import TextPreprocessor
            
            # First preprocess the data
            with patch('src.preprocessing.config', mock_config):
                preprocessor = TextPreprocessor()
                processed_df = preprocessor.preprocess_dataframe(sample_articles_data)
            
            # Filter for sufficient content
            processed_df = processed_df[processed_df['cleaned_text'].str.len() > 10]
            
            if len(processed_df) >= 3:  # Need minimum documents for topic modeling
                # Train topic model
                modeler = TopicModeler(method='sklearn')
                documents = processed_df['cleaned_text'].tolist()
                modeler.fit(documents)
                
                # Predict topics
                predictions = modeler.predict_topics(documents)
                
                # Verify results
                assert len(predictions) == len(documents)
                assert modeler.model is not None
                assert len(modeler.topics) == 3
    
    def test_data_export_integration(self, temp_workspace, sample_config):
        """Test data export integration."""
        # Create sample topic modeling results
        topic_results = pd.DataFrame({
            'title': ['Article 1', 'Article 2', 'Article 3'],
            'description': ['Desc 1', 'Desc 2', 'Desc 3'],
            'content': ['Content 1', 'Content 2', 'Content 3'],
            'cleaned_text': ['clean text one', 'clean text two', 'clean text three'],
            'publishedAt': pd.date_range('2024-01-01', periods=3),
            'source_name': ['Source 1', 'Source 2', 'Source 1'],
            'topic_id': [0, 1, 0],
            'topic_label': ['Tech', 'Politics', 'Tech'],
            'topic_keywords': ['tech, ai', 'politics, election', 'tech, ai'],
            'topic_confidence': [0.8, 0.9, 0.7],
            'token_count': [10, 12, 8],
            'char_count': [100, 120, 80]
        })
        
        # Save topic results
        topic_file = temp_workspace / 'data' / 'topic_trends.csv'
        topic_results.to_csv(topic_file, index=False)
        
        # Mock config
        with patch('src.data_export.config') as mock_config:
            mock_config.get.side_effect = lambda key, default=None: {
                'output.data_dir': str(temp_workspace / 'data'),
                'output.powerbi_export_file': 'powerbi_data.csv',
                'output.topic_trends_file': 'topic_trends.csv'
            }.get(key, default)
            
            from src.data_export import PowerBIExporter
            
            # Export data
            exporter = PowerBIExporter()
            exported_files = exporter.export_for_powerbi(str(topic_file))
            
            # Verify exports
            assert len(exported_files) > 0
            assert 'main_data' in exported_files
            
            # Check that files exist
            main_file = Path(exported_files['main_data'])
            assert main_file.exists()
            
            # Verify exported data structure
            exported_df = pd.read_csv(main_file)
            assert not exported_df.empty
            assert 'Article_Title' in exported_df.columns
            assert 'Topic_ID' in exported_df.columns

class TestPipelineErrorHandling:
    """Test error handling in pipeline integration."""
    
    def test_missing_data_files(self, temp_workspace):
        """Test handling of missing data files."""
        with patch('src.data_export.config') as mock_config:
            mock_config.get.side_effect = lambda key, default=None: {
                'output.data_dir': str(temp_workspace / 'data'),
                'output.topic_trends_file': 'nonexistent.csv'
            }.get(key, default)
            
            from src.data_export import PowerBIExporter
            
            exporter = PowerBIExporter()
            exported_files = exporter.export_for_powerbi()
            
            # Should handle gracefully
            assert exported_files == {}
    
    def test_empty_dataframes(self, temp_workspace):
        """Test handling of empty dataframes."""
        # Create empty CSV file
        empty_file = temp_workspace / 'data' / 'empty.csv'
        empty_file.parent.mkdir(exist_ok=True)
        pd.DataFrame().to_csv(empty_file, index=False)
        
        with patch('src.data_export.config') as mock_config:
            mock_config.get.side_effect = lambda key, default=None: {
                'output.data_dir': str(temp_workspace / 'data'),
                'output.topic_trends_file': 'empty.csv'
            }.get(key, default)
            
            from src.data_export import PowerBIExporter
            
            exporter = PowerBIExporter()
            exported_files = exporter.export_for_powerbi(str(empty_file))
            
            # Should handle empty data gracefully
            assert isinstance(exported_files, dict)
    
    def test_malformed_data(self, temp_workspace):
        """Test handling of malformed data."""
        # Create malformed CSV
        malformed_file = temp_workspace / 'data' / 'malformed.csv'
        malformed_file.parent.mkdir(exist_ok=True)
        
        with open(malformed_file, 'w') as f:
            f.write("invalid,csv,data\n")
            f.write("missing,columns\n")
            f.write("inconsistent,data,structure,extra\n")
        
        with patch('src.data_export.config') as mock_config:
            mock_config.get.side_effect = lambda key, default=None: {
                'output.data_dir': str(temp_workspace / 'data'),
                'output.topic_trends_file': 'malformed.csv'
            }.get(key, default)
            
            from src.data_export import PowerBIExporter
            
            exporter = PowerBIExporter()
            
            # Should handle malformed data without crashing
            try:
                exported_files = exporter.export_for_powerbi(str(malformed_file))
                assert isinstance(exported_files, dict)
            except Exception as e:
                # If it raises an exception, it should be a specific, handled exception
                assert "missing" in str(e).lower() or "column" in str(e).lower()
