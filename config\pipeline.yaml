# Real-Time Topic Modeling Configuration
# Configuration for hourly news collection and LDA classification

# News API Configuration
news_api:
  api_key: "from_environment"  # Set in .env file
  base_url: "https://newsapi.org/v2/everything"
  query_keywords:
    - "technology"
    - "artificial intelligence"
    - "politics"
    - "economy"
    - "business"
  language: "en"
  sort_by: "publishedAt"
  page_size: 50
  max_pages: 2

# RSS Feeds (fallback when API limits reached)
rss_feeds:
  - "https://feeds.bbci.co.uk/news/rss.xml"
  - "https://rss.cnn.com/rss/edition.rss"
  - "https://feeds.reuters.com/reuters/topNews"

# LDA Model Configuration
model:
  # Path to your trained .pkl file
  file_path: "models/lda_model.pkl"

  # Alternative paths (system will try in order)
  fallback_paths:
    - "models/latest_model.pkl"
    - "models/topic_model_*.pkl"

  # Classification settings
  confidence_threshold: 0.3
  min_text_length: 50

# Hourly Collection Settings
collection:
  enabled: true
  schedule: "every hour at :05"  # 5 minutes past each hour
  hours_lookback: 2  # Look back 2 hours for new articles
  max_articles_per_hour: 100

# Text Preprocessing
preprocessing:
  min_word_length: 3
  max_word_length: 20
  remove_stopwords: true
  remove_numbers: true
  remove_punctuation: true
  custom_stopwords:
    - "said"
    - "says"
    - "according"
    - "reuters"
    - "news"

# Output Configuration
output:
  data_dir: "data"
  hourly_dir: "data/hourly"
  processed_dir: "data/processed"
  dashboard_dir: "data/dashboard"

  # File naming
  hourly_file_pattern: "news_{timestamp}.csv"
  processed_file_pattern: "classified_{timestamp}.csv"

  # Dashboard exports
  dashboard_files:
    hourly_trends: "hourly_trends.csv"
    realtime_articles: "realtime_articles.csv"
    topic_summary: "topic_summary.csv"

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  log_file: "logs/realtime_service.log"
  max_log_size: "10MB"
  backup_count: 5

# Service Configuration
service:
  check_interval: 30  # seconds
  max_retries: 3
  retry_delay: 60  # seconds

# Power BI Integration
powerbi:
  auto_export: true
  export_interval: "hourly"
  keep_history_hours: 24
