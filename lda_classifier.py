#!/usr/bin/env python3
"""
LDA Classification Service
Step 3: [CSV] → [LDA Model] → [Classified Data]
"""

import pickle
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
import os
import re
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LDAClassifier:
    """Simple LDA classifier using pre-trained .pkl model."""
    
    def __init__(self, model_path: str = "models/lda_model.pkl"):
        """
        Initialize LDA classifier.
        
        Args:
            model_path: Path to .pkl model file
        """
        self.model_path = model_path
        self.model = None
        self.vectorizer = None
        self.topics = None
        self.is_loaded = False
        
        # Try to load model
        self.load_model()
    
    def load_model(self) -> bool:
        """
        Load LDA model from .pkl file.
        
        Returns:
            True if model loaded successfully, False otherwise
        """
        
        # Check if model file exists
        if not os.path.exists(self.model_path):
            logger.error(f"❌ Model file not found: {self.model_path}")
            
            # Try alternative paths
            alternative_paths = [
                "models/latest_model.pkl",
                "models/topic_model.pkl"
            ]
            
            for alt_path in alternative_paths:
                if os.path.exists(alt_path):
                    logger.info(f"🔄 Trying alternative model: {alt_path}")
                    self.model_path = alt_path
                    break
            else:
                logger.error("❌ No model file found in any location")
                return False
        
        try:
            logger.info(f"🧠 Loading LDA model from: {self.model_path}")
            
            # Load pickle file
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # Extract components
            self.model = model_data['model']
            self.vectorizer = model_data['vectorizer']
            self.topics = model_data['topics']
            
            logger.info(f"✅ Model loaded successfully")
            logger.info(f"📊 Found {len(self.topics)} topics")
            
            # Display topics
            for topic_id, topic_info in self.topics.items():
                label = topic_info['label']
                keywords = ', '.join(topic_info['keywords'][:3])
                logger.info(f"  Topic {topic_id}: {label} ({keywords}...)")
            
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            return False
    
    def preprocess_text(self, text: str) -> str:
        """
        Simple text preprocessing.
        
        Args:
            text: Raw text to preprocess
            
        Returns:
            Cleaned text
        """
        
        if not text or not isinstance(text, str):
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def classify_article(self, text: str, confidence_threshold: float = 0.3) -> Optional[Dict]:
        """
        Classify a single article.
        
        Args:
            text: Article text to classify
            confidence_threshold: Minimum confidence for classification
            
        Returns:
            Classification result or None if confidence too low
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return None
        
        if not text or len(text.strip()) < 20:
            logger.warning("⚠️ Text too short for classification")
            return None
        
        try:
            # Preprocess text
            clean_text = self.preprocess_text(text)
            
            # Vectorize text
            text_vector = self.vectorizer.transform([clean_text])
            
            # Get topic probabilities
            topic_probs = self.model.transform(text_vector)[0]
            
            # Find best topic
            best_topic_id = np.argmax(topic_probs)
            best_confidence = topic_probs[best_topic_id]
            
            # Check confidence threshold
            if best_confidence < confidence_threshold:
                logger.debug(f"⚠️ Low confidence: {best_confidence:.3f} < {confidence_threshold}")
                return None
            
            # Get topic information
            topic_info = self.topics[best_topic_id]
            
            result = {
                'topic_id': int(best_topic_id),
                'topic_label': topic_info['label'],
                'topic_confidence': float(best_confidence),
                'topic_keywords': topic_info['keywords'][:5]  # Top 5 keywords
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Classification failed: {e}")
            return None
    
    def classify_articles_batch(self, articles_df: pd.DataFrame, 
                               text_column: str = 'full_text',
                               confidence_threshold: float = 0.3) -> List[Dict]:
        """
        Classify multiple articles from DataFrame.
        
        Args:
            articles_df: DataFrame with articles
            text_column: Column name containing text to classify
            confidence_threshold: Minimum confidence for classification
            
        Returns:
            List of articles with topic classifications
        """
        
        if not self.is_loaded:
            logger.error("❌ Model not loaded")
            return []
        
        if articles_df.empty:
            logger.warning("⚠️ No articles to classify")
            return []
        
        logger.info(f"🔍 Classifying {len(articles_df)} articles...")
        
        classified_articles = []
        successful_classifications = 0
        
        for idx, row in articles_df.iterrows():
            try:
                # Get text content
                text = row.get(text_column, '')
                
                # Classify article
                classification = self.classify_article(text, confidence_threshold)
                
                if classification:
                    # Combine original article data with classification
                    article_with_topic = row.to_dict()
                    article_with_topic.update(classification)
                    classified_articles.append(article_with_topic)
                    successful_classifications += 1
                else:
                    logger.debug(f"⚠️ Skipped article {idx}: low confidence or error")
                
            except Exception as e:
                logger.error(f"❌ Failed to classify article {idx}: {e}")
                continue
        
        logger.info(f"✅ Successfully classified {successful_classifications}/{len(articles_df)} articles")
        return classified_articles
    
    def get_topic_summary(self) -> Dict:
        """
        Get summary of available topics.
        
        Returns:
            Dictionary with topic information
        """
        
        if not self.is_loaded:
            return {}
        
        summary = {}
        for topic_id, topic_info in self.topics.items():
            summary[topic_id] = {
                'label': topic_info['label'],
                'keywords': topic_info['keywords'][:5]
            }
        
        return summary

def main():
    """Test the LDA classifier."""
    
    # Create classifier
    classifier = LDAClassifier()
    
    if not classifier.is_loaded:
        logger.error("❌ Cannot test - model not loaded")
        return
    
    # Test single classification
    test_text = "Artificial intelligence and machine learning are transforming the technology industry with new innovations."
    
    result = classifier.classify_article(test_text)
    if result:
        logger.info(f"🎯 Classification result:")
        logger.info(f"  Topic: {result['topic_label']}")
        logger.info(f"  Confidence: {result['topic_confidence']:.3f}")
        logger.info(f"  Keywords: {', '.join(result['topic_keywords'])}")
    else:
        logger.warning("⚠️ No classification result")
    
    # Show topic summary
    topics = classifier.get_topic_summary()
    logger.info(f"📊 Available topics: {len(topics)}")
    for topic_id, info in topics.items():
        logger.info(f"  {topic_id}: {info['label']}")

if __name__ == "__main__":
    main()
