"""
Configuration management for the topic modeling pipeline.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration manager for the topic modeling pipeline."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize configuration.
        
        Args:
            config_path: Path to the YAML configuration file
        """
        self.config_path = Path(config_path)
        self._config = self._load_config()
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        
        # Override with environment variables
        self._override_with_env_vars(config)
        
        return config
    
    def _override_with_env_vars(self, config: Dict[str, Any]) -> None:
        """Override configuration with environment variables."""
        # News API key
        if os.getenv('NEWS_API_KEY'):
            config['news_api']['api_key'] = os.getenv('NEWS_API_KEY')
        
        # Log level
        if os.getenv('LOG_LEVEL'):
            config['logging']['level'] = os.getenv('LOG_LEVEL')
    
    def _validate_config(self) -> None:
        """Validate required configuration parameters."""
        required_sections = ['news_api', 'preprocessing', 'topic_modeling', 'output']
        
        for section in required_sections:
            if section not in self._config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate API key
        if not self._config['news_api']['api_key'] or self._config['news_api']['api_key'] == "YOUR_NEWS_API_KEY_HERE":
            print("Warning: NEWS_API_KEY not set. Please update config.yaml or .env file.")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'news_api.api_key')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.
        
        Args:
            section: Section name
            
        Returns:
            Configuration section as dictionary
        """
        return self._config.get(section, {})
    
    @property
    def news_api(self) -> Dict[str, Any]:
        """Get news API configuration."""
        return self.get_section('news_api')
    
    @property
    def preprocessing(self) -> Dict[str, Any]:
        """Get preprocessing configuration."""
        return self.get_section('preprocessing')
    
    @property
    def topic_modeling(self) -> Dict[str, Any]:
        """Get topic modeling configuration."""
        return self.get_section('topic_modeling')
    
    @property
    def output(self) -> Dict[str, Any]:
        """Get output configuration."""
        return self.get_section('output')
    
    @property
    def logging(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.get_section('logging')

# Global configuration instance
config = Config()
