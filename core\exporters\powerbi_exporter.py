"""
Data export module for Power BI integration.
Formats and exports topic modeling results in Power BI-ready format.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional
import json

from loguru import logger
from .config import config
from .utils import load_articles, save_dataframe, get_latest_file, create_summary_stats

class PowerBIExporter:
    """Export topic modeling results for Power BI consumption."""
    
    def __init__(self):
        """Initialize the Power BI exporter."""
        self.data_dir = Path(config.get('output.data_dir', 'data'))
        self.export_file = config.get('output.powerbi_export_file', 'powerbi_data.csv')
    
    def prepare_powerbi_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare data for Power BI with proper schema and formatting.
        
        Args:
            df: DataFrame with topic modeling results
            
        Returns:
            Power BI-ready DataFrame
        """
        if df.empty:
            logger.warning("Empty DataFrame provided for Power BI export")
            return pd.DataFrame()
        
        # Create a copy for processing
        powerbi_df = df.copy()
        
        # Ensure required columns exist
        required_columns = {
            'title': 'Article Title',
            'publishedAt': 'Published Date',
            'topic_id': 'Topic ID',
            'topic_label': 'Topic Label',
            'topic_keywords': 'Topic Keywords'
        }
        
        for col, description in required_columns.items():
            if col not in powerbi_df.columns:
                logger.warning(f"Missing column: {col} ({description})")
                powerbi_df[col] = ''
        
        # Clean and format data for Power BI
        powerbi_df = self._clean_for_powerbi(powerbi_df)
        
        # Add derived columns for better analysis
        powerbi_df = self._add_derived_columns(powerbi_df)
        
        # Select and rename columns for Power BI
        powerbi_columns = {
            'title': 'Article_Title',
            'description': 'Article_Description',
            'content': 'Article_Content',
            'cleaned_text': 'Cleaned_Text',
            'url': 'Article_URL',
            'publishedAt': 'Published_Date',
            'source_name': 'Source_Name',
            'author': 'Author',
            'topic_id': 'Topic_ID',
            'topic_label': 'Topic_Label',
            'topic_keywords': 'Topic_Keywords',
            'topic_confidence': 'Topic_Confidence',
            'token_count': 'Token_Count',
            'char_count': 'Character_Count',
            'published_year': 'Published_Year',
            'published_month': 'Published_Month',
            'published_day': 'Published_Day',
            'published_hour': 'Published_Hour',
            'weekday': 'Weekday',
            'is_weekend': 'Is_Weekend',
            'content_length_category': 'Content_Length_Category',
            'topic_strength': 'Topic_Strength'
        }
        
        # Select available columns
        available_columns = {k: v for k, v in powerbi_columns.items() if k in powerbi_df.columns}
        final_df = powerbi_df[list(available_columns.keys())].copy()
        final_df.columns = list(available_columns.values())
        
        logger.info(f"Prepared {len(final_df)} articles for Power BI export")
        return final_df
    
    def _clean_for_powerbi(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean data for Power BI compatibility."""
        # Handle missing values
        df = df.fillna('')
        
        # Clean text fields
        text_columns = ['title', 'description', 'content', 'cleaned_text']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str)
                df[col] = df[col].str.replace('\n', ' ').str.replace('\r', ' ')
                df[col] = df[col].str.replace('\t', ' ')
                df[col] = df[col].str.strip()
        
        # Ensure numeric columns are properly typed
        numeric_columns = ['topic_id', 'topic_confidence', 'token_count', 'char_count']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Format dates
        if 'publishedAt' in df.columns:
            df['publishedAt'] = pd.to_datetime(df['publishedAt'], errors='coerce')
        
        return df
    
    def _add_derived_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add derived columns for enhanced analysis."""
        # Date-based columns
        if 'publishedAt' in df.columns and df['publishedAt'].notna().any():
            df['published_year'] = df['publishedAt'].dt.year
            df['published_month'] = df['publishedAt'].dt.month
            df['published_day'] = df['publishedAt'].dt.day
            df['published_hour'] = df['publishedAt'].dt.hour
            df['weekday'] = df['publishedAt'].dt.day_name()
            df['is_weekend'] = df['publishedAt'].dt.weekday >= 5
        
        # Content length categories
        if 'char_count' in df.columns:
            df['content_length_category'] = pd.cut(
                df['char_count'],
                bins=[0, 500, 1500, 3000, float('inf')],
                labels=['Short', 'Medium', 'Long', 'Very Long']
            )
        
        # Topic strength categories
        if 'topic_confidence' in df.columns:
            df['topic_strength'] = pd.cut(
                df['topic_confidence'],
                bins=[0, 0.3, 0.6, 0.8, 1.0],
                labels=['Low', 'Medium', 'High', 'Very High']
            )
        
        return df
    
    def create_topic_summary_table(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create a topic summary table for Power BI.
        
        Args:
            df: DataFrame with topic modeling results
            
        Returns:
            Topic summary DataFrame
        """
        if df.empty or 'topic_id' not in df.columns:
            return pd.DataFrame()
        
        # Group by topic
        topic_summary = df.groupby(['topic_id', 'topic_label']).agg({
            'title': 'count',
            'topic_confidence': ['mean', 'std'],
            'char_count': 'mean',
            'publishedAt': ['min', 'max']
        }).round(3)
        
        # Flatten column names
        topic_summary.columns = [
            'Article_Count',
            'Avg_Confidence',
            'Std_Confidence',
            'Avg_Content_Length',
            'Earliest_Article',
            'Latest_Article'
        ]
        
        # Reset index
        topic_summary = topic_summary.reset_index()
        topic_summary.columns = [
            'Topic_ID',
            'Topic_Label',
            'Article_Count',
            'Avg_Confidence',
            'Std_Confidence',
            'Avg_Content_Length',
            'Earliest_Article',
            'Latest_Article'
        ]
        
        # Add topic keywords
        if 'topic_keywords' in df.columns:
            keywords_map = df.groupby('topic_id')['topic_keywords'].first().to_dict()
            topic_summary['Topic_Keywords'] = topic_summary['Topic_ID'].map(keywords_map)
        
        # Calculate topic percentage
        total_articles = len(df)
        topic_summary['Topic_Percentage'] = (topic_summary['Article_Count'] / total_articles * 100).round(2)
        
        # Sort by article count
        topic_summary = topic_summary.sort_values('Article_Count', ascending=False)
        
        logger.info(f"Created topic summary with {len(topic_summary)} topics")
        return topic_summary
    
    def create_time_series_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create time series data for trend analysis.
        
        Args:
            df: DataFrame with topic modeling results
            
        Returns:
            Time series DataFrame
        """
        if df.empty or 'publishedAt' not in df.columns:
            return pd.DataFrame()
        
        # Ensure datetime
        df['publishedAt'] = pd.to_datetime(df['publishedAt'], errors='coerce')
        df = df.dropna(subset=['publishedAt'])
        
        if df.empty:
            return pd.DataFrame()
        
        # Create daily aggregations
        df['date'] = df['publishedAt'].dt.date
        
        # Group by date and topic
        time_series = df.groupby(['date', 'topic_id', 'topic_label']).agg({
            'title': 'count',
            'topic_confidence': 'mean'
        }).reset_index()
        
        time_series.columns = [
            'Date',
            'Topic_ID',
            'Topic_Label',
            'Article_Count',
            'Avg_Confidence'
        ]
        
        # Fill missing dates for each topic
        date_range = pd.date_range(
            start=time_series['Date'].min(),
            end=time_series['Date'].max(),
            freq='D'
        )
        
        topics = time_series[['Topic_ID', 'Topic_Label']].drop_duplicates()
        
        # Create complete date-topic combinations
        complete_index = []
        for _, topic in topics.iterrows():
            for date in date_range:
                complete_index.append({
                    'Date': date.date(),
                    'Topic_ID': topic['Topic_ID'],
                    'Topic_Label': topic['Topic_Label']
                })
        
        complete_df = pd.DataFrame(complete_index)
        
        # Merge with actual data
        time_series = complete_df.merge(
            time_series,
            on=['Date', 'Topic_ID', 'Topic_Label'],
            how='left'
        ).fillna(0)
        
        # Sort by date and topic
        time_series = time_series.sort_values(['Date', 'Topic_ID'])
        
        logger.info(f"Created time series data with {len(time_series)} records")
        return time_series
    
    def export_for_powerbi(self, input_file: str = None) -> Dict[str, str]:
        """
        Export all data for Power BI consumption.
        
        Args:
            input_file: Optional input file path
            
        Returns:
            Dictionary with exported file paths
        """
        logger.info("Starting Power BI data export...")
        
        # Load topic modeling results
        if input_file is None:
            topic_file = config.get('output.topic_trends_file', 'topic_trends.csv')
            input_path = self.data_dir / topic_file
            
            if not input_path.exists():
                # Try to find the latest topic trends file
                latest_file = get_latest_file(str(self.data_dir), "topic_trends*.csv")
                if latest_file:
                    input_path = Path(latest_file)
                else:
                    logger.error("No topic trends file found. Please run topic modeling first.")
                    return {}
        else:
            input_path = Path(input_file)
        
        # Load data
        df = load_articles(str(input_path))
        if df.empty:
            logger.error("No data to export")
            return {}
        
        exported_files = {}
        
        # 1. Main Power BI dataset
        powerbi_data = self.prepare_powerbi_data(df)
        if not powerbi_data.empty:
            main_export_path = self.data_dir / self.export_file
            save_dataframe(powerbi_data, str(main_export_path), add_timestamp=False)
            exported_files['main_data'] = str(main_export_path)
            logger.info(f"Main dataset exported to: {main_export_path}")
        
        # 2. Topic summary
        topic_summary = self.create_topic_summary_table(df)
        if not topic_summary.empty:
            summary_path = self.data_dir / 'powerbi_topic_summary.csv'
            save_dataframe(topic_summary, str(summary_path), add_timestamp=False)
            exported_files['topic_summary'] = str(summary_path)
            logger.info(f"Topic summary exported to: {summary_path}")
        
        # 3. Time series data
        time_series = self.create_time_series_data(df)
        if not time_series.empty:
            timeseries_path = self.data_dir / 'powerbi_time_series.csv'
            save_dataframe(time_series, str(timeseries_path), add_timestamp=False)
            exported_files['time_series'] = str(timeseries_path)
            logger.info(f"Time series data exported to: {timeseries_path}")
        
        # 4. Export metadata
        metadata = self._create_export_metadata(df)
        metadata_path = self.data_dir / 'powerbi_metadata.json'
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, default=str)
        exported_files['metadata'] = str(metadata_path)
        logger.info(f"Metadata exported to: {metadata_path}")
        
        logger.info(f"Power BI export completed. {len(exported_files)} files created.")
        return exported_files
    
    def _create_export_metadata(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Create metadata about the exported data."""
        metadata = {
            'export_timestamp': datetime.now().isoformat(),
            'total_articles': len(df),
            'date_range': {},
            'topics': {},
            'sources': {},
            'schema': {}
        }
        
        # Date range
        if 'publishedAt' in df.columns:
            df['publishedAt'] = pd.to_datetime(df['publishedAt'], errors='coerce')
            valid_dates = df['publishedAt'].dropna()
            if not valid_dates.empty:
                metadata['date_range'] = {
                    'earliest': valid_dates.min().isoformat(),
                    'latest': valid_dates.max().isoformat(),
                    'span_days': (valid_dates.max() - valid_dates.min()).days
                }
        
        # Topic information
        if 'topic_id' in df.columns:
            topic_counts = df['topic_id'].value_counts().to_dict()
            metadata['topics'] = {
                'total_topics': len(topic_counts),
                'topic_distribution': topic_counts
            }
        
        # Source information
        if 'source_name' in df.columns:
            source_counts = df['source_name'].value_counts().head(10).to_dict()
            metadata['sources'] = source_counts
        
        # Schema information
        metadata['schema'] = {
            'columns': list(df.columns),
            'data_types': df.dtypes.astype(str).to_dict()
        }
        
        return metadata

def main():
    """Main function for running data export."""
    logger.info("Starting data export for Power BI...")
    
    exporter = PowerBIExporter()
    exported_files = exporter.export_for_powerbi()
    
    if exported_files:
        logger.info("Data export completed successfully!")
        for file_type, file_path in exported_files.items():
            logger.info(f"  {file_type}: {file_path}")
    else:
        logger.error("Data export failed")

if __name__ == "__main__":
    main()
