#!/usr/bin/env python3
"""
Simple Setup Script for Topic Modeling Workflow
Creates necessary directories and validates setup
"""

import os
import sys
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_directories():
    """Create necessary directories for the workflow."""
    
    directories = [
        'data',
        'data/powerbi',
        'models',
        'logs',
        'config'
    ]
    
    logger.info("📁 Creating directories...")
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"  ✅ {directory}")
    
    logger.info("✅ All directories created")

def check_model_file():
    """Check if LDA model file exists."""
    
    model_paths = [
        'models/lda_model.pkl',
        'models/latest_model.pkl',
        'models/topic_model.pkl'
    ]
    
    logger.info("🧠 Checking for LDA model file...")
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            logger.info(f"  ✅ Found model: {model_path}")
            return True
    
    logger.warning("  ⚠️ No LDA model file found")
    logger.info("  💡 Place your trained .pkl model in: models/lda_model.pkl")
    return False

def check_environment():
    """Check environment configuration."""
    
    logger.info("⚙️ Checking environment configuration...")
    
    # Check if .env file exists
    env_file = 'config/.env'
    if os.path.exists(env_file):
        logger.info(f"  ✅ Environment file found: {env_file}")
        
        # Check if API key is set
        try:
            with open(env_file, 'r') as f:
                content = f.read()
                if 'NEWS_API_KEY=' in content and 'your_news_api_key_here' not in content:
                    logger.info("  ✅ NEWS_API_KEY appears to be configured")
                    return True
                else:
                    logger.warning("  ⚠️ NEWS_API_KEY not configured")
                    return False
        except Exception as e:
            logger.error(f"  ❌ Error reading .env file: {e}")
            return False
    else:
        logger.warning(f"  ⚠️ Environment file not found: {env_file}")
        logger.info("  💡 Copy config/.env.example to config/.env and add your API key")
        return False

def validate_dependencies():
    """Validate that required dependencies are installed."""
    
    logger.info("📦 Checking dependencies...")
    
    required_packages = [
        'pandas',
        'numpy',
        'scikit-learn',
        'requests',
        'python-dotenv',
        'schedule'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"  ✅ {package}")
        except ImportError:
            logger.warning(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ Missing packages: {', '.join(missing_packages)}")
        logger.info("💡 Install with: pip install -r requirements.txt")
        return False
    
    logger.info("✅ All dependencies installed")
    return True

def main():
    """Main setup function."""
    
    logger.info("🚀 Setting up Topic Modeling Workflow")
    logger.info("=" * 50)
    
    # Create directories
    create_directories()
    
    # Check dependencies
    deps_ok = validate_dependencies()
    
    # Check model file
    model_ok = check_model_file()
    
    # Check environment
    env_ok = check_environment()
    
    # Summary
    logger.info("\n📊 Setup Summary:")
    logger.info("=" * 30)
    
    checks = [
        ("Dependencies", deps_ok),
        ("LDA Model", model_ok),
        ("Environment", env_ok)
    ]
    
    all_ok = True
    for check_name, status in checks:
        icon = "✅" if status else "❌"
        logger.info(f"  {icon} {check_name}")
        if not status:
            all_ok = False
    
    if all_ok:
        logger.info("\n🎉 Setup complete! Ready to run workflow.")
        logger.info("\n🚀 Next steps:")
        logger.info("  1. Test single run: python main_workflow.py run")
        logger.info("  2. Start hourly schedule: python main_workflow.py schedule")
        logger.info("  3. Check status: python main_workflow.py status")
    else:
        logger.info("\n⚠️ Setup incomplete. Please fix the issues above.")
        logger.info("\n💡 Quick fixes:")
        if not deps_ok:
            logger.info("  - Install dependencies: pip install -r requirements.txt")
        if not model_ok:
            logger.info("  - Place your .pkl model in: models/lda_model.pkl")
        if not env_ok:
            logger.info("  - Configure API key in: config/.env")
        
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
