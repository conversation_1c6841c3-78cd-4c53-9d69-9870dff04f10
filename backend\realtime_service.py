#!/usr/bin/env python3
"""
Backend Service for Real-Time Topic Modeling Pipeline.
Orchestrates hourly data collection, LDA classification, and dashboard updates.
"""

import sys
import time
import threading
import logging
from datetime import datetime
from pathlib import Path
import json
from typing import Dict, Any

# Add core modules to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'core'))

from collectors.hourly_news import HourlyNewsCollector, HourlyDataManager
from models.lda_classifier import LDAModelService, RealTimeProcessor
from config_manager import config
from utils import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

class TopicModelingBackend:
    """Backend service that orchestrates the entire real-time pipeline."""
    
    def __init__(self):
        """Initialize backend service."""
        self.hourly_collector = HourlyNewsCollector()
        self.lda_service = LDAModelService()
        self.data_manager = HourlyDataManager()
        
        # Service status
        self.is_running = False
        self.services_status = {
            'hourly_collector': False,
            'lda_processor': False,
            'model_loaded': False
        }
        
        # Data directory
        self.data_dir = Path(config.get('output.data_dir', 'data'))
        self.status_file = self.data_dir / 'backend_status.json'
        
        logger.info("🚀 Topic Modeling Backend initialized")
    
    def check_model_availability(self) -> bool:
        """Check if trained LDA model is available."""
        try:
            model_info = self.lda_service.get_model_info()
            if model_info['loaded']:
                self.services_status['model_loaded'] = True
                logger.info(f"✅ LDA model loaded: {model_info['num_topics']} topics")
                return True
            else:
                logger.error("❌ No trained LDA model found")
                return False
        except Exception as e:
            logger.error(f"❌ Error checking model: {e}")
            return False
    
    def start_hourly_collection_service(self):
        """Start the hourly data collection service in a separate thread."""
        def collection_worker():
            try:
                logger.info("🕐 Starting hourly collection service...")
                self.services_status['hourly_collector'] = True
                
                # Run collection every hour
                import schedule
                schedule.every().hour.at(":05").do(self._run_hourly_cycle)
                
                # Run initial collection
                self._run_hourly_cycle()
                
                while self.is_running:
                    schedule.run_pending()
                    time.sleep(60)  # Check every minute
                    
            except Exception as e:
                logger.error(f"❌ Hourly collection service failed: {e}")
                self.services_status['hourly_collector'] = False
        
        collection_thread = threading.Thread(target=collection_worker, daemon=True)
        collection_thread.start()
        logger.info("✅ Hourly collection service started")
    
    def start_lda_processing_service(self):
        """Start the LDA processing service in a separate thread."""
        def lda_worker():
            try:
                logger.info("🧠 Starting LDA processing service...")
                self.services_status['lda_processor'] = True
                
                # Monitor for new data signals
                signal_file = self.data_dir / 'new_data_signal.txt'
                
                while self.is_running:
                    if signal_file.exists():
                        try:
                            # Read signal
                            with open(signal_file, 'r') as f:
                                lines = f.readlines()
                            
                            if lines:
                                csv_file_path = lines[0].strip()
                                logger.info(f"🔄 Processing new data: {csv_file_path}")
                                
                                # Classify data
                                result = self.lda_service.classify_csv_data(csv_file_path)
                                
                                if result['success']:
                                    logger.info(f"✅ Classified {result['articles_processed']} articles")
                                    self._update_dashboard_status(result)
                                else:
                                    logger.error(f"❌ Classification failed: {result.get('error')}")
                                
                                # Remove signal file
                                signal_file.unlink()
                        
                        except Exception as e:
                            logger.error(f"Error processing LDA signal: {e}")
                            if signal_file.exists():
                                signal_file.unlink()
                    
                    time.sleep(30)  # Check every 30 seconds
                    
            except Exception as e:
                logger.error(f"❌ LDA processing service failed: {e}")
                self.services_status['lda_processor'] = False
        
        lda_thread = threading.Thread(target=lda_worker, daemon=True)
        lda_thread.start()
        logger.info("✅ LDA processing service started")
    
    def _run_hourly_cycle(self):
        """Run a complete hourly collection and processing cycle."""
        try:
            logger.info("🔄 Starting hourly cycle...")
            
            # Collect hourly data
            result = self.hourly_collector.run_hourly_collection()
            
            if result['success'] and result['articles_collected'] > 0:
                logger.info(f"📰 Collected {result['articles_collected']} articles")
                
                # The collection automatically triggers LDA processing via signal file
                # So we just need to update our status
                self._update_backend_status({
                    'last_collection': result['timestamp'],
                    'articles_collected': result['articles_collected'],
                    'status': 'active'
                })
            else:
                logger.info("📰 No new articles collected this hour")
                
        except Exception as e:
            logger.error(f"❌ Hourly cycle failed: {e}")
    
    def _update_dashboard_status(self, classification_result: Dict[str, Any]):
        """Update dashboard with latest classification results."""
        try:
            dashboard_status = {
                'last_update': datetime.now().isoformat(),
                'articles_processed': classification_result.get('articles_processed', 0),
                'classification_summary': classification_result.get('summary', {}),
                'status': 'updated'
            }
            
            # Save dashboard status
            dashboard_file = self.data_dir / 'dashboard_status.json'
            with open(dashboard_file, 'w') as f:
                json.dump(dashboard_status, f, indent=2)
            
            logger.info("📊 Dashboard status updated")
            
        except Exception as e:
            logger.error(f"Error updating dashboard status: {e}")
    
    def _update_backend_status(self, status_data: Dict[str, Any]):
        """Update backend service status."""
        try:
            full_status = {
                'backend_running': self.is_running,
                'services': self.services_status,
                'last_status_update': datetime.now().isoformat(),
                **status_data
            }
            
            with open(self.status_file, 'w') as f:
                json.dump(full_status, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error updating backend status: {e}")
    
    def start_backend_service(self):
        """Start the complete backend service."""
        logger.info("🚀 Starting Topic Modeling Backend Service...")
        
        # Check prerequisites
        if not self.check_model_availability():
            logger.error("❌ Cannot start backend: No trained model available")
            logger.info("💡 Please run: python run_pipeline.py --step modeling")
            return False
        
        # Set running flag
        self.is_running = True
        
        # Start services
        self.start_hourly_collection_service()
        self.start_lda_processing_service()
        
        # Update initial status
        self._update_backend_status({
            'startup_time': datetime.now().isoformat(),
            'status': 'running'
        })
        
        logger.info("✅ Backend service started successfully!")
        logger.info("📊 Real-time topic modeling is now active")
        logger.info("🔄 Hourly data collection scheduled")
        logger.info("🧠 LDA classification service running")
        
        return True
    
    def stop_backend_service(self):
        """Stop the backend service."""
        logger.info("🛑 Stopping backend service...")
        self.is_running = False
        
        # Update status
        self._update_backend_status({
            'shutdown_time': datetime.now().isoformat(),
            'status': 'stopped'
        })
        
        logger.info("✅ Backend service stopped")
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get current service status."""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r') as f:
                    return json.load(f)
            else:
                return {'status': 'not_running'}
        except Exception as e:
            logger.error(f"Error reading service status: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def cleanup_old_data(self):
        """Clean up old data files."""
        try:
            cleaned_count = self.data_manager.cleanup_old_files(days_to_keep=7)
            logger.info(f"🧹 Cleaned up {cleaned_count} old files")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Main function with command line interface."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Topic Modeling Backend Service")
    parser.add_argument('command', choices=['start', 'stop', 'status', 'cleanup'], 
                       help='Backend command')
    parser.add_argument('--daemon', action='store_true', 
                       help='Run as daemon service')
    
    args = parser.parse_args()
    
    backend = TopicModelingBackend()
    
    if args.command == 'start':
        if backend.start_backend_service():
            try:
                if args.daemon:
                    # Run as daemon
                    while True:
                        time.sleep(60)
                else:
                    # Interactive mode
                    print("Backend service running. Press Ctrl+C to stop...")
                    while True:
                        time.sleep(1)
            except KeyboardInterrupt:
                backend.stop_backend_service()
                print("\nBackend service stopped.")
        else:
            sys.exit(1)
    
    elif args.command == 'stop':
        backend.stop_backend_service()
        print("Backend service stop signal sent.")
    
    elif args.command == 'status':
        status = backend.get_service_status()
        print(json.dumps(status, indent=2))
    
    elif args.command == 'cleanup':
        backend.cleanup_old_data()
        print("Cleanup completed.")

if __name__ == "__main__":
    main()
