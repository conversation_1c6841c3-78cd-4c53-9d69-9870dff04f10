"""
Setup script for the topic modeling pipeline.
This script downloads required NLTK data and spaCy models.
"""

import subprocess
import sys
import nltk
from pathlib import Path

def download_nltk_data():
    """Download required NLTK data."""
    print("Downloading NLTK data...")
    
    nltk_data = [
        'punkt',
        'stopwords', 
        'wordnet',
        'averaged_perceptron_tagger',
        'omw-1.4'
    ]
    
    for data in nltk_data:
        try:
            nltk.download(data, quiet=True)
            print(f"✓ Downloaded {data}")
        except Exception as e:
            print(f"✗ Failed to download {data}: {e}")

def download_spacy_model():
    """Download spaCy English model."""
    print("Downloading spaCy English model...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "spacy", "download", "en_core_web_sm"
        ])
        print("✓ Downloaded en_core_web_sm")
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to download spaCy model: {e}")

def create_env_file():
    """Create .env file from template if it doesn't exist."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        env_file.write_text(env_example.read_text())
        print("✓ Created .env file from template")
        print("⚠️  Please update .env file with your API keys")
    else:
        print("ℹ️  .env file already exists")

def main():
    """Run setup process."""
    print("🚀 Setting up Topic Modeling Pipeline...")
    print("=" * 50)
    
    # Create .env file
    create_env_file()
    
    # Download NLTK data
    download_nltk_data()
    
    # Download spaCy model
    download_spacy_model()
    
    print("=" * 50)
    print("✅ Setup complete!")
    print("\nNext steps:")
    print("1. Update .env file with your NEWS_API_KEY")
    print("2. Modify config.yaml as needed")
    print("3. Run: python run_pipeline.py")

if __name__ == "__main__":
    main()
