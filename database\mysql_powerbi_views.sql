-- =====================================================
-- MySQL Power BI DirectQuery Optimized Views
-- Compatible with MySQL Workbench and Power BI
-- =====================================================

USE TopicModelingDB;

-- =====================================================
-- 1. OVERVIEW DASHBOARD VIEWS
-- =====================================================

-- Real-time topic summary for overview cards (MySQL compatible)
CREATE OR REPLACE VIEW vw_topic_summary AS
SELECT 
    t.topic_id,
    t.topic_label,
    t.topic_keywords,
    COUNT(tr.result_id) as total_articles,
    AVG(tr.confidence) as avg_confidence,
    STDDEV(tr.confidence) as confidence_std,
    MIN(tr.confidence) as min_confidence,
    MAX(tr.confidence) as max_confidence,
    MIN(ra.published_at) as first_article_date,
    MAX(ra.published_at) as latest_article_date,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    CAST(COUNT(tr.result_id) * 100.0 / (SELECT COUNT(*) FROM topic_results) AS DECIMAL(5,2)) as percentage,
    MAX(tr.classification_timestamp) as last_updated,
    
    -- Time-based metrics (MySQL compatible)
    SUM(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 ELSE 0 END) as articles_last_hour,
    SUM(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as articles_last_24h,
    SUM(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as articles_last_week,
    
    -- Confidence categories
    SUM(CASE WHEN tr.confidence >= 0.8 THEN 1 ELSE 0 END) as high_confidence_articles,
    SUM(CASE WHEN tr.confidence >= 0.5 AND tr.confidence < 0.8 THEN 1 ELSE 0 END) as medium_confidence_articles,
    SUM(CASE WHEN tr.confidence < 0.5 THEN 1 ELSE 0 END) as low_confidence_articles,
    
    -- Processing performance metrics
    AVG(tr.processing_duration_ms) as avg_classification_time_ms,
    AVG(pa.preprocessing_duration_ms) as avg_preprocessing_time_ms
    
FROM topics t
LEFT JOIN topic_results tr ON t.topic_id = tr.topic_id
LEFT JOIN raw_articles ra ON tr.article_id = ra.article_id
LEFT JOIN preprocessed_articles pa ON tr.preprocessed_id = pa.preprocessed_id
GROUP BY t.topic_id, t.topic_label, t.topic_keywords;

-- Key performance indicators (MySQL compatible)
CREATE OR REPLACE VIEW vw_kpi_metrics AS
SELECT 
    COUNT(DISTINCT ra.article_id) as total_articles,
    COUNT(DISTINCT CASE WHEN tr.confidence > 0.7 THEN ra.article_id END) as high_confidence_articles,
    COUNT(DISTINCT t.topic_id) as active_topics,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    AVG(tr.confidence) as overall_avg_confidence,
    CAST(COUNT(DISTINCT CASE WHEN tr.confidence > 0.3 THEN ra.article_id END) * 100.0 / COUNT(DISTINCT ra.article_id) AS DECIMAL(5,2)) as classification_rate,
    MAX(ra.published_at) as latest_article_time,
    MAX(tr.classification_timestamp) as latest_classification_time,
    
    -- Pipeline health metrics
    COUNT(DISTINCT CASE WHEN ra.processing_status = 'pending' THEN ra.article_id END) as pending_articles,
    COUNT(DISTINCT CASE WHEN ra.processing_status = 'processed' THEN ra.article_id END) as processed_articles,
    COUNT(DISTINCT CASE WHEN ra.processing_status = 'failed' THEN ra.article_id END) as failed_articles,
    
    -- Processing performance
    AVG(pa.preprocessing_duration_ms) as avg_preprocessing_time_ms,
    AVG(tr.processing_duration_ms) as avg_classification_time_ms
    
FROM raw_articles ra
LEFT JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
LEFT JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
LEFT JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 30 DAY); -- Last 30 days

-- =====================================================
-- 2. TREND ANALYSIS VIEWS
-- =====================================================

-- Hourly trends for real-time analysis (MySQL compatible)
CREATE OR REPLACE VIEW vw_hourly_trends AS
SELECT 
    DATE_FORMAT(tr.classification_timestamp, '%Y-%m-%d %H:00:00') as hour_bucket,
    t.topic_id,
    t.topic_label,
    COUNT(tr.result_id) as article_count,
    AVG(tr.confidence) as avg_confidence,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    
    -- Time intelligence
    HOUR(tr.classification_timestamp) as hour_of_day,
    DAYNAME(tr.classification_timestamp) as day_of_week,
    CASE 
        WHEN DAYOFWEEK(tr.classification_timestamp) IN (1, 7) THEN 'Weekend'
        ELSE 'Weekday'
    END as day_type,
    
    -- Performance categories
    CASE 
        WHEN COUNT(tr.result_id) >= 50 THEN 'Peak'
        WHEN COUNT(tr.result_id) >= 20 THEN 'High'
        WHEN COUNT(tr.result_id) >= 5 THEN 'Normal'
        ELSE 'Low'
    END as activity_level
    
FROM topic_results tr
INNER JOIN topics t ON tr.topic_id = t.topic_id
INNER JOIN raw_articles ra ON tr.article_id = ra.article_id
WHERE tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)  -- Last 7 days
GROUP BY 
    DATE_FORMAT(tr.classification_timestamp, '%Y-%m-%d %H:00:00'),
    t.topic_id, 
    t.topic_label,
    HOUR(tr.classification_timestamp),
    DAYNAME(tr.classification_timestamp),
    CASE WHEN DAYOFWEEK(tr.classification_timestamp) IN (1, 7) THEN 'Weekend' ELSE 'Weekday' END
ORDER BY hour_bucket DESC, article_count DESC;

-- Daily trends for historical analysis
CREATE OR REPLACE VIEW vw_daily_trends AS
SELECT 
    DATE(tr.classification_timestamp) as date_key,
    t.topic_id,
    t.topic_label,
    COUNT(tr.result_id) as article_count,
    AVG(tr.confidence) as avg_confidence,
    STDDEV(tr.confidence) as confidence_std,
    MIN(tr.confidence) as min_confidence,
    MAX(tr.confidence) as max_confidence,
    COUNT(DISTINCT ra.source_name) as unique_sources,
    
    -- Calculate percentage of day
    CAST(COUNT(tr.result_id) * 100.0 / SUM(COUNT(tr.result_id)) OVER (PARTITION BY DATE(tr.classification_timestamp)) AS DECIMAL(5,2)) as percentage_of_day
    
FROM topic_results tr
INNER JOIN topics t ON tr.topic_id = t.topic_id
INNER JOIN raw_articles ra ON tr.article_id = ra.article_id
WHERE tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)  -- Last 30 days
GROUP BY 
    DATE(tr.classification_timestamp),
    t.topic_id, 
    t.topic_label
ORDER BY date_key DESC, article_count DESC;

-- =====================================================
-- 3. REAL-TIME ARTICLE VIEWS
-- =====================================================

-- Latest articles with topic classifications
CREATE OR REPLACE VIEW vw_realtime_articles AS
SELECT 
    ra.article_id,
    ra.title,
    ra.description,
    ra.source_name,
    ra.author,
    ra.url,
    ra.published_at,
    ra.fetched_at,
    t.topic_id,
    t.topic_label,
    tr.confidence,
    tr.classification_timestamp,
    pa.word_count,
    pa.preprocessing_duration_ms,
    tr.processing_duration_ms as classification_duration_ms,
    
    -- Freshness indicators
    CASE 
        WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'Very Fresh'
        WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 6 HOUR) THEN 'Fresh'
        WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 'Recent'
        ELSE 'Older'
    END as freshness,
    
    -- Confidence categories
    CASE 
        WHEN tr.confidence >= 0.8 THEN 'High'
        WHEN tr.confidence >= 0.5 THEN 'Medium'
        ELSE 'Low'
    END as confidence_category
    
FROM raw_articles ra
INNER JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
INNER JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
INNER JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  -- Last 24 hours
ORDER BY ra.published_at DESC
LIMIT 1000;  -- Limit for performance

-- =====================================================
-- 4. SOURCE ANALYSIS VIEWS
-- =====================================================

-- Source performance and reliability
CREATE OR REPLACE VIEW vw_source_analysis AS
SELECT 
    ra.source_name,
    COUNT(DISTINCT ra.article_id) as total_articles,
    AVG(tr.confidence) as avg_confidence,
    COUNT(DISTINCT t.topic_id) as topic_diversity,
    COUNT(DISTINCT DATE(ra.published_at)) as active_days,
    
    -- Time-based metrics
    SUM(CASE WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as articles_last_24h,
    SUM(CASE WHEN ra.published_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as articles_last_week,
    
    -- Quality metrics
    AVG(pa.word_count) as avg_word_count,
    SUM(CASE WHEN tr.confidence >= 0.7 THEN 1 ELSE 0 END) as high_quality_articles,
    
    -- Performance metrics
    AVG(pa.preprocessing_duration_ms) as avg_preprocessing_time,
    AVG(tr.processing_duration_ms) as avg_classification_time,
    
    MIN(ra.published_at) as first_article,
    MAX(ra.published_at) as latest_article
    
FROM raw_articles ra
INNER JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
INNER JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
INNER JOIN topics t ON tr.topic_id = t.topic_id
WHERE ra.published_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)  -- Last 30 days
GROUP BY ra.source_name
ORDER BY total_articles DESC;

-- =====================================================
-- 5. PIPELINE MONITORING VIEWS
-- =====================================================

-- Pipeline health dashboard
CREATE OR REPLACE VIEW vw_pipeline_health AS
SELECT 
    -- Processing status summary
    COUNT(CASE WHEN ra.processing_status = 'pending' THEN 1 END) as pending_articles,
    COUNT(CASE WHEN ra.processing_status = 'processed' THEN 1 END) as processed_articles,
    COUNT(CASE WHEN ra.processing_status = 'failed' THEN 1 END) as failed_articles,
    
    -- Recent activity
    COUNT(CASE WHEN ra.fetched_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as fetched_last_hour,
    COUNT(CASE WHEN pa.preprocessing_timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as processed_last_hour,
    COUNT(CASE WHEN tr.classification_timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as classified_last_hour,
    
    -- Performance metrics
    AVG(pa.preprocessing_duration_ms) as avg_preprocessing_time_ms,
    AVG(tr.processing_duration_ms) as avg_classification_time_ms,
    
    -- Data freshness
    MAX(ra.fetched_at) as last_fetch_time,
    MAX(pa.preprocessing_timestamp) as last_preprocessing_time,
    MAX(tr.classification_timestamp) as last_classification_time,
    
    -- Overall health score (0-100)
    CASE 
        WHEN COUNT(CASE WHEN ra.processing_status = 'failed' THEN 1 END) = 0 
             AND MAX(ra.fetched_at) >= DATE_SUB(NOW(), INTERVAL 2 HOUR) THEN 100
        WHEN COUNT(CASE WHEN ra.processing_status = 'failed' THEN 1 END) / COUNT(*) < 0.1 
             AND MAX(ra.fetched_at) >= DATE_SUB(NOW(), INTERVAL 6 HOUR) THEN 80
        WHEN COUNT(CASE WHEN ra.processing_status = 'failed' THEN 1 END) / COUNT(*) < 0.2 THEN 60
        ELSE 40
    END as health_score
    
FROM raw_articles ra
LEFT JOIN preprocessed_articles pa ON ra.article_id = pa.article_id
LEFT JOIN topic_results tr ON pa.preprocessed_id = tr.preprocessed_id
WHERE ra.fetched_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);  -- Last 24 hours

-- =====================================================
-- 6. VERIFICATION QUERIES
-- =====================================================

-- Test all views
SELECT 'vw_topic_summary' as view_name, COUNT(*) as row_count FROM vw_topic_summary
UNION ALL
SELECT 'vw_kpi_metrics' as view_name, COUNT(*) as row_count FROM vw_kpi_metrics
UNION ALL
SELECT 'vw_hourly_trends' as view_name, COUNT(*) as row_count FROM vw_hourly_trends
UNION ALL
SELECT 'vw_daily_trends' as view_name, COUNT(*) as row_count FROM vw_daily_trends
UNION ALL
SELECT 'vw_realtime_articles' as view_name, COUNT(*) as row_count FROM vw_realtime_articles
UNION ALL
SELECT 'vw_source_analysis' as view_name, COUNT(*) as row_count FROM vw_source_analysis
UNION ALL
SELECT 'vw_pipeline_health' as view_name, COUNT(*) as row_count FROM vw_pipeline_health;
