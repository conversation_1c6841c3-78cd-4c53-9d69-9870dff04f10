# Installation Guide

Complete installation guide for the Real-Time Topic Modeling and Trend Analysis Pipeline.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8 or higher
- **Memory**: 4 GB RAM
- **Storage**: 5 GB free disk space
- **Internet**: Stable internet connection for API access

### Recommended Requirements
- **Python**: 3.9 or 3.10
- **Memory**: 8 GB RAM or more
- **Storage**: 20 GB free disk space (SSD preferred)
- **CPU**: Multi-core processor for better performance

## Quick Installation

### Option 1: Automated Setup (Recommended)

```bash
# 1. Clone or download the project
git clone <repository-url>
cd topic_modelling

# 2. Run the automated setup
python setup.py

# 3. Configure your API key
cp .env.example .env
# Edit .env file with your NewsAPI key

# 4. Test the installation
python run_pipeline.py --step ingestion
```

### Option 2: Manual Installation

```bash
# 1. Install Python dependencies
pip install -r requirements.txt

# 2. Download NLTK data
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"

# 3. Install spaCy model
python -m spacy download en_core_web_sm

# 4. Configure environment
cp .env.example .env
# Edit .env with your API keys

# 5. Test installation
python -c "from src.config import config; print('Installation successful!')"
```

## Detailed Installation Steps

### Step 1: Python Installation

#### Windows
1. Download Python from [python.org](https://www.python.org/downloads/)
2. Run the installer and check "Add Python to PATH"
3. Verify installation:
   ```cmd
   python --version
   pip --version
   ```

#### macOS
```bash
# Using Homebrew (recommended)
brew install python

# Or download from python.org
# Verify installation
python3 --version
pip3 --version
```

#### Linux (Ubuntu/Debian)
```bash
# Update package list
sudo apt update

# Install Python and pip
sudo apt install python3 python3-pip python3-venv

# Verify installation
python3 --version
pip3 --version
```

### Step 2: Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv topic_modeling_env

# Activate virtual environment
# Windows:
topic_modeling_env\Scripts\activate
# macOS/Linux:
source topic_modeling_env/bin/activate

# Verify activation (should show virtual environment name)
which python
```

### Step 3: Download Project

#### Option A: Git Clone
```bash
git clone <repository-url>
cd topic_modelling
```

#### Option B: Download ZIP
1. Download the project ZIP file
2. Extract to your desired location
3. Navigate to the project directory

### Step 4: Install Dependencies

```bash
# Install Python packages
pip install -r requirements.txt

# Verify installation
pip list
```

### Step 5: Install NLP Models

```bash
# Download NLTK data
python -c "
import nltk
nltk.download('punkt')
nltk.download('stopwords')
nltk.download('wordnet')
nltk.download('averaged_perceptron_tagger')
nltk.download('omw-1.4')
print('NLTK data downloaded successfully')
"

# Install spaCy English model
python -m spacy download en_core_web_sm

# Verify spaCy installation
python -c "import spacy; nlp = spacy.load('en_core_web_sm'); print('spaCy model loaded successfully')"
```

### Step 6: Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
# Windows: notepad .env
# macOS: open .env
# Linux: nano .env
```

**Required Configuration:**
```bash
# Get your free API key from https://newsapi.org/
NEWS_API_KEY=your_actual_api_key_here
```

### Step 7: Verify Installation

```bash
# Test configuration loading
python -c "from src.config import config; print('✅ Configuration loaded successfully')"

# Test data ingestion (requires API key)
python run_pipeline.py --step ingestion

# Run full pipeline test
python run_pipeline.py
```

## API Key Setup

### NewsAPI (Required)

1. **Sign up** at [https://newsapi.org/](https://newsapi.org/)
2. **Get your API key** from the dashboard
3. **Add to .env file**:
   ```bash
   NEWS_API_KEY=your_api_key_here
   ```

**Free Tier Limits:**
- 1,000 requests per day
- 100 articles per request
- Development use only

### Alternative Sources (Optional)

If you need more data or hit API limits:

1. **GNews API**: [https://gnews.io/](https://gnews.io/)
   ```bash
   GNEWS_API_KEY=your_gnews_key_here
   ```

2. **RSS Feeds**: No API key required, configured in `config.yaml`

## Troubleshooting Installation

### Common Issues

#### 1. Python Not Found
```bash
# Windows: Add Python to PATH
# Check "Add Python to PATH" during installation
# Or manually add: C:\Python39\Scripts\;C:\Python39\

# macOS/Linux: Use python3
python3 --version
pip3 install -r requirements.txt
```

#### 2. Permission Errors
```bash
# Windows: Run as Administrator
# macOS/Linux: Use --user flag
pip install --user -r requirements.txt
```

#### 3. SSL Certificate Errors
```bash
# Update certificates
pip install --upgrade certifi

# Or use trusted hosts
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
```

#### 4. spaCy Model Download Fails
```bash
# Alternative download method
pip install https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.4.1/en_core_web_sm-3.4.1.tar.gz

# Or use conda
conda install -c conda-forge spacy-model-en_core_web_sm
```

#### 5. NLTK Download Issues
```bash
# Manual download
python -c "
import nltk
import ssl
try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context
nltk.download('punkt')
"
```

#### 6. Memory Issues
```bash
# Reduce memory usage in config.yaml
topic_modeling:
  max_features: 500  # Reduce from 1000
  num_topics: 5      # Reduce from 8
```

### Platform-Specific Issues

#### Windows
- **Long Path Issues**: Enable long paths in Windows 10
- **Antivirus**: Exclude project folder from real-time scanning
- **PowerShell**: Use `python` instead of `py`

#### macOS
- **Xcode Tools**: Install with `xcode-select --install`
- **Homebrew**: Install from [brew.sh](https://brew.sh/)
- **Python Version**: Use `python3` and `pip3`

#### Linux
- **Build Tools**: Install with `sudo apt install build-essential`
- **Python Headers**: Install with `sudo apt install python3-dev`
- **Virtual Environment**: Install with `sudo apt install python3-venv`

## Verification Checklist

After installation, verify these components:

- [ ] Python 3.8+ installed
- [ ] All pip packages installed successfully
- [ ] NLTK data downloaded
- [ ] spaCy model installed
- [ ] Configuration file created
- [ ] API key configured
- [ ] Test pipeline runs successfully

## Performance Optimization

### For Better Performance

```bash
# Install optional performance packages
pip install numba  # Faster numerical computations
pip install joblib  # Better parallel processing

# Use SSD storage for data directory
# Increase system RAM if processing large datasets
# Use multiple CPU cores
```

### Memory Optimization

```yaml
# Edit config.yaml for lower memory usage
topic_modeling:
  max_features: 500
  num_topics: 5
  batch_size: 64

preprocessing:
  max_word_length: 15
```

## Development Setup

For development and testing:

```bash
# Install development dependencies
pip install pytest black flake8 jupyter

# Install in development mode
pip install -e .

# Run tests
python run_tests.py

# Format code
black src/

# Check code quality
flake8 src/
```

## Docker Installation (Alternative)

If you prefer Docker:

```bash
# Build Docker image
docker build -t topic-modeling .

# Run container
docker run -v $(pwd)/data:/app/data -v $(pwd)/.env:/app/.env topic-modeling

# Or use docker-compose
docker-compose up
```

## Next Steps

After successful installation:

1. **Configure** your settings in `config.yaml`
2. **Test** with a small dataset: `python run_pipeline.py --step ingestion`
3. **Run** the full pipeline: `python run_pipeline.py`
4. **Set up** Power BI integration (see `docs/POWERBI_INTEGRATION.md`)
5. **Schedule** automated runs (see `docs/SCHEDULING.md`)
6. **Deploy** to production (see `docs/DEPLOYMENT.md`)

## Getting Help

If you encounter issues:

1. **Check** the troubleshooting section above
2. **Review** log files in the `logs/` directory
3. **Run** tests to identify specific issues: `python run_tests.py`
4. **Check** system requirements and dependencies
5. **Consult** the documentation in the `docs/` folder

## Support

For additional support:
- Check the project documentation
- Review the configuration guide
- Run the test suite for diagnostics
- Check system requirements and compatibility
