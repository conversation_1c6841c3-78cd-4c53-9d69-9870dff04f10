"""
Hourly news data collection service for real-time trend analysis.
Collects news data every hour and feeds to LDA model for classification.
"""

import pandas as pd
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Dict, Any, List

from .data_ingestion import NewsAPICollector, RSSCollector
from .config import config
from .utils import save_dataframe

logger = logging.getLogger(__name__)

class HourlyNewsCollector:
    """Hourly news collection service for real-time processing."""
    
    def __init__(self):
        """Initialize hourly collector."""
        self.news_collector = NewsAPICollector()
        self.rss_collector = RSSCollector()
        self.data_dir = Path(config.get('output.data_dir', 'data'))
        self.hourly_dir = self.data_dir / 'hourly'
        self.hourly_dir.mkdir(exist_ok=True)
        
        # Configure for hourly collection
        self.hourly_config = {
            'page_size': 50,  # Smaller batches for hourly
            'max_pages': 2,   # Limit for frequent collection
            'hours_back': 2   # Look back 2 hours for new articles
        }
    
    def collect_hourly_data(self) -> pd.DataFrame:
        """
        Collect news data for the current hour.
        
        Returns:
            DataFrame with collected articles
        """
        current_time = datetime.now()
        logger.info(f"Starting hourly collection at {current_time}")
        
        # Calculate time window (last 2 hours)
        from_time = current_time - timedelta(hours=self.hourly_config['hours_back'])
        from_date = from_time.strftime('%Y-%m-%d')
        
        articles = []
        
        # Collect from NewsAPI
        try:
            newsapi_articles = self.news_collector.fetch_articles(
                page_size=self.hourly_config['page_size'],
                max_pages=self.hourly_config['max_pages'],
                from_date=from_date
            )
            
            # Filter for recent articles (last 2 hours)
            recent_articles = []
            for article in newsapi_articles:
                pub_time = pd.to_datetime(article.get('publishedAt'))
                if pub_time >= from_time:
                    recent_articles.append(article)
            
            articles.extend(recent_articles)
            logger.info(f"Collected {len(recent_articles)} recent articles from NewsAPI")
            
        except Exception as e:
            logger.error(f"Error collecting from NewsAPI: {e}")
        
        # Fallback to RSS if needed
        if len(articles) < 5:  # If we got very few articles
            try:
                rss_articles = self.rss_collector.fetch_articles(max_articles_per_feed=20)
                articles.extend(rss_articles[:10])  # Add up to 10 RSS articles
                logger.info(f"Added {len(rss_articles[:10])} articles from RSS feeds")
            except Exception as e:
                logger.error(f"Error collecting from RSS: {e}")
        
        if not articles:
            logger.warning("No articles collected in this hour")
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(articles)
        df = self._clean_hourly_data(df)
        
        # Add collection metadata
        df['collection_hour'] = current_time.strftime('%Y-%m-%d_%H')
        df['collection_timestamp'] = current_time
        
        logger.info(f"Hourly collection completed: {len(df)} articles")
        return df
    
    def _clean_hourly_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and prepare hourly data."""
        if df.empty:
            return df
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['title', 'url'], keep='first')
        
        # Remove articles with missing essential fields
        df = df.dropna(subset=['title'])
        
        # Clean text fields
        text_columns = ['title', 'description', 'content']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
                df[col] = df[col].replace('nan', '')
        
        # Standardize date format
        if 'publishedAt' in df.columns:
            df['publishedAt'] = pd.to_datetime(df['publishedAt'], errors='coerce')
        
        # Extract source name
        if 'source' in df.columns:
            df['source_name'] = df['source'].apply(
                lambda x: x.get('name', '') if isinstance(x, dict) else str(x)
            )
        
        return df
    
    def save_hourly_data(self, df: pd.DataFrame) -> str:
        """
        Save hourly data to CSV for LDA processing.
        
        Args:
            df: DataFrame with hourly articles
            
        Returns:
            Path to saved file
        """
        if df.empty:
            return ""
        
        # Create filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M')
        filename = f"hourly_news_{timestamp}.csv"
        file_path = self.hourly_dir / filename
        
        # Save to CSV
        df.to_csv(file_path, index=False, encoding='utf-8')
        
        # Also update the latest hourly file for LDA processing
        latest_file = self.data_dir / 'latest_hourly.csv'
        df.to_csv(latest_file, index=False, encoding='utf-8')
        
        logger.info(f"Hourly data saved: {file_path}")
        logger.info(f"Latest file updated: {latest_file}")
        
        return str(file_path)
    
    def run_hourly_collection(self):
        """Run a single hourly collection cycle."""
        try:
            # Collect data
            df = self.collect_hourly_data()
            
            if not df.empty:
                # Save data
                file_path = self.save_hourly_data(df)
                
                # Trigger LDA processing (this will be called by the backend)
                self._trigger_lda_processing(file_path)
                
                return {
                    'success': True,
                    'articles_collected': len(df),
                    'file_path': file_path,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': True,
                    'articles_collected': 0,
                    'message': 'No new articles found',
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Hourly collection failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _trigger_lda_processing(self, csv_file_path: str):
        """
        Trigger LDA model processing for new data.
        This will be called by the backend service.
        """
        # Create a signal file for the backend to pick up
        signal_file = self.data_dir / 'new_data_signal.txt'
        with open(signal_file, 'w') as f:
            f.write(f"{csv_file_path}\n{datetime.now().isoformat()}")
        
        logger.info(f"LDA processing signal created: {signal_file}")
    
    def start_hourly_service(self):
        """Start the hourly collection service."""
        logger.info("Starting hourly news collection service...")
        
        # Schedule hourly collection
        schedule.every().hour.at(":05").do(self.run_hourly_collection)  # 5 minutes past each hour
        
        # Also run immediately for testing
        logger.info("Running initial collection...")
        result = self.run_hourly_collection()
        logger.info(f"Initial collection result: {result}")
        
        # Keep the service running
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

class HourlyDataManager:
    """Manage hourly data files and cleanup."""
    
    def __init__(self):
        self.data_dir = Path(config.get('output.data_dir', 'data'))
        self.hourly_dir = self.data_dir / 'hourly'
    
    def cleanup_old_files(self, days_to_keep: int = 7):
        """Clean up hourly files older than specified days."""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        cleaned_count = 0
        for file_path in self.hourly_dir.glob('hourly_news_*.csv'):
            if file_path.stat().st_mtime < cutoff_date.timestamp():
                file_path.unlink()
                cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} old hourly files")
        return cleaned_count
    
    def get_hourly_summary(self) -> Dict[str, Any]:
        """Get summary of hourly data collection."""
        hourly_files = list(self.hourly_dir.glob('hourly_news_*.csv'))
        
        if not hourly_files:
            return {'total_files': 0, 'total_articles': 0}
        
        total_articles = 0
        latest_file = None
        latest_time = None
        
        for file_path in hourly_files:
            try:
                df = pd.read_csv(file_path)
                total_articles += len(df)
                
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if latest_time is None or file_time > latest_time:
                    latest_time = file_time
                    latest_file = file_path.name
                    
            except Exception as e:
                logger.warning(f"Error reading {file_path}: {e}")
        
        return {
            'total_files': len(hourly_files),
            'total_articles': total_articles,
            'latest_file': latest_file,
            'latest_collection': latest_time.isoformat() if latest_time else None
        }

def main():
    """Main function to run hourly collection service."""
    collector = HourlyNewsCollector()
    
    try:
        collector.start_hourly_service()
    except KeyboardInterrupt:
        logger.info("Hourly collection service stopped by user")
    except Exception as e:
        logger.error(f"Hourly collection service failed: {e}")

if __name__ == "__main__":
    main()
