#!/usr/bin/env python3
"""
Advanced Model Drift Detection and Retraining System
Monitors LDA model performance and triggers retraining when needed
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from scipy.stats import entropy, ks_2samp
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import pickle
import json
import warnings

logger = logging.getLogger(__name__)

class ModelDriftDetector:
    """
    Comprehensive model drift detection system for LDA topic modeling.
    """
    
    def __init__(self, 
                 confidence_threshold: float = 0.05,
                 topic_shift_threshold: float = 0.15,
                 vocabulary_drift_threshold: float = 0.20,
                 min_samples_for_detection: int = 100):
        """
        Initialize drift detector with configurable thresholds.
        
        Args:
            confidence_threshold: Threshold for confidence score drift
            topic_shift_threshold: Threshold for topic distribution changes
            vocabulary_drift_threshold: Threshold for vocabulary changes
            min_samples_for_detection: Minimum samples needed for drift detection
        """
        self.confidence_threshold = confidence_threshold
        self.topic_shift_threshold = topic_shift_threshold
        self.vocabulary_drift_threshold = vocabulary_drift_threshold
        self.min_samples = min_samples_for_detection
        
        # Historical baselines
        self.baseline_confidence_dist = None
        self.baseline_topic_dist = None
        self.baseline_vocabulary = None
        self.baseline_topic_coherence = None
        
        # Drift detection results
        self.drift_history = []
        
    def establish_baseline(self, 
                          articles_df: pd.DataFrame,
                          classifications_df: pd.DataFrame,
                          model_path: str) -> Dict:
        """
        Establish baseline metrics from historical data.
        
        Args:
            articles_df: Historical articles data
            classifications_df: Historical classifications
            model_path: Path to current LDA model
            
        Returns:
            Dictionary with baseline metrics
        """
        
        logger.info("🔍 Establishing model performance baseline...")
        
        try:
            # Load current model
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            model = model_data['model']
            vectorizer = model_data['vectorizer']
            
            # 1. Confidence distribution baseline
            self.baseline_confidence_dist = self._calculate_confidence_distribution(
                classifications_df['confidence']
            )
            
            # 2. Topic distribution baseline
            self.baseline_topic_dist = self._calculate_topic_distribution(
                classifications_df['topic_id']
            )
            
            # 3. Vocabulary baseline
            self.baseline_vocabulary = self._extract_vocabulary_features(
                articles_df['content'].tolist(), vectorizer
            )
            
            # 4. Topic coherence baseline
            self.baseline_topic_coherence = self._calculate_topic_coherence(
                model, vectorizer, articles_df['content'].tolist()
            )
            
            baseline_metrics = {
                'timestamp': datetime.now().isoformat(),
                'sample_size': len(articles_df),
                'avg_confidence': float(classifications_df['confidence'].mean()),
                'confidence_std': float(classifications_df['confidence'].std()),
                'topic_entropy': float(entropy(self.baseline_topic_dist)),
                'vocabulary_size': len(self.baseline_vocabulary),
                'avg_topic_coherence': float(np.mean(self.baseline_topic_coherence))
            }
            
            logger.info(f"✅ Baseline established: {baseline_metrics}")
            return baseline_metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to establish baseline: {e}")
            raise
    
    def detect_drift(self, 
                    recent_articles_df: pd.DataFrame,
                    recent_classifications_df: pd.DataFrame,
                    model_path: str) -> Dict:
        """
        Detect various types of model drift.
        
        Args:
            recent_articles_df: Recent articles data
            recent_classifications_df: Recent classifications
            model_path: Path to current LDA model
            
        Returns:
            Comprehensive drift detection results
        """
        
        if len(recent_articles_df) < self.min_samples:
            logger.warning(f"⚠️ Insufficient samples for drift detection: {len(recent_articles_df)}")
            return {'drift_detected': False, 'reason': 'insufficient_samples'}
        
        logger.info(f"🔍 Detecting drift on {len(recent_articles_df)} recent samples...")
        
        drift_results = {
            'timestamp': datetime.now().isoformat(),
            'sample_size': len(recent_articles_df),
            'drift_detected': False,
            'drift_types': [],
            'drift_scores': {},
            'recommendations': []
        }
        
        try:
            # Load current model
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            model = model_data['model']
            vectorizer = model_data['vectorizer']
            
            # 1. Confidence Drift Detection
            confidence_drift = self._detect_confidence_drift(
                recent_classifications_df['confidence']
            )
            drift_results['drift_scores']['confidence_drift'] = confidence_drift
            
            if confidence_drift['drift_detected']:
                drift_results['drift_detected'] = True
                drift_results['drift_types'].append('confidence_drift')
                drift_results['recommendations'].append(
                    f"Confidence drift detected: {confidence_drift['description']}"
                )
            
            # 2. Topic Distribution Drift
            topic_drift = self._detect_topic_distribution_drift(
                recent_classifications_df['topic_id']
            )
            drift_results['drift_scores']['topic_drift'] = topic_drift
            
            if topic_drift['drift_detected']:
                drift_results['drift_detected'] = True
                drift_results['drift_types'].append('topic_drift')
                drift_results['recommendations'].append(
                    f"Topic distribution drift detected: {topic_drift['description']}"
                )
            
            # 3. Vocabulary Drift Detection
            vocabulary_drift = self._detect_vocabulary_drift(
                recent_articles_df['content'].tolist(), vectorizer
            )
            drift_results['drift_scores']['vocabulary_drift'] = vocabulary_drift
            
            if vocabulary_drift['drift_detected']:
                drift_results['drift_detected'] = True
                drift_results['drift_types'].append('vocabulary_drift')
                drift_results['recommendations'].append(
                    f"Vocabulary drift detected: {vocabulary_drift['description']}"
                )
            
            # 4. Topic Coherence Drift
            coherence_drift = self._detect_coherence_drift(
                model, vectorizer, recent_articles_df['content'].tolist()
            )
            drift_results['drift_scores']['coherence_drift'] = coherence_drift
            
            if coherence_drift['drift_detected']:
                drift_results['drift_detected'] = True
                drift_results['drift_types'].append('coherence_drift')
                drift_results['recommendations'].append(
                    f"Topic coherence drift detected: {coherence_drift['description']}"
                )
            
            # 5. Performance Degradation Detection
            performance_drift = self._detect_performance_degradation(
                recent_classifications_df
            )
            drift_results['drift_scores']['performance_drift'] = performance_drift
            
            if performance_drift['drift_detected']:
                drift_results['drift_detected'] = True
                drift_results['drift_types'].append('performance_drift')
                drift_results['recommendations'].append(
                    f"Performance degradation detected: {performance_drift['description']}"
                )
            
            # Store drift history
            self.drift_history.append(drift_results)
            
            # Generate overall recommendation
            if drift_results['drift_detected']:
                severity = self._calculate_drift_severity(drift_results['drift_scores'])
                drift_results['severity'] = severity
                
                if severity >= 0.7:
                    drift_results['recommendations'].append("🚨 URGENT: Immediate model retraining recommended")
                elif severity >= 0.5:
                    drift_results['recommendations'].append("⚠️ WARNING: Schedule model retraining soon")
                else:
                    drift_results['recommendations'].append("ℹ️ INFO: Monitor closely, consider retraining")
            
            logger.info(f"🔍 Drift detection complete: {drift_results['drift_detected']}")
            return drift_results
            
        except Exception as e:
            logger.error(f"❌ Drift detection failed: {e}")
            drift_results['error'] = str(e)
            return drift_results
    
    def _detect_confidence_drift(self, recent_confidences: pd.Series) -> Dict:
        """Detect drift in confidence score distribution."""
        
        if self.baseline_confidence_dist is None:
            return {'drift_detected': False, 'reason': 'no_baseline'}
        
        recent_dist = self._calculate_confidence_distribution(recent_confidences)
        
        # KS test for distribution change
        ks_stat, p_value = ks_2samp(self.baseline_confidence_dist, recent_dist)
        
        # Mean shift detection
        baseline_mean = np.mean(self.baseline_confidence_dist)
        recent_mean = np.mean(recent_dist)
        mean_shift = abs(recent_mean - baseline_mean)
        
        drift_detected = (p_value < 0.05) or (mean_shift > self.confidence_threshold)
        
        return {
            'drift_detected': drift_detected,
            'ks_statistic': float(ks_stat),
            'p_value': float(p_value),
            'mean_shift': float(mean_shift),
            'baseline_mean': float(baseline_mean),
            'recent_mean': float(recent_mean),
            'description': f"Confidence mean shifted by {mean_shift:.3f}" if drift_detected else "No significant drift"
        }
    
    def _detect_topic_distribution_drift(self, recent_topic_ids: pd.Series) -> Dict:
        """Detect drift in topic distribution."""
        
        if self.baseline_topic_dist is None:
            return {'drift_detected': False, 'reason': 'no_baseline'}
        
        recent_dist = self._calculate_topic_distribution(recent_topic_ids)
        
        # Ensure same length for comparison
        max_topics = max(len(self.baseline_topic_dist), len(recent_dist))
        baseline_padded = np.pad(self.baseline_topic_dist, (0, max_topics - len(self.baseline_topic_dist)))
        recent_padded = np.pad(recent_dist, (0, max_topics - len(recent_dist)))
        
        # Jensen-Shannon divergence
        js_divergence = self._jensen_shannon_divergence(baseline_padded, recent_padded)
        
        drift_detected = js_divergence > self.topic_shift_threshold
        
        return {
            'drift_detected': drift_detected,
            'js_divergence': float(js_divergence),
            'threshold': self.topic_shift_threshold,
            'description': f"Topic distribution JS divergence: {js_divergence:.3f}" if drift_detected else "No significant drift"
        }
    
    def _detect_vocabulary_drift(self, recent_texts: List[str], vectorizer) -> Dict:
        """Detect drift in vocabulary usage."""
        
        if self.baseline_vocabulary is None:
            return {'drift_detected': False, 'reason': 'no_baseline'}
        
        recent_vocab = self._extract_vocabulary_features(recent_texts, vectorizer)
        
        # Calculate vocabulary overlap
        baseline_set = set(self.baseline_vocabulary.keys())
        recent_set = set(recent_vocab.keys())
        
        overlap = len(baseline_set.intersection(recent_set))
        union = len(baseline_set.union(recent_set))
        jaccard_similarity = overlap / union if union > 0 else 0
        
        vocabulary_drift_score = 1 - jaccard_similarity
        drift_detected = vocabulary_drift_score > self.vocabulary_drift_threshold
        
        return {
            'drift_detected': drift_detected,
            'vocabulary_drift_score': float(vocabulary_drift_score),
            'jaccard_similarity': float(jaccard_similarity),
            'new_terms_ratio': float(len(recent_set - baseline_set) / len(recent_set)) if recent_set else 0,
            'description': f"Vocabulary drift score: {vocabulary_drift_score:.3f}" if drift_detected else "No significant drift"
        }
    
    def _detect_coherence_drift(self, model, vectorizer, recent_texts: List[str]) -> Dict:
        """Detect drift in topic coherence."""
        
        if self.baseline_topic_coherence is None:
            return {'drift_detected': False, 'reason': 'no_baseline'}
        
        recent_coherence = self._calculate_topic_coherence(model, vectorizer, recent_texts)
        
        baseline_avg = np.mean(self.baseline_topic_coherence)
        recent_avg = np.mean(recent_coherence)
        
        coherence_drop = baseline_avg - recent_avg
        drift_detected = coherence_drop > 0.1  # 10% drop threshold
        
        return {
            'drift_detected': drift_detected,
            'baseline_coherence': float(baseline_avg),
            'recent_coherence': float(recent_avg),
            'coherence_drop': float(coherence_drop),
            'description': f"Topic coherence dropped by {coherence_drop:.3f}" if drift_detected else "No significant drift"
        }
    
    def _detect_performance_degradation(self, recent_classifications_df: pd.DataFrame) -> Dict:
        """Detect overall performance degradation."""
        
        # Calculate performance metrics
        low_confidence_ratio = (recent_classifications_df['confidence'] < 0.3).mean()
        unclassified_ratio = (recent_classifications_df['topic_id'] == -1).mean()
        avg_confidence = recent_classifications_df['confidence'].mean()
        
        # Performance degradation thresholds
        performance_issues = []
        
        if low_confidence_ratio > 0.5:
            performance_issues.append(f"High low-confidence ratio: {low_confidence_ratio:.2f}")
        
        if unclassified_ratio > 0.2:
            performance_issues.append(f"High unclassified ratio: {unclassified_ratio:.2f}")
        
        if avg_confidence < 0.4:
            performance_issues.append(f"Low average confidence: {avg_confidence:.3f}")
        
        drift_detected = len(performance_issues) > 0
        
        return {
            'drift_detected': drift_detected,
            'low_confidence_ratio': float(low_confidence_ratio),
            'unclassified_ratio': float(unclassified_ratio),
            'avg_confidence': float(avg_confidence),
            'issues': performance_issues,
            'description': "; ".join(performance_issues) if performance_issues else "No performance issues"
        }
    
    def _calculate_confidence_distribution(self, confidences: pd.Series) -> np.ndarray:
        """Calculate confidence score distribution."""
        return np.histogram(confidences, bins=20, range=(0, 1))[0] / len(confidences)
    
    def _calculate_topic_distribution(self, topic_ids: pd.Series) -> np.ndarray:
        """Calculate topic distribution."""
        topic_counts = topic_ids.value_counts().sort_index()
        return topic_counts.values / topic_counts.sum()
    
    def _extract_vocabulary_features(self, texts: List[str], vectorizer) -> Dict:
        """Extract vocabulary features from texts."""
        try:
            # Transform texts and get feature names
            tfidf_matrix = vectorizer.transform(texts)
            feature_names = vectorizer.get_feature_names_out()
            
            # Calculate average TF-IDF scores
            avg_scores = np.array(tfidf_matrix.mean(axis=0)).flatten()
            
            # Return top features
            vocab_features = {}
            for i, feature in enumerate(feature_names):
                if avg_scores[i] > 0:
                    vocab_features[feature] = avg_scores[i]
            
            return vocab_features
            
        except Exception as e:
            logger.error(f"Vocabulary extraction failed: {e}")
            return {}
    
    def _calculate_topic_coherence(self, model, vectorizer, texts: List[str]) -> List[float]:
        """Calculate topic coherence scores."""
        try:
            # This is a simplified coherence calculation
            # In practice, you might want to use more sophisticated methods
            
            coherence_scores = []
            
            # Get topic-word distributions
            topic_word_dist = model.components_
            feature_names = vectorizer.get_feature_names_out()
            
            for topic_idx in range(len(topic_word_dist)):
                # Get top words for topic
                top_word_indices = topic_word_dist[topic_idx].argsort()[-10:][::-1]
                top_words = [feature_names[i] for i in top_word_indices]
                
                # Calculate simple coherence (word co-occurrence)
                coherence = self._calculate_word_coherence(top_words, texts)
                coherence_scores.append(coherence)
            
            return coherence_scores
            
        except Exception as e:
            logger.error(f"Coherence calculation failed: {e}")
            return [0.0] * model.n_components
    
    def _calculate_word_coherence(self, words: List[str], texts: List[str]) -> float:
        """Calculate coherence for a set of words."""
        # Simplified coherence calculation
        # Count co-occurrences of word pairs
        
        coherence_sum = 0
        pair_count = 0
        
        for i, word1 in enumerate(words[:-1]):
            for word2 in words[i+1:]:
                # Count documents containing both words
                both_count = sum(1 for text in texts if word1 in text.lower() and word2 in text.lower())
                word1_count = sum(1 for text in texts if word1 in text.lower())
                
                if word1_count > 0:
                    coherence_sum += both_count / word1_count
                    pair_count += 1
        
        return coherence_sum / pair_count if pair_count > 0 else 0.0
    
    def _jensen_shannon_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """Calculate Jensen-Shannon divergence between two distributions."""
        # Ensure no zeros for log calculation
        p = p + 1e-10
        q = q + 1e-10
        
        # Normalize
        p = p / np.sum(p)
        q = q / np.sum(q)
        
        # Calculate JS divergence
        m = 0.5 * (p + q)
        js_div = 0.5 * entropy(p, m) + 0.5 * entropy(q, m)
        
        return js_div
    
    def _calculate_drift_severity(self, drift_scores: Dict) -> float:
        """Calculate overall drift severity score."""
        
        severity_weights = {
            'confidence_drift': 0.3,
            'topic_drift': 0.3,
            'vocabulary_drift': 0.2,
            'coherence_drift': 0.1,
            'performance_drift': 0.1
        }
        
        total_severity = 0.0
        total_weight = 0.0
        
        for drift_type, weight in severity_weights.items():
            if drift_type in drift_scores and drift_scores[drift_type].get('drift_detected', False):
                # Calculate severity based on drift type
                if drift_type == 'confidence_drift':
                    severity = min(1.0, drift_scores[drift_type].get('mean_shift', 0) / self.confidence_threshold)
                elif drift_type == 'topic_drift':
                    severity = min(1.0, drift_scores[drift_type].get('js_divergence', 0) / self.topic_shift_threshold)
                elif drift_type == 'vocabulary_drift':
                    severity = min(1.0, drift_scores[drift_type].get('vocabulary_drift_score', 0) / self.vocabulary_drift_threshold)
                else:
                    severity = 0.5  # Default severity for other types
                
                total_severity += severity * weight
                total_weight += weight
        
        return total_severity / total_weight if total_weight > 0 else 0.0

class AutoRetrainingSystem:
    """
    Automated model retraining system triggered by drift detection.
    """
    
    def __init__(self, drift_detector: ModelDriftDetector):
        self.drift_detector = drift_detector
        self.retraining_history = []
    
    def should_retrain(self, drift_results: Dict) -> Tuple[bool, str]:
        """
        Determine if model should be retrained based on drift results.
        
        Returns:
            Tuple of (should_retrain, reason)
        """
        
        if not drift_results.get('drift_detected', False):
            return False, "No drift detected"
        
        severity = drift_results.get('severity', 0)
        drift_types = drift_results.get('drift_types', [])
        
        # Critical drift types that require immediate retraining
        critical_drifts = ['performance_drift', 'coherence_drift']
        has_critical_drift = any(dt in critical_drifts for dt in drift_types)
        
        if has_critical_drift:
            return True, f"Critical drift detected: {', '.join(drift_types)}"
        
        if severity >= 0.7:
            return True, f"High severity drift: {severity:.2f}"
        
        if len(drift_types) >= 3:
            return True, f"Multiple drift types: {', '.join(drift_types)}"
        
        return False, f"Drift severity below threshold: {severity:.2f}"
    
    def trigger_retraining(self, 
                          training_data_query: str,
                          model_config: Dict) -> Dict:
        """
        Trigger automated model retraining.
        
        Args:
            training_data_query: SQL query to fetch training data
            model_config: Configuration for new model training
            
        Returns:
            Retraining results
        """
        
        retraining_result = {
            'timestamp': datetime.now().isoformat(),
            'status': 'started',
            'model_config': model_config,
            'training_data_query': training_data_query
        }
        
        try:
            logger.info("🔄 Starting automated model retraining...")
            
            # This would integrate with your model training pipeline
            # For now, we'll outline the process
            
            steps = [
                "fetch_training_data",
                "preprocess_data", 
                "train_model",
                "validate_model",
                "deploy_model",
                "update_baseline"
            ]
            
            for step in steps:
                logger.info(f"📋 Executing step: {step}")
                # Implement actual retraining logic here
                
            retraining_result['status'] = 'completed'
            retraining_result['new_model_path'] = f"models/lda_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            
            self.retraining_history.append(retraining_result)
            
            logger.info("✅ Automated retraining completed successfully")
            return retraining_result
            
        except Exception as e:
            logger.error(f"❌ Automated retraining failed: {e}")
            retraining_result['status'] = 'failed'
            retraining_result['error'] = str(e)
            return retraining_result

# Example usage
def main():
    """Example of how to use the drift detection system."""
    
    # Initialize drift detector
    detector = ModelDriftDetector(
        confidence_threshold=0.05,
        topic_shift_threshold=0.15,
        vocabulary_drift_threshold=0.20
    )
    
    # Load historical data (example)
    # historical_articles = pd.read_csv('data/historical_articles.csv')
    # historical_classifications = pd.read_csv('data/historical_classifications.csv')
    
    # Establish baseline
    # baseline = detector.establish_baseline(
    #     historical_articles, 
    #     historical_classifications,
    #     'models/lda_model.pkl'
    # )
    
    # Detect drift on recent data
    # recent_articles = pd.read_csv('data/recent_articles.csv')
    # recent_classifications = pd.read_csv('data/recent_classifications.csv')
    
    # drift_results = detector.detect_drift(
    #     recent_articles,
    #     recent_classifications,
    #     'models/lda_model.pkl'
    # )
    
    # Check if retraining is needed
    # retraining_system = AutoRetrainingSystem(detector)
    # should_retrain, reason = retraining_system.should_retrain(drift_results)
    
    # if should_retrain:
    #     print(f"🔄 Retraining recommended: {reason}")
    #     # Trigger retraining
    # else:
    #     print(f"✅ No retraining needed: {reason}")

if __name__ == "__main__":
    main()
