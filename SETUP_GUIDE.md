# 🚀 Setup Guide for Pre-trained LDA Model

**Quick setup guide for using your existing .pkl LDA model with real-time news classification**

## 📋 **Prerequisites**

- ✅ Python 3.8 or higher
- ✅ Trained LDA model in `.pkl` format
- ✅ NewsAPI key (free from [newsapi.org](https://newsapi.org/))

## 🧠 **Step 1: Place Your LDA Model**

### **Required Location**
```bash
models/
└── lda_model.pkl    # ← Place your trained model here
```

### **Model Format Requirements**
Your `.pkl` file must contain a dictionary with these keys:

```python
{
    'model': your_trained_lda_model,     # Scikit-learn LDA model
    'vectorizer': your_fitted_vectorizer, # CountVectorizer used for training
    'topics': {                          # Topic information
        0: {
            'label': 'Technology_AI',
            'keywords': ['artificial', 'intelligence', 'machine', 'learning', 'technology']
        },
        1: {
            'label': 'Politics_Election',
            'keywords': ['election', 'political', 'voting', 'democracy', 'campaign']
        },
        # ... more topics
    },
    'method': 'sklearn'                  # Model framework
}
```

### **Validate Your Model**
```bash
# Test if your model is properly formatted
python validate_model.py

# Or test a specific file
python validate_model.py path/to/your/model.pkl
```

## ⚙️ **Step 2: Configuration**

### **API Key Setup**
```bash
# 1. Copy environment template
cp config/.env.example config/.env

# 2. Edit config/.env and add your NewsAPI key
NEWS_API_KEY=your_actual_api_key_here
```

### **Customize Keywords** (Optional)
Edit `config/pipeline.yaml` to change news collection keywords:

```yaml
news_api:
  query_keywords: 
    - "your custom keywords"
    - "specific topics"
    - "industry terms"
```

## 📦 **Step 3: Installation**

```bash
# Install required packages
pip install -r requirements.txt

# Download NLTK data
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"

# Install spaCy English model
python -m spacy download en_core_web_sm
```

## 🚀 **Step 4: Start Real-Time Service**

```bash
# Start the hourly collection and classification service
python start_service.py start

# The service will:
# ✅ Collect news every hour
# ✅ Classify articles using your .pkl model
# ✅ Export data for Power BI dashboard
```

## 📊 **Step 5: Power BI Integration**

### **Data Files Generated**
The service creates these files for your Power BI dashboard:

1. **`data/dashboard/hourly_trends.csv`** - Hourly topic trends
2. **`data/dashboard/realtime_articles.csv`** - Latest classified articles  
3. **`data/dashboard/topic_summary.csv`** - Topic statistics

### **Power BI Setup**
1. Open Power BI Desktop
2. Import the CSV files from `data/dashboard/`
3. Create visualizations:
   - Line chart: Topic trends over time
   - Pie chart: Current topic distribution
   - Table: Recent articles with topics
4. Set auto-refresh every hour

## 🔍 **Step 6: Monitor & Verify**

### **Check Service Status**
```bash
# View service status
python start_service.py status

# View logs
tail -f logs/realtime_service.log
```

### **Verify Data Collection**
```bash
# Check hourly collections
ls -la data/hourly/

# Check classified results
ls -la data/processed/

# Check Power BI exports
ls -la data/dashboard/
```

## 🛠️ **Troubleshooting**

### **Model Issues**
```bash
# Problem: Model not found
# Solution: Ensure .pkl file is in models/lda_model.pkl

# Problem: Model loading error
# Solution: Run validation script
python validate_model.py
```

### **No Articles Collected**
```bash
# Problem: No news articles
# Solution: Check API key and keywords in config/pipeline.yaml
```

### **Classification Fails**
```bash
# Problem: Classification errors
# Solution: Verify model was trained on similar text data
```

## 📈 **Expected Output**

### **Hourly Trends CSV**
```csv
hour,topic_id,topic_label,article_count,avg_confidence
2024-01-15 14:00:00,0,Technology_AI,12,0.85
2024-01-15 14:00:00,1,Politics_Election,8,0.78
```

### **Real-time Articles CSV**
```csv
title,source,topic_label,topic_confidence,published_at
"AI Breakthrough in Healthcare",TechNews,Technology_AI,0.92,2024-01-15 14:30:00
"Election Results Analysis",NewsSource,Politics_Election,0.87,2024-01-15 14:25:00
```

## 🎯 **Success Checklist**

- ✅ Model file placed in `models/lda_model.pkl`
- ✅ Model validation passes
- ✅ NewsAPI key configured
- ✅ Dependencies installed
- ✅ Service starts without errors
- ✅ Hourly data collection working
- ✅ Articles being classified
- ✅ Power BI files generated

## 📞 **Need Help?**

1. **Model Issues**: Run `python validate_model.py`
2. **Service Issues**: Check `logs/realtime_service.log`
3. **API Issues**: Verify NewsAPI key and limits
4. **Data Issues**: Check file permissions and disk space

---

**🎉 You're ready for real-time topic analysis with your trained LDA model!**

**Next**: Start the service with `python start_service.py start` and watch your Power BI dashboard update hourly with topic trends!
