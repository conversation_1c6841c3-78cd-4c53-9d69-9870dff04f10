# 🚀 **Complete Setup Guide: NewsAPI to SQL Server**

## 📋 **Prerequisites**
- ✅ Python 3.8+ installed
- ✅ SQL Server running (local or remote)
- ✅ NewsAPI account (free at newsapi.org)

## 🔑 **Step 1: Get NewsAPI Key**

1. **Sign up at NewsAPI.org**
   ```
   https://newsapi.org/register
   ```

2. **Get your API key from dashboard**
   ```
   https://newsapi.org/account
   ```

3. **Note your key** (looks like: `a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`)

## ⚙️ **Step 2: Configure API Key**

### **Option A: Environment Variable (Recommended)**
```bash
# Windows
set NEWS_API_KEY=your_actual_api_key_here

# Linux/Mac
export NEWS_API_KEY=your_actual_api_key_here
```

### **Option B: .env File**
```bash
# 1. Copy template
cp .env.example .env

# 2. Edit .env file
NEWS_API_KEY=your_actual_api_key_here
```

### **Option C: Direct in Code (Not Recommended)**
Edit `config/pipeline.yaml`:
```yaml
news_api:
  api_key: "your_actual_api_key_here"  # Replace this line
```

## 🗄️ **Step 3: Configure SQL Server**

Edit `config/database.json`:
```json
{
  "server": "localhost",              # Your SQL Server
  "database": "TopicModelingDB",      # Your database name
  "username": null,                   # Username (null for Windows auth)
  "password": null,                   # Password (null for Windows auth)
  "trusted_connection": true,         # Use Windows authentication
  "driver": "ODBC Driver 17 for SQL Server"
}
```

## 🏗️ **Step 4: Setup Database**

```bash
# 1. Create database in SQL Server
sqlcmd -S localhost -Q "CREATE DATABASE TopicModelingDB"

# 2. Create tables and views
sqlcmd -S localhost -d TopicModelingDB -i database/schema.sql
sqlcmd -S localhost -d TopicModelingDB -i database/powerbi_views.sql
```

## 🧠 **Step 5: Add Your LDA Model**

```bash
# Place your trained model in the models directory
cp your_lda_model.pkl models/lda_model.pkl
```

## 🔄 **Step 6: Test the Pipeline**

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Run setup validation
python setup.py

# 3. Test hourly collection (demo)
python demo_hourly_collection.py

# 4. Run actual pipeline
python scripts/run_pipeline.py run --use-database
```

## ⏰ **Step 7: Setup Automatic Hourly Collection**

### **Windows Task Scheduler:**
```bash
# 1. Open Task Scheduler
# 2. Import scheduler/windows_task.xml
# 3. Modify paths to match your setup
# 4. Enable the task
```

### **Linux/Mac Cron:**
```bash
# 1. Edit crontab
crontab -e

# 2. Add this line (runs every hour at 5 minutes past)
5 * * * * cd /path/to/topic_modelling && python scripts/run_pipeline.py run --use-database

# 3. Save and exit
```

## 📊 **Step 8: Connect Power BI**

Follow the guide in `powerbi/DIRECTQUERY_SETUP.md`

## 🔍 **How Hourly Collection Works**

### **Timeline Example:**
```
12:05 PM - Pipeline runs, collects articles from 11:00-12:00
01:05 PM - Pipeline runs, collects articles from 12:00-13:00
02:05 PM - Pipeline runs, collects articles from 13:00-14:00
...and so on every hour
```

### **Data Flow:**
```
NewsAPI (last hour) → Text Preprocessing → LDA Classification → SQL Server → Power BI
```

### **SQL Tables Updated:**
- `articles` - Raw article data
- `article_topics` - Topic classifications
- `topic_trends_daily` - Aggregated trends

## 🧪 **Testing Commands**

```bash
# Test NewsAPI connection
python -c "
import os
from scripts.fetch_news import NewsDataIngestion
api_key = os.getenv('NEWS_API_KEY')
ingestion = NewsDataIngestion(api_key)
articles = ingestion.fetch_articles(query='technology', max_pages=1)
print(f'Fetched {len(articles)} articles')
"

# Test SQL connection
python -c "
from database.sql_connection import SQLConnectionManager
manager = SQLConnectionManager.from_config_file('config/database.json')
with manager.get_connection() as conn:
    result = conn.execute('SELECT 1 as test')
    print('SQL connection successful')
"

# Test complete pipeline
python scripts/run_pipeline.py run --use-database --hours-back 2
```

## 📈 **Monitoring**

```bash
# Check pipeline status
python monitor_pipeline.py check

# View logs
tail -f logs/pipeline.log

# Check SQL data
sqlcmd -S localhost -d TopicModelingDB -Q "
SELECT COUNT(*) as total_articles FROM articles;
SELECT COUNT(*) as classified_articles FROM article_topics;
"
```

## 🚨 **Troubleshooting**

### **NewsAPI Issues:**
- ❌ "Invalid API key" → Check your API key
- ❌ "Rate limit exceeded" → Wait or upgrade plan
- ❌ "No articles found" → Normal during off-peak hours

### **SQL Issues:**
- ❌ "Connection failed" → Check SQL Server running
- ❌ "Database not found" → Create TopicModelingDB
- ❌ "Table not found" → Run schema.sql

### **Model Issues:**
- ❌ "Model not found" → Place lda_model.pkl in models/
- ❌ "Classification failed" → Check model format

## 🎯 **Expected Results**

After setup, you should see:
- ✅ Articles collected every hour
- ✅ Text preprocessing applied
- ✅ Topic classifications generated
- ✅ Data stored in SQL Server
- ✅ Power BI dashboard updating automatically

## 📞 **Support**

If you encounter issues:
1. Check logs in `logs/pipeline.log`
2. Run `python demo_hourly_collection.py` for diagnostics
3. Verify each component individually using test commands above
